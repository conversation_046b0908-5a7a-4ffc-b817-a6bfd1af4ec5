{"name": "tops-browser", "private": true, "version": "2025.5.2", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "build:test": "vite build --mode test", "build:qa": "vite build --mode qa", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.3.3", "typescript": "~5.7.3", "vite": "^6.3.5"}, "dependencies": {"@sentry/browser": "4.4.2", "axios": "^0.21.4", "bowser": "^2.11.0", "cookies-js": "^1.2.3", "date-fns": "1.29.0", "element-ui": "1.3.5", "fuse.js": "^6.6.2", "gsap": "^3.13.0", "is_js": "^0.9.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.30.1", "numeral": "^2.0.6", "pikaday": "^1.8.2", "portal-vue": "^2.1.7", "vue": "2.7.16", "vue-axios": "^2.1.5", "vue-meta": "^1.6.0", "vue-router": "^2.8.1", "vue2-daterange-picker": "^0.6.8", "vuedraggable": "2.24.3", "vuex": "^2.5.0", "vuex-persistedstate": "^2.7.1"}}