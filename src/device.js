export default class Device {
  constructor () {
    this.id = null;

    this.setId();
  }

  setId () {
    let id = window.localStorage.getItem('device.id');

    if (!id) {
      id = this.makeHash();

      window.localStorage.setItem('device.id', id);
    }

    this.id = id;
  }

  makeHash (length = 20) {
    let result = '';
    let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let charactersLength = characters.length;

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
  }
}
