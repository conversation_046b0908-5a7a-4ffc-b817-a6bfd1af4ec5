import is from 'is_js';
import numeral from 'numeral';

import parse from 'date-fns/parse';
import format from 'date-fns/format';
import distanceInWords from 'date-fns/distance_in_words';

import forEach from 'lodash/forEach';
import isEmpty from 'lodash/isEmpty';
import toNumber from 'lodash/toNumber';
import isString from 'lodash/isString';
import toString from 'lodash/toString';

export function towSale (value) {
  return value === '1' ? 'Tow' : 'Sale';
};

export function json (value) {
  if (isEmpty(value)) return value;

  return JSON.stringify(value, undefined, 2);
};

export function relativeDate (value) {
  if (isEmpty(value)) return value;

  return distanceInWords(new Date(), value);
};

export function verbalDate (value) {
  if (isEmpty(value)) return value;

  return format(value, 'MMMM D, YYYY');
};

export function verbalTime (value) {
  if (isEmpty(value)) return value;

  return format(value, 'h:mm A');
};

export function simpleDate (value) {
  if (isEmpty(value)) return value;

  return format(value, 'MM/DD/YYYY');
};

export function simpleDateTime (value) {
  if (isEmpty(value)) return value;

  return format(value, 'M/D/YY h:mma');
};

export function fullDateTime (value) {
  if (isEmpty(value)) return value;

  return format(value, 'M/D/YYYY HH:mm:ss');
};

export function storifyDate (value) {
  if (isEmpty(value)) return value;

  if (isString(value)) value = parse(value);

  value = format(value, 'YYYY-MM-DD HH:mm:ss');

  return value;
};

export function affirmative (value) {
  if (is.inArray(toString(value), ['1', 'true'])) return 'Yes';

  return 'No';
};

export function wholeNumber (value, length = 0) {
  return toNumber(value).toFixed(length);
};

export function usd (value, language = 'en-US') {
  return numeral(value).format('$0,0.00');
};

export function prepareApiData (data) {
  forEach(data, (value, key) => {
    if (is.array(value) || is.object(value)) {   // Item is an object or array so go deep
      if (is.date(value)) {
        data[key] = storifyDate(value);
      } else {
        prepareApiData(value);
      }
    } else {
      if (key.length > 2) {
        let firstCharacter = key[0];
        if (firstCharacter === 'd' && value !== '') {
          data[key] = storifyDate(value);
        }
      }
    }
  });
  return data;
};

// All values come from the API as strings. Convert them to booleans/numbers/etc as needed based on their data types
export function castDataTypes (data) {
  forEach(data, (value, key) => {
    if (is.string(value)) {
      if (key.length > 2 && value !== '') {
        if ([
          'scInitialRate',
          'scSecondaryRate',
          'scTertiaryRate'
        ].includes(key)) {
          // data[key] = (Number(value)).toPrecision(2);
        } else if ([
          'bOwnerWithVehicle'
        ].includes(key)) {
          data[key] = Number(value);
        } else {
          let firstCharacter = key[0];
          let secondCharacter = key[1];

          if (firstCharacter === 'b') {
            if (value === 'true' || value === '1') {
              data[key] = true;
            } else {
              data[key] = false;
            }
          } else if (firstCharacter === 'c' && secondCharacter === 'h') {
            // Leave 'ch*' as string
          } else if (firstCharacter === 'g' && secondCharacter === 'c') {
            data[key] = toNumber(value);
          } else if (firstCharacter === 'j') {
            if (value.length) {
              data[key] = JSON.parse(value);
            }
          } else if (
            firstCharacter === 'p' ||
            firstCharacter === 't' ||
            firstCharacter === 'c' ||
            firstCharacter === 'f' ||
            firstCharacter === 'l' ||
            firstCharacter === 'i') {
            data[key] = toNumber(value);
          }
        }
      }
    } else if (is.array(value) || is.object(value)) {   // Item is an object or array so go deep
      castDataTypes(value);
    }
  });

  return data;
};
