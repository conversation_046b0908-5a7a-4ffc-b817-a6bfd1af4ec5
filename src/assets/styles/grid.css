.data-grid {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: var(--titlebar-height) 1fr;
  grid-template-areas:
    "title-bar"
    "data-grid";
  width: 100%;
  height: 100%;

  > .title-bar {
    grid-area: title-bar;
    height: var(--titlebar-height);
  }

  > .viewport {
    grid-area: data-grid;

    display: grid;

    padding-bottom: 6rem;
    min-width: 100%;
    overflow: auto;
    color: var(--body-fg);
    background: var(--body-bg);
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
    user-select: none;

    ._liner {
      display: flex;
    }

    ._floating-tools {
      position: absolute;
      right: 1rem;
      bottom: 1rem;

      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;

      pointer-events: none;

      > .pill {
        display: flex;
        gap: 0.25rem;

        width: min-content;
        background-color: var(--header-background);
        backdrop-filter: var(--blur);
        padding: 0.5rem;
        border-radius: 10rem;
        box-shadow: var(--box-shadow);

        > .divider {
          height: 100%;
          width: 0;
          border-right: 1px solid var(--input-border);
        }

        > .button {
          border-radius: 10rem;
          pointer-events: initial;
        }
      }
    }

    .-column {
      position: relative;

      width: var(--width);
      min-height: 100%;
      height: max-content;

      &.-action {
        .-button {
          border: none;
          color: var(--body-fg);
          background: none;
        }

        .-error {
          position: relative;
          color: var(--danger);

          .-icon {
            position: relative;
          }

          .-detail {
            position: absolute;
            left: 0;
            top: 0;

            visibility: hidden;
            text-align: left;
            padding: 1em 1em 1em 3em;
            color: var(--body-fg);
            background: var(--body-bg);
            border: 1px solid var(--body-border);
            border-radius: var(--border-radius-100);
            box-shadow: var(--box-shadow-100);
            z-index: 10;
          }

          &:hover {
            .-icon {
              z-index: 11;
            }

            .-detail {
              visibility: visible;
            }
          }
        }
      }

      &.-flexer {
        width: 100%;
      }
    }

    .-row {
      --even-color: var(--pure-black);
      --even-background: var(--white);
      --odd-background: color-mix(in oklch, var(--concrete), var(--white) 50%);

      --even-selected-color: color-mix(in oklch, var(--even-selected-background), var(--pure-black) 70%);
      --even-selected-background: color-mix(in oklch, var(--even-background), var(--pure-blue) 20%);
      --odd-selected-background: color-mix(in oklch, var(--odd-background), var(--pure-blue) 20%);

      --even-flagged-color: color-mix(in oklch, var(--even-flagged-background), var(--pure-black) 70%);
      --even-flagged-background: color-mix(in oklch, var(--white), var(--pure-red) 20%);
      --odd-flagged-background: color-mix(in oklch, var(--odd-background), var(--pure-red) 20%);

      --even-flagged-selected-color: color-mix(in oklch, var(--even-flagged-selected-background), var(--pure-black) 70%);
      --even-flagged-selected-background: color-mix(in oklch, var(--even-flagged-background), var(--pure-blue) 20%);
      --odd-flagged-selected-background: color-mix(in oklch, var(--odd-flagged-background), var(--pure-blue) 20%);

      --header-background: color-mix(in oklch, var(--white), transparent 20%);

      display: flex;
      justify-content: left;
      align-items: center;

      height: 2.5rem;
      padding: 0 0.5em;
      color: var(--even-color);
      background-color: var(--even-background);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.-allow-overflow {
        overflow: visible;
      }

      &:nth-child(odd):not(.-header) {
        background-color: var(--odd-background);
      }

      &.-ghost {
        color: var(--body-fg-a4);
      }

      &.-highlighted {
        color: var(--even-selected-color);
        background-color: var(--even-selected-background);

        &:nth-child(odd):not(.-header) {
          background-color: var(--odd-selected-background);
        }
      }

      &:is(.-internet, .-hold) {
        color: var(--even-flagged-color);
        background-color: var(--even-flagged-background);

        &:nth-child(odd):not(.-header) {
          background-color: var(--odd-flagged-background);
        }

        &.-highlighted {
          color: var(--even-flagged-selected-color);
          background-color: var(--even-flagged-selected-background);

          &:nth-child(odd):not(.-header) {
            background-color: var(--odd-flagged-selected-background);
          }
        }
      }

      .-cell-tag {
        padding: 0 .75em;
        border-radius: 50px;
      }
    }

    .-header {
      position: sticky;
      left: 0;
      top: 0;
      width: 100%;
      font-weight: bold;
      background: var(--header-background);
      backdrop-filter: var(--blur);
      z-index: 2;
      box-shadow:
        0 1px 0 color-mix(in oklch, var(--pure-black), transparent 99%),
        0 2px 0 color-mix(in oklch, var(--pure-black), transparent 99%),
        0 3px 0 color-mix(in oklch, var(--pure-black), transparent 99%),
        0 4px 0 color-mix(in oklch, var(--pure-black), transparent 99%),
        0 5px 0 color-mix(in oklch, var(--pure-black), transparent 99%);

      .-sort {
        color: color-mix(in oklch, var(--pure-black), transparent 40%);
        margin-right: .5em;
      }

      .-resizer {
        position: absolute;
        top: 0;
        right: 0;
        width: 5px;
        height: 100%;
        line-height: 100%;
        border: 2px dotted var(--pure-aqua);
        border-top: none;
        border-bottom: none;
        cursor: ew-resize;
        opacity: 0;
        transition: all .2s .4s;
      }

      &:hover {
        .-resizer {
          opacity: 1;
        }
      }
    }
  }

  > .loadport {
    display: flex;
    justify-content: center;
    align-items: center;

    ._indicator {
      width: 15rem;
    }
  }
}
