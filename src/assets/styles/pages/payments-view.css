#payments-view {
  position: relative;

  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-rows: var(--titlebar-height) 1fr;
  grid-template-areas:
    "customers main"
    "payments  main";

  width: 100%;
  height: 100%;

  #customers-section {
    grid-area: customers;

    display: flex;
    gap: 0.5rem;

    .customer-control {
      flex: 1;
    }

    .local-settings-button {
      margin: 0.5rem;
      height: calc(100% - 1rem);
    }
  }

  #local-settings {
    header {
      color: var(--blue);
      margin-bottom: 1rem;
    }

    label {
      display: grid;
      grid-template-columns: 2rem 1fr;
      gap: 0.5rem;
      align-items: center;
    }
  }

  #payments-section {
    grid-area: payments;

    overflow-y: auto;

    .payments {
      padding: 0;
      margin: 0;
      border-right: 1px solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
    }

    .payment {
      display: grid;
      grid-template-columns: 1fr max-content;
      grid-template-rows: 1fr;
      grid-template-areas:
        "type     actions"
        "balance  actions";

      padding: 0.5rem 1rem;
      border-bottom: 1px solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);

      &:last-of-type {
        border-bottom: none;
      }

      &:hover {
        ._actions {
          opacity: 1;
        }
      }

      &[data-selected] {
        background-color: var(--selected-background);
      }

      &[data-deleted] {
        opacity: 0.5;
      }

      ._balance {
        font-weight: bold;

        ._amount {
          color: var(--dark-blue);
          opacity: 0.3;
        }
      }

      &[data-positive-balance] {
        ._balance {
          color: var(--pure-blue);
        }
      }

      ._received {
        grid-area: received;

        opacity: 0.6;
      }

      ._type {
        grid-area: type;

        width: max-content;
      }

      ._actions {
        grid-area: actions;
        justify-self: right;
        align-self: center;

        opacity: 0.3;
        transition: opacity 0.2s linear;
      }

      ._filter {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        padding: 0 0.75rem;
        border-radius: 1rem;
        cursor: pointer;

        &[data-active] {
          font-weight: bold;
          color: var(--pure-blue);
          background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
        }
      }

      &[data-controls] {
        position: sticky;
        left: 0;
        top: 0;

        display: flex;
        justify-content: space-between;
        align-items: center;

        padding: 0.5rem;
        margin: 0 0.5rem;
        font-weight: bold;
        background-color: white;
        border: 1px solid hsla(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 50%), 1);
        border-radius: 2rem;
        box-shadow: var(--box-shadow-100);
        z-index: var(--layer-modals);
      }

      &[data-gradient] {
        position: sticky;
        left: 6rem;
        bottom: -1px;

        width: calc(300px - 1px);
        height: 7rem;
        background: linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0.75) 100%);
        pointer-events: none;
      }
    }

    ._add {
      position: relative;

      display: grid;

      width: min-content;
      background-color: white;
      border-radius: 2rem;
      z-index: 100;

      & > * {
        grid-area: 1 / 1;

        border-radius: 2rem;
        font-weight: bold;
      }

      select {
        opacity: 0;
      }
    }
  }

  #calls-section {
    grid-area: main;

    width: 100%;
    overflow-y: scroll;

    ._liner {
      height: 100%;

      .distribute {
        padding: 0.2rem;
        width: 11.5rem;
        background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
        border: none;
        border-radius: 0.25rem;
      }

      #application-controls {
        display: flex;
        gap: 0.1rem;

        padding: 0.2rem;
        background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
        border-radius: 0.25rem;

        > * {
          width: 3rem;
          background-color: transparent;
          border: none;
        }

        ._amount {
          width: 5rem;
          background-color: white;
          border-radius: 0.2rem;

          &[data-active] {
            font-weight: bold;
            color: var(--pure-blue);
          }
        }

        ._short {
          position: relative;

          display: flex;
          justify-content: center;
          align-items: center;

          &[data-active] {
            color: var(--pure-red);
            background-color: hsla(var(--pure-red-h), var(--pure-red-s), calc(var(--pure-red-l) + 33%), 1);
            border-top-right-radius: 0.2rem;
            border-bottom-right-radius: 0.2rem;
          }

          &[data-disabled] {
            opacity: 0.3;
          }

          input {
            position: absolute;

            display: none;
          }
        }
      }
    }
  }

  #application-panel {
    --offset: calc(300px + 7rem);

    position: fixed;
    left: var(--offset);
    bottom: 1rem;

    display: flex;
    align-items: center;
    gap: 1rem;

    width: calc(100% - var(--offset) - 1rem);
    padding: 0.5rem 1rem;
    background:
      linear-gradient(180deg, white 55%, white) padding-box,
      linear-gradient(45deg,
        color-mix(in srgb, var(--pure-green), white 80%),
        color-mix(in srgb, var(--pure-blue), white 80%)
      ) border-box;
    border: 2px solid transparent;
    box-shadow: var(--box-shadow-100);
    border-radius: 1rem;

    > * {
      flex: 1;
    }
  }
}
