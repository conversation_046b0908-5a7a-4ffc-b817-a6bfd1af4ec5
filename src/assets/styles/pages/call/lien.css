#lien-section {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 3rem 1fr;
  grid-template-areas:
    "tabs"
    "section";

  .-global-note {
    display: grid;
    grid-template-columns: max-content 1fr;
    grid-template-rows: auto;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid var(--body-border);

    > .-icon {
      color: var(--information);
      margin-top: 0.4em;
    }
  }

  .-tabs {
    grid-area: tabs;
    display: flex;
    border-bottom: 1px solid var(--body-border);

    .-tab {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background: var(--placeholder-bg);
      user-select: none;
      cursor: pointer;

      &.-focused {
        color: var(--pure-blue);
        background: var(--pure-white);
      }

      & + .-tab {
        border-left: 1px solid var(--body-border);
      }
    }
  }

  .-section {
    grid-area: section;
    overflow-y: scroll;
  }

  .-process {
    > .-actions {
      padding: 1rem;

      > .-tools {
        display: flex;

        > * {
          margin-right: .5rem;
        }

        .-input {
          width: 33%;
        }
      }
    }
  }

  .-steps {
    --thumbnail-columns: 3;

    >._step {
      border-bottom: 1px solid var(--input-border);

      &:last-of-type {
        border-bottom: 0;
      }
    }

    ._thumbnail {
      ._required {
        color: var(--danger);
        font-size: var(--font-size-small1);
      }

      ._opened-at {
        grid-area: 2 / 1;
      }

      ._closed-at {
        grid-area: 2 / 2;
      }

      ._activated-at {
        grid-area: 2 / 3;
      }
    }

    ._actions {
      padding: 1rem;
      background: var(--body-bg);
      border-bottom: 1px solid var(--input-border);

      .-hold {
        display: grid;
        grid-template-columns: 2fr 2fr 1fr;
        grid-gap: .5rem;
        align-items: end;
      }

      .-undo,
      .-complete {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .-tasks {
      > .-label {
        font-size: var(--font-size-h4);
        font-weight: bold;
        padding: 1rem 1rem 0 1rem;
      }

      .-task {
        padding: 0.5rem 1rem;

        &:first-of-type {
          padding-top: 1rem;
        }

        &[data-expanded] {
          background-color: var(--selected-background);
        }

        > .-view {
          display: grid;
          grid-template-columns: 1fr 1fr min-content;
          grid-template-rows: auto;
          grid-template-areas:
            "name name action"
            "type copies action"
            "description description action"
            "prerequisites prerequisites action";
          gap: 0.5rem;

          .-name {
            grid-area: name;
            font-weight: bold;
          }

          .-description {
            grid-area: description;
            font-size: var(--font-size-small1);
          }

          .-prerequisites {
            grid-area: prerequisites;
          }

          .-type {
            grid-area: type;
          }

          .-copies {
            grid-area: copies;
          }

          .-action {
            grid-area: action;

            &.-toggle {
              visibility: hidden;

              &[data-visible] {
                visibility: visible;
              }
            }

            &.-process {
              align-self: end;
            }
          }
        }

        > .-prerequisites {
          display: grid;
          grid-template-columns: 1fr min-content min-content;
          grid-template-rows: auto;
          gap: 1rem;
        }
      }
    }
  }

  .-owners {
    --thumbnail-columns: 2;
  }

  .-letters {
    --thumbnail-columns: 3;

    ._faux-input {
      margin-top: 0.4rem;

      ._value {
        margin-top: 0.4rem;
      }
    }
  }

  .-steps,
  .-owners,
  .-letters {
    ._thumbnail {
      display: grid;
      grid-template-columns: repeat(var(--thumbnail-columns), 1fr);
      gap: 1rem;
    }

    > ._actions {
      padding: 1rem;

      .-add {
        position: relative;
        margin-right: .5rem;

        ._options {
          position: absolute;
          bottom: 2rem;
          left: 0;
          width: 200px;
          max-height: 50vh;
          color: var(--body-fg);
          background: var(--body-bg);
          border: 1px solid var(--body-border);
          border-radius: var(--border-radius-100);
          box-shadow: var(--box-shadow-100);
          overflow-y: scroll;
        }

        ._option {
          text-align: left;
          padding: .5rem 1rem;
          cursor: pointer;
        }
      }
    }
  }
}
