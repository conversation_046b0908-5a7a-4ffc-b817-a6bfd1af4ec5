#prices-view {
  position: relative;

  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-rows: var(--titlebar-height) min-content 1fr;
  grid-template-areas:
    "tabs     title-bar"
    "filters  sketch"
    "filters  data";

  width: 100%;
  height: 100%;

  dialog {
    max-width: 50rem;
  }

  .tabs-section {
    grid-area: tabs;

    display: grid;
    place-content: center;

    padding: 0.5rem;
  }

  > .title-bar {
    grid-area: title-bar;

    background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) - 10%));
  }

  .filters-section {
    position: relative;

    grid-area: filters;

    border-right: 1px solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
    overflow-y: auto;

    .filter {
      position: relative;

      padding: 0.5rem 1rem;
      border-bottom: 1px solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
      cursor: default;

      &:first-of-type {
        border-bottom: none;
      }

      &[data-service] {
        display: flex;
        flex-direction: column;
      }

      &[data-variant="minimal"] {
        .gl-number,
        .tags {
          display: none;
        }
      }

      &[data-variant="compact"] {
        .tags {
          display: none;
        }
      }

      &[data-active] {
        background-color: var(--selected-background);
      }

      &[data-filter] {
        position: sticky;
        top: 0;
        left: 0;

        display: block;

        padding: 0 0.5rem 0.5rem 0.5rem;
        z-index: 5;

        input {
          padding: 0.5rem 1rem;
          background-color: hsla(0, 0%, 100%, 0.8);
          backdrop-filter: var(--blur);
          border: 1px solid hsla(0, 0%, 0%, 0.1);
          border-radius: 50rem;
          box-shadow: var(--box-shadow-50);
        }
      }

      &[data-controls] {
        position: sticky;
        bottom: 0;
        left: 0;

        display: flex;
        justify-content: space-between;
        align-items: center;

        border-bottom: none;

        .layouts {
          display: flex;

          padding: 0.2rem;
          background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
          border-radius: var(--border-radius-200);

          .layout {
            --color: var(--body-fg);
            --background-color: transparent;

            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: repeat(3, 1fr);
            gap: 3px;

            color: var(--color);
            background-color: var(--background-color);
            width: 2.25rem;
            height: 2rem;
            padding: 0.5rem;
            border-radius: 0.25rem;

            &[data-active] {
              --color: var(--pure-blue);
              --background-color: white;
              box-shadow: var(--box-shadow-50);
            }
          }

          .segment {
            border-radius: 2rem;

            &[data-visible] {
              background-color: var(--color);
            }
          }
        }
      }

      &[data-fade] {
        position: fixed;
        left: 6rem;
        bottom: -1px;

        width: calc(300px - 1px);
        height: 7rem;
        background: linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 1) 100%);
        backdrop-filter: blur(0.5rem);
        mask-image: linear-gradient(to bottom, transparent, black);
        pointer-events: none;
      }

      .name {
        font-weight: bold;
      }

      .calculated {
        position: absolute;
        top: 0.75rem;
        right: 1rem;
      }

      .tags {
        display: flex;
        gap: 0.25rem;

        padding-top: 0.5rem;
        padding-bottom: 0.75rem;

        > * {
          padding: 0rem 0.75rem;
          color: var(--pure-blue);
          background-color: hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.1);
          border-radius: 1rem;
        }
      }

      mark {
        position: relative;

        background-color: transparent;

        &::before {
          --padding: -0.1rem;

          position: absolute;
          top: var(--padding);
          right: calc(var(--padding) * 2);
          bottom: var(--padding);
          left: calc(var(--padding) * 2);

          content: "";
          background-color: hsla(var(--pure-yellow-h), var(--pure-yellow-s), var(--pure-yellow-l), 0.3);
          mix-blend-mode: multiply;
          border-radius: 0.25rem;
        }
      }
    }
  }

  .data-placeholder {
    grid-row: title-bar / data;

    display: grid;
    place-items: center;

    background-color: var(--placeholder-bg);

    > * {
      width: 20rem;
      height: min-content;
      background: linear-gradient(90deg,
        hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.7),
        hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.5)
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  #service-sketch {
    grid-area: sketch;

    position: relative;

    display: grid;
    grid-template-columns: repeat(4, 1fr);

    padding: 1rem;
    width: 100%;
    height: 100%;
    color: white;
    background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) - 5%));

    ._column {
      display: flex;
      flex-direction: column;
      gap: 0.2rem;
    }

    button.is-unstyled {
      color: white;
      background-color: hsla(0, 0%, 100%, 0.3);
      border: 0 solid hsla(0, 0%, 100%, 0.7);
      border-radius: 0.2rem;
    }
  }

  .data-section {
    grid-area: data;

    overflow: hidden;
  }

  .service-defaults {
    padding: 2rem;
    background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 55%));

    li {
      padding: 1rem;
      margin-bottom: 1.0rem;
      background-color: white;
      box-shadow: var(--box-shadow-100);
      border-radius: 0.5rem;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}

#record-view[data-page="price"] {
  .structure {
    th,
    td {
      padding: 0.5rem 1rem;
    }

    th {
      font-weight: 400;
    }

    tr {
      border-bottom: 1px solid hsla(0, 0%, 0%, 0.1);

      &:last-of-type {
        border-bottom: none;
      }
    }

    input,
    select {
      width: 100%;
      font-weight: bold;
      border: none;
    }
  }
}
