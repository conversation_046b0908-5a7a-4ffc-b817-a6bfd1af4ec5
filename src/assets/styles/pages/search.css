#search {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 4rem 1fr 4rem;
  grid-template-areas:
    "titlebar"
    "content"
    "footer";

  height: 100vh;
  background-color: var(--body-border);

  > .title-bar {
    grid-area: titlebar;
  }

  > .guide-wrapper {
    overflow: hidden;
  }

  > .guide {
    grid-area: content;
  }

  > .clauses {
    grid-area: content;

    display: grid;
    justify-content: center;
    align-items: center;

    padding-top: 1rem;
    overflow-y: scroll;

    .whitespace-placeholder {
      width: var(--font-size-lead3);
      opacity: 0.2;

      g {
        stroke: var(--body-fg);
      }
    }

    ._clause {
      --offset-x: 0;

      position: relative;

      display: grid;
      grid-template-columns: 5rem min-content 33em min-content min-content;
      grid-template-rows: auto;
      grid-template-areas:
        "and-or open-paren field-value close-paren controls";
      align-items: center;

      padding: 0 1rem;
      margin-bottom: 1rem;
      border-radius: var(--border-radius-100);
      background-color: var(--body-bg);
      transition: box-shadow 0.2s linear;
      will-change: box-shadow;
      transform: translate3d(calc(var(--offset-x) * 2rem), 0, 0);

      &:focus-within {
        box-shadow: 0 0 0 2px var(--selected-background),
          var(--box-shadow-100);
      }

      ._and-or-section {
        grid-area: and-or;

        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
        opacity: 0.5;
        transition: opacity 0.2s linear;
        will-change: opacity;

        &:hover {
          opacity: 1;
        }

        ._and-or,
        ._not {
          width: 100%;
          margin-bottom: 0.5em;
          text-transform: uppercase;
          text-align: right;
          font-size: var(--font-size-small1);
          font-weight: bold;
          letter-spacing: var(--font-letter-spacing);
        }
      }

      ._open-paren {
        grid-area: open-paren;
      }

      ._field-value-section {
        grid-area: field-value;

        display: grid;
        grid-template-columns: 1fr min-content 1fr;
        grid-template-rows: repeat(2, 1fr);
        grid-template-areas:
          "field preset operator"
          "value value value";
        gap: 0.5rem;

        height: max-content;

        ._field {
          grid-area: field;
        }

        ._operator {
          grid-area: operator;
        }

        ._preset {
          grid-area: preset;
          align-self: center;

          display: grid;

          ._trigger {
            grid-area: 1 / 1;

            width: var(--font-size-h4);
            height: var(--font-size-h4);
            border-radius: 50%;
            background: hsla(var(--body-fg-h), var(--body-fg-s), var(--body-fg-l), 0.15);
            pointer-events: none;
            text-align: center;
          }

          select {
            grid-area: 1 / 1;

            appearance: none;
            border: none;
            opacity: 0;
          }
        }

        ._value {
          grid-area: value;

          font-weight: bold;
        }

      }

      ._close-paren {
        grid-area: close-paren;
      }

      ._controls-section {
        grid-area: controls;

        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0.5;
        transition: opacity 0.2s linear;
        will-change: opacity;

        &:hover {
          opacity: 1;
        }

        ._clone {
          margin-bottom: 0.5em;
        }
      }

      ._and-or,
      ._not,
      ._open-paren,
      ._field,
      ._operator,
      ._preset,
      ._value,
      ._close-paren,
      ._clone,
      ._remove {
        appearance: none;
        padding: 0;
        margin: 0;
        color: var(--body-fg);
        background: inherit;
        border: 0;
        outline: 0;
        cursor: pointer;
      }

      ._open-paren,
      ._close-paren {
        font-size: var(--font-size-lead1);
        font-weight: lighter;
        opacity: 0.1;
        visibility: hidden;

        &[data-active] {
          opacity: 1;
        }

        &[data-visible] {
          visibility: visible;
        }
      }

      ._field {
        font-size: var(--font-size-h4);
      }

      ._operator {
        font-size: var(--font-size-h4);
      }

      ._value {
        font-size: var(--font-size-h4);
      }
    }
  }

  > .footer-bar {
    grid-area: footer;
  }
}
