#towpay {
  position: relative;

  padding: 2rem;
  background-image:
    radial-gradient(circle at bottom right,
      hsla(var(--pure-aqua-h), var(--pure-aqua-s), var(--pure-aqua-l), 0.5) 0,
      hsla(var(--pure-aqua-h), var(--pure-aqua-s), var(--pure-aqua-l), 0) 90%
    ),
    radial-gradient(circle at top right,
      hsla(var(--pure-lime-h), var(--pure-lime-s), var(--pure-lime-l), 0.5) 0,
      hsla(var(--pure-lime-h), var(--pure-lime-s), var(--pure-lime-l), 0) 50%
    );

  /* Tabs */

  .towpay-tabs {
    display: flex;

    width: max-content;
    padding: 0.2rem;
    margin: 0 auto 2rem auto;
    background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
    border-radius: 0.5rem;
    user-select: none;
    cursor: default;

    .tab {
      padding: 0.5rem 1rem;

      &[data-active] {
        color: var(--pure-blue);
        background-color: white;
        border-radius: 0.25rem;
      }
    }
  }

  /* Sections */

  #swipe-section,
  #entry-section,
  #link-section {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    justify-content: center;
    align-items: center;

    > * {
      opacity: 0;
      pointer-events: none;

      &[data-show] {
        opacity: 1;
        pointer-events: all;
      }
    }
  }

  #swipe-section {
    --feedback-color: var(--pure-blue);
    --feedback-color-h: var(--pure-blue-h);
    --feedback-color-s: var(--pure-blue-s);
    --feedback-color-l: var(--pure-blue-l);
  }

  #entry-section {
    --feedback-color: var(--pure-green);
    --feedback-color-h: var(--pure-green-h);
    --feedback-color-s: var(--pure-green-s);
    --feedback-color-l: var(--pure-green-l);

    #card-input {
      padding: 1rem;
      margin-bottom: 1rem;
      background: white !important;
      border-radius: 0.5rem;
      box-shadow: var(--box-shadow-100);
    }

    ._payment-number {
      font-size: 1rem;
      user-select: all;
    }
  }

  #link-section {
    --feedback-color: var(--pure-green);
    --feedback-color-h: var(--pure-green-h);
    --feedback-color-s: var(--pure-green-s);
    --feedback-color-l: var(--pure-green-l);
  }

  /* Section panels */

  .input-panel {
    grid-area: 1 / 1;
  }

  .feedback-panel {
    grid-area: 1 / 1;

    display: grid;
    justify-content: center;

    ._card {
      grid-template-areas:
        "icon instruction"
        "actions actions";
      display: grid;
      grid-template-columns: repeat(2, max-content);
      grid-template-rows: repeat(2, min-content);
      gap: 1rem;
      justify-items: center;
      align-items: center;
    }

    ._icon {
      grid-area: icon;
      align-self: start;

      padding: 1.5rem;
      font-size: var(--font-size-h3);
      color: var(--feedback-color);
      background-color: hsla(var(--feedback-color-h), var(--feedback-color-s), var(--feedback-color-l), 0.1);
      border-radius: 50%;
    }

    ._instruction {
      grid-area: instruction;

      font-size: var(--font-size-h3);
      color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) - 10%));
    }

    ._actions {
      grid-area: actions;
    }
  }

  #loader-section {
    display: grid;
    justify-content: center;
    align-items: center;

    height: 20rem;

    .loader-indicator {
      width: 10rem;
    }
  }

  .grid-form {
    margin-bottom: 2rem;
    background: white !important;
    border-radius: 0.5rem;
    box-shadow: var(--box-shadow-100);
  }

  .powered-by {
    position: absolute;
    right: 2rem;
    bottom: 2rem;

    height: var(--font-size-h2);
    opacity: 0.8;
  }
}
