.vue-daterange-picker {
  width: 100%;

  .form-control {
    background-color: transparent !important;
  }

  .reportrange-text {
    border: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .-label {
    margin: 0 !important;
  }

  .button {
    margin: 0 !important;
  }

  .daterangepicker {
    background-color: var(--body-bg);
    box-shadow: var(--box-shadow-200);
    border: 1px solid var(--input-border);

    &::before {
      border-bottom: 7px solid var(--input-border);
    }

    &::after {
      border-bottom: 6px solid var(--body-bg);
    }

    .calendar-table {
      background-color: inherit;
      border: 0;

      .prev > *,
      .next > * {
        border-right: 2px solid var(--body-fg) !important;
        border-bottom: 2px solid var(--body-fg) !important;
      }

      th,
      td {
        color: inherit;
        background-color: inherit;

        &.off {
          color: var(--body-fg-a6) !important;
        }
      }

      .in-range {
        background-color: var(--selected-background);
      }

      .active {
        color: color-mix(in oklch, var(--pure-black), var(--pure-blue) 66%);
      }
    }
  }
}
