.table {
  &.is-striped {
    td {
      border: 0 !important;
    }
  }

  &.is-fixed {
    table-layout: fixed !important;
  }

  thead tr:nth-child(1) th {
    background: var(--body-bg);
    position: sticky;
    top: 0;
    z-index: 10;
  }
}

.is-highlighted {
  background: var(--selected-background) !important;
}

.is-ghost {
  color: var(--body-fg-a4);
}

.show-scrollbars {
  &::-webkit-scrollbar {
    display: normal;
  }
}
