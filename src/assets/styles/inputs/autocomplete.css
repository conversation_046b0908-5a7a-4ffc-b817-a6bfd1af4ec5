.el-autocomplete__suggestions {
  position: absolute;
  top: 35px;
  left: 0;
  width: 100%;
  border-radius: var(--border-radius-100);
  z-index: 5;
  overflow: hidden;
  color: var(--body-fg);
  background: var(--body-bg);
  margin: 5px 0;
  box-shadow: 0 0 6px 0 rgba(0,0,0,0.04), 0 2px 4px 0 rgba(0,0,0,0.12);

  & li {
    list-style: none;
    line-height: 36px;
    padding: 0 10px;
    margin: 0;
    cursor: pointer;
    color: var(--metadata-fg);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: var(--list-hover-bg);
    }

    &.highlighted {
      background-color: var(--selected-background);
      color: var(--body-fg);
    }

    &:active {
      background-color: var(--button-primary-bg-press);
    }

    &.divider {
      margin-top: 6px;
      border-top: 1px solid var(--body-border);
    }

    &.divider:last-child {
      margin-bottom: -6px;
    }
  }
}
