.dropdown {
  position: relative;

  > .dropdown-menu {
    transition: all var(--transition-fast) ease-in-out;

    .dropdown-content {
      max-height: 90vh;
      overflow-y: scroll;
      color: var(--body-fg);
      background: var(--body-bg);
      border: 1px solid var(--input-border);
      box-shadow: var(--box-shadow-100);
    }

    .dropdown-item {
      color: var(--body-fg);
      background: var(--body-bg);

      &:hover {
        background: var(--list-hover-bg);
      }

      &.has-intent {
        background: var(--selected-background);
      }
    }
  }
}
