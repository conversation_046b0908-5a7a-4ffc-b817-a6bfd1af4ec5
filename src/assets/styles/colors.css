:root {
  /* Pure colors */
  --pure-navy: #001f3f;
  --pure-navy-h: 210.5;
  --pure-navy-s: 100%;
  --pure-navy-l: 12.4%;
  --pure-navy-hsl: var(--pure-navy-h), var(--pure-navy-s), var(--pure-navy-l);

  --pure-blue: #0074D9;
  --pure-blue-h: 207.9;
  --pure-blue-s: 100%;
  --pure-blue-l: 42.5%;
  --pure-blue-hsl: var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l);

  --pure-aqua: #7FDBFF;
  --pure-aqua-h: 196.9;
  --pure-aqua-s: 100%;
  --pure-aqua-l: 74.9%;

  --pure-teal: #39CCCC;
  --pure-teal-h: 180;
  --pure-teal-s: 59%;
  --pure-teal-l: 51.2%;

  --pure-olive: #3D9970;
  --pure-olive-h: 153.3;
  --pure-olive-s: 43%;
  --pure-olive-l: 42%;

  --pure-green: #2ECC40;
  --pure-green-hsl: 126.8, 63.2%, 49%;
  --pure-green-h: 126.8;
  --pure-green-s: 63.2%;
  --pure-green-l: 49%;

  --pure-lime: #01FF70;
  --pure-lime-h: 146.2;
  --pure-lime-s: 100%;
  --pure-lime-l: 50.2%;

  --pure-yellow: #FFDC00;
  --pure-yellow-h: 51.8;
  --pure-yellow-s: 100%;
  --pure-yellow-l: 50%;

  --pure-orange: #FF851B;
  --pure-orange-hsl: 27.9, 100%, 55.3%;
  --pure-orange-h: 27.9;
  --pure-orange-s: 100%;
  --pure-orange-l: 55.3%;

  --pure-red: #FF4136;
  --pure-red-h: 3.3;
  --pure-red-s: 100%;
  --pure-red-l: 60.6%;

  --pure-maroon: #85144b;
  --pure-maroon-h: 330.8;
  --pure-maroon-s: 73.9%;
  --pure-maroon-l: 30%;

  --pure-fuchsia: #F012BE;
  --pure-fuchsia-h: 313.5;
  --pure-fuchsia-s: 88.1%;
  --pure-fuchsia-l: 50.6%;

  --pure-purple: #B10DC9;
  --pure-purple-h: 292.3;
  --pure-purple-s: 87.9%;
  --pure-purple-l: 42%;
  --pure-purple-hsl: var(--pure-purple-h), var(--pure-purple-s), var(--pure-purple-l);

  --pure-black: #111111;
  --pure-black-h: 0;
  --pure-black-s: 0%;
  --pure-black-l: 6.7%;

  --pure-gray: #AAAAAA;
  --pure-gray-h: 0;
  --pure-gray-s: 0%;
  --pure-gray-l: 66.7%;

  --pure-silver: #DDDDDD;
  --pure-silver-h: 0;
  --pure-silver-s: 0%;
  --pure-silver-l: 86.7%;

  --pure-white: #FFFFFF;

  /* App colors */
  --san-marino: #3D6999;
  --green: #A8B926;
  --yellow: #DCD000;
  --dove-gray: #636363;
  --concrete: #F2F2F2;
  --white: #FFFFFF;

  --blue: #476895;
  --blue-h: 211.3;
  --blue-s: 43%;
  --blue-l: 42%;
  --blue-hsl: 211.3, 43%, 42%;

  --dark-blue: #25364E;
  --dark-blue-h: 211.3;
  --dark-blue-s: 43%;
  --dark-blue-l: 22%;
  --dark-blue-hsl: var(--dark-blue-h), var(--dark-blue-s), var(--dark-blue-l);

  --orange: #C68A74;
  --orange-h: 14.4;
  --orange-s: 50.5%;
  --orange-l: 62.7%;
  --orange-hsl: var(--orange-h), var(--orange-s), var(--orange-l);

  /* Color roles */
  --selected-background: color-mix(in oklch, var(--white), var(--pure-blue) 20%);

  --primary: var(--san-marino);
  --primary-dark-100: color-mix(in oklch, var(--san-marino), black 5%);
  --primary-dark-200: color-mix(in oklch, var(--san-marino), black 10%);
  --primary-dark-300: color-mix(in oklch, var(--san-marino), black 15%);
  --primary-light-200: color-mix(in oklch, var(--san-marino), white 10%);
  --primary-light-300: color-mix(in oklch, var(--san-marino), white 15%);
  --accent: var(--pure-aqua);

  --success: var(--pure-green);
  --success-darken: color-mix(in oklch, var(--pure-green), var(--pure-black) 3%);
  --success-lighten: color-mix(in oklch, var(--pure-green), var(--pure-white) 3%);
  --warning: var(--pure-orange);
  --information: var(--pure-blue);
  --danger: var(--pure-red);
  --danger-a5: color-mix(in oklch, var(--pure-red), transparent 50%);
  --danger-lighten: color-mix(in oklch, var(--pure-red), white 5%);

  --body-bg: var(--white);
  --body-bg-a6: color-mix(in oklch, var(--white) 60%, transparent);
  --body-border: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 50%));
  --body-fg: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) - 25%)) !important;
  --body-fg-a2: color-mix(in oklch, var(--pure-black), transparent 20%);
  --body-fg-a6: color-mix(in oklch, var(--pure-black), transparent 60%);
  --body-fg-h: 0;
  --body-fg-s: 0%;
  --body-fg-l: 6.7%;
  --body-fg: hsla(var(--body-fg-h), var(--body-fg-s), var(--body-fg-l), 1);

  --titlebar-bg: var(--san-marino);
  --titlebar-fg: var(--white);
  --titlebar-fg-a2: color-mix(in oklch, var(--white) 20%, transparent);
  --titlebar-fg-a5: color-mix(in oklch, var(--white) 40%, transparent);
  --titlebar-fg-accent: var(--pure-black);

  --datepicker-outline: var(--pure-blue);
  --datepicker-fg: var(--pure-black);
  --datepicker-range-bg: var(--concrete);

  --images-bg: color-mix(in oklch, var(--pure-black), transparent 40%);
  --images-accent: var(--pure-yellow);

  --loader: var(--pure-black);

  --divider-bg: color-mix(in oklch, var(--concrete), var(--pure-black) 5%);

  --form-section-border: var(--pure-black);
  --form-match-underline: color-mix(in oklch, var(--pure-black), transparent 40%);

  --metadata-fg: color-mix(in oklch, var(--blue) 60%, transparent);

  --input-border: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 47%));
  --input-button-fg: color-mix(in oklch, var(--pure-black), transparent 60%);
  --input-bg-focus: var(--concrete);

  --placeholder-fg: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 25%));
  --placeholder-bg: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 53%));

  --modal-bg: color-mix(in oklch, var(--dove-gray), transparent 60%);

  --button-primary-bg: var(--pure-blue);
  --button-primary-bg-hover: color-mix(in oklch, var(--pure-blue), white 3%);
  --button-primary-bg-press: color-mix(in oklch, var(--pure-blue), black 3%);

  --list-hover-bg: var(--concrete);

  --tab-group-bg: var(--san-marino);
  --tab-fg: var(--white);
}

/* Color Utilities */
.blue { color: var(--blue); }
.dark-blue { color: var(--dark-blue); }
.orange { color: var(--orange); }

.pure-navy { color: var(--pure-navy); }
.pure-blue { color: var(--pure-blue); }
.pure-aqua { color: var(--pure-aqua); }
.pure-teal { color: var(--pure-teal); }
.pure-olive { color: var(--pure-olive); }
.pure-green { color: var(--pure-green); }
.pure-lime { color: var(--pure-lime); }
.pure-yellow { color: var(--pure-yellow); }
.pure-orange { color: var(--pure-orange); }
.pure-red { color: var(--pure-red); }
.pure-maroon { color: var(--pure-maroon); }
.pure-fuchsia { color: var(--pure-fuchsia); }
.pure-purple { color: var(--pure-purple); }
.pure-black { color: var(--pure-black); }
.pure-gray { color: var(--pure-gray); }
.pure-silver { color: var(--pure-silver); }
.pure-white { color: var(--pure-white); }

.opacity-0 { opacity: 0; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-100 { opacity: 1; }
