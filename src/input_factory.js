class InputFactory {
  model = {
    DRIVER_KEY: {
      label: 'Driver',
      control: 'app-select',
      action: 'TOPSCOMPANY__getDrivers',
      value: -1,
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    },
    TRUCK_KEY: {
      label: 'Truck',
      control: 'app-select',
      action: 'TOPSCOMPANY__getTrucks',
      value: -1,
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    },
    LOT_KEY: {
      label: 'Lot',
      control: 'app-select',
      action: 'TOPSCOMPANY__getLots',
      value: -1,
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    },
    LOAD_NUMBER: {
      label: 'Load Number',
      control: 'app-text',
      isVisible: true
    }
  };

  hydrate (inputs) {
    return inputs;
  }
}

export default InputFactory;
