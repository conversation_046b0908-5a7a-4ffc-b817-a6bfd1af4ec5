import Requester from './Requester';

class CSVFileRequest extends Requester {
  constructor (context, options) {
    super(context, options);

    this.afterSuccessCallback = () => {
      let hiddenElement = document.createElement('a');

      hiddenElement.href = 'data:attachment/csv,' + encodeURIComponent(this.response);
      hiddenElement.target = '_blank';
      hiddenElement.download = 'Results.csv';
      hiddenElement.click();
    };

    if (!this.isLazy) {
      this.make();
    }
  }
};

export default CSVFileRequest;
