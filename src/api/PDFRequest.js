import Requester from './Requester';

class PDFRequest extends Requester {
  constructor (context, options) {
    super(context, options);

    this.responseType = 'blob';
    this.afterSuccessCallback = () => {
      let reader = new window.FileReader();

      reader.readAsDataURL(this.response);
      reader.onload = function () {
        let hiddenElement = document.createElement('a');

        hiddenElement.href = reader.result;
        hiddenElement.target = '_blank';
        hiddenElement.download = 'Export.pdf';
        hiddenElement.click();
      };
    };

    if (!this.isLazy) {
      this.make();
    }
  }
};

export default PDFRequest;
