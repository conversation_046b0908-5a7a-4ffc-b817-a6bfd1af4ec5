/**
 * @param {*} s1: string
 * @param {*} s2: string
 * @returns similarityPercentage: number
 */
export function levenshteinDistance (s1, s2) {
  const matrix = [];

  // Initialize the matrix with the distances
  for (let i = 0; i <= s1.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= s2.length; j++) {
    matrix[0][j] = j;
  }

  // Calculate the distances
  for (let i = 1; i <= s1.length; i++) {
    for (let j = 1; j <= s2.length; j++) {
      const cost = (s1[i - 1] === s2[j - 1]) ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,    // deletion
        matrix[i][j - 1] + 1,    // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }

  // Calculate similarity percentage
  const maxLength = Math.max(s1.length, s2.length);
  const distance = matrix[s1.length][s2.length];
  const similarityPercentage = ((maxLength - distance) / maxLength) * 100;

  // Return the similarity percentage
  return similarityPercentage;
}

/**
 * @param {*} value: string
 * @returns value: string
 */
export function newLineToCsv (value) {
  if (!!value && value.includes('\n')) {
    value = value.split('\n');
    value = value.join(',');
    value = value.trim(', ');
    value = value.replace(/,+/g, ',');
  }

  return value;
}
