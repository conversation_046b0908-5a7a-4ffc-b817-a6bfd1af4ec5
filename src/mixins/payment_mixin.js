import {
  EVENT_OPEN_PAYMENTS,
  EVENT_CLOSE_PAYMENTS
} from '../config';

let PaymentMixin = {
  data () {
    return {
      paymentsVisible: false
    };
  },

  methods: {
    $_PaymentMixin_togglePayments (value = !this.$data.paymentsVisible) {
      this.$data.paymentsVisible = value;
    }
  },

  mounted () {
    this.$hub.$on(EVENT_OPEN_PAYMENTS, () => {
      this.$_PaymentMixin_togglePayments();
    });

    this.$hub.$on(EVENT_CLOSE_PAYMENTS, () => {
      this.toggleActions();
    });
  }
};

export default PaymentMixin;
