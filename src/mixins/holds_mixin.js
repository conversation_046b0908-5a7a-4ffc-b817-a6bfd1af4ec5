import {
  EVENT_OPEN_HOLDS,
  EVENT_CLOSE_HOLDS
} from '../config';

let HoldsMixin = {
  data () {
    return {
      holdsVisible: false
    };
  },

  methods: {
    $_HoldsMixin_toggleHolds (value = !this.$data.holdsVisible) {
      this.$data.holdsVisible = value;
    }
  },

  mounted () {
    this.$hub.$on(EVENT_OPEN_HOLDS, () => {
      this.$_HoldsMixin_toggleHolds();
    });

    this.$hub.$on(EVENT_CLOSE_HOLDS, () => {
      this.toggleActions();
    });
  }
};

export default HoldsMixin;
