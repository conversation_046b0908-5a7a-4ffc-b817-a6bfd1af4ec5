export const callMixin = {
  computed: {
    dispatchNotesProxy: {
      get () {
        return this.call.vc255DispatchNotes +
          this.call.vc255DispatchNotes2 +
          this.call.vc255DispatchNotes3 +
          this.call.vc255DispatchNotes4;
      },
      set (value) {
        const chunkSize = 255;
        const chunkOneStart = 0;
        const chunkTwoStart = chunkOneStart + chunkSize;
        const chunkThreeStart = chunkTwoStart + chunkSize;
        const chunkFourStart = chunkThreeStart + chunkSize;

        this.call.vc255DispatchNotes = value.slice(chunkOneStart, chunkOneStart + chunkSize);
        this.call.vc255DispatchNotes2 = value.slice(chunkTwoStart, chunkTwoStart + chunkSize);
        this.call.vc255DispatchNotes3 = value.slice(chunkThreeStart, chunkThreeStart + chunkSize);
        this.call.vc255DispatchNotes4 = value.slice(chunkFourStart, chunkFourStart + chunkSize);
      }
    }
  }
};
