export default {
  data () {
    return {
      eventListeners: [],
      observers: [],
      timers: [],
      subscriptions: []
    };
  },

  methods: {
    // Helper method to safely add event listeners
    addEventListener (event, handler) {
      this.eventListeners.push({ event, handler });
      this.$hub.$on(event, handler);
    },

    // Helper method to safely add observers
    addObserver (observer) {
      this.observers.push(observer);
    },

    // Helper method to safely add timers
    addTimer (timer) {
      this.timers.push(timer);
    },

    // Helper method to safely add subscriptions
    addSubscription (subscription) {
      this.subscriptions.push(subscription);
    },

    // Cleanup method that can be called manually if needed
    cleanup () {
      // Clean up event listeners
      this.eventListeners.forEach(({ event, handler }) => {
        if (this.$hub) {
          this.$hub.$off(event, handler);
        }
      });

      // Clean up observers
      this.observers.forEach(observer => {
        if (observer && observer.disconnect) {
          observer.disconnect();
        }
      });

      // Clean up timers
      this.timers.forEach(timer => {
        if (timer) {
          clearTimeout(timer);
        }
      });

      // Clean up subscriptions
      this.subscriptions.forEach(subscription => {
        if (subscription && typeof subscription.unsubscribe === 'function') {
          subscription.unsubscribe();
        }
      });

      // Clear arrays
      this.eventListeners = [];
      this.observers = [];
      this.timers = [];
      this.subscriptions = [];
    }
  },

  beforeDestroy () {
    this.cleanup();
  }
};
