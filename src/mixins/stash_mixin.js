import { mapGetters, mapActions } from 'vuex';

let StashMixin = {
  data () {
    return {
      stashRaw: {
        id: '', // Set by setStashId()
        data: {} // Set by setStash()
      }
    };
  },

  computed: {
    ...mapGetters(['STASH_get']),

    stash: {
      get () {
        return this.$data.stashRaw.data;
      },
      set (value) {
        if (!this.$data.stashRaw.id) throw new Error('Stash ID is not set.');

        this.$data.stashRaw.data = value;
      }
    }
  },

  methods: {
    ...mapActions(['STASH_set']),

    setStashId (id) {
      this.$data.stashRaw.id = id;
    },

    setStash (data = {}) {
      console.log('*** setStash', data);
      this.$data.stashRaw.data = data;
    }
  },

  mounted () {
    // this.$data.stashRaw = this.STASH_get;
    // this.$data.stashRaw = this.STASH_get(this.$data.stashRaw.id);
  },

  beforeDestroy () {
    this.STASH_set({
      screen: this.$data.stashRaw.id,
      state: this.$data.stashRaw.data
    });
  }
};

export default StashMixin;
