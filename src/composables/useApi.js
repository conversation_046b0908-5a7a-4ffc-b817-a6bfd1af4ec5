import { ref, computed } from '@vue/composition-api';
import StandardRequest from '@/api/StandardRequest.js';
import store from '@/store';

/**
 * Composable for making API requests with reactive state
 * This wraps the existing StandardRequest class without replacing it
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - API methods and reactive state
 */
export default function useApi(options = {}) {
  const loading = ref(false);
  const error = ref(null);
  const data = ref(null);

  /**
   * Make an API request using the existing StandardRequest pattern
   * @param {Object} requestOptions - Request configuration
   * @returns {Promise} - Promise that resolves with the response
   */
  const request = async (requestOptions) => {
    loading.value = true;
    error.value = null;

    return new Promise((resolve, reject) => {
      new StandardRequest(store, {
        ...requestOptions,
        ...options
      })
      .success(response => {
        data.value = response;
        loading.value = false;
        resolve(response);
      })
      .error(err => {
        error.value = err;
        loading.value = false;
        reject(err);
      });
    });
  };

  /**
   * Clear the current state
   */
  const clear = () => {
    data.value = null;
    error.value = null;
    loading.value = false;
  };

  return {
    // Reactive state
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    data: computed(() => data.value),
    
    // Methods
    request,
    clear
  };
}
