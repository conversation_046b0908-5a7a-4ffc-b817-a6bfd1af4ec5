# Composables

This directory contains Vue Composition API composables that provide reusable logic for the application.

## Purpose

These composables are designed to:
- Work alongside existing code without breaking it
- Provide modern alternatives to common patterns
- Prepare for eventual Vue 3 migration
- Improve code reusability and testability

## Available Composables

### `useApi.js`
Wrapper around the existing StandardRequest class that provides reactive state for API calls.

**Usage:**
```javascript
import { useApi } from '@/composables';

export default {
  setup() {
    const { request, loading, error, data } = useApi();
    
    const loadData = async () => {
      await request({
        noun: 'Customer',
        verb: 'GetAll'
      });
    };
    
    return {
      loadData,
      loading,
      error,
      data
    };
  }
};
```

### `useNavigation.js`
Navigation utilities that work with the existing router setup.

**Usage:**
```javascript
import { useNavigation } from '@/composables';

export default {
  setup() {
    const { navigateTo, goBack, canNavigateTo } = useNavigation();
    
    const handleSave = async () => {
      // Save logic...
      await navigateTo({ name: 'Customers' });
    };
    
    return {
      handleSave,
      goBack
    };
  }
};
```

### `useForm.js`
Form state management utilities.

**Usage:**
```javascript
import { useForm } from '@/composables';

export default {
  setup() {
    const { formData, updateField, errors, isValid } = useForm({
      name: '',
      email: ''
    });
    
    return {
      formData,
      updateField,
      errors,
      isValid
    };
  }
};
```

## Migration Strategy

1. **Phase 1**: Use composables in new components only
2. **Phase 2**: Gradually migrate existing components when they need updates
3. **Phase 3**: Eventually replace old patterns entirely

## Compatibility

These composables are designed to work with:
- Vue 2.7 with Composition API plugin
- Existing Vuex store
- Current router setup
- Existing API layer (StandardRequest, etc.)

## Testing

Each composable should have corresponding tests in the `__tests__` directory following the pattern established in `src/components/inputs/TowType/__tests__/`.
