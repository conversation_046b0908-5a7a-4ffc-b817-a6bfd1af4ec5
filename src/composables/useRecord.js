import { ref, computed, reactive } from '@vue/composition-api';
import useApi from './useApi.js';
import useNavigation from './useNavigation.js';

/**
 * Composable for single record operations (CRUD)
 * Provides utilities for loading, saving, and managing individual records
 * Works with existing record patterns in the application
 *
 * @param {Object} options - Configuration options
 * @returns {Object} - Record methods and reactive state
 */
export default function useRecord(options = {}) {
  const {
    noun,
    keyField = 'lKey',
    listRouteName,
    autoLoad = true
  } = options;

  const { request, loading, error } = useApi();
  const { navigateTo } = useNavigation();

  const record = reactive({});
  const originalRecord = ref({});
  const isNew = ref(true);
  const isDirty = ref(false);
  const saving = ref(false);

  /**
   * Load a record by key
   * @param {String|Number} key - Record key
   * @param {Object} additionalParams - Additional parameters
   */
  const loadRecord = async (key, additionalParams = {}) => {
    if (!noun) {
      console.warn('useRecord: noun is required for record loading');
      return;
    }

    if (!key) {
      // New record
      isNew.value = true;
      Object.keys(record).forEach(k => delete record[k]);
      originalRecord.value = {};
      return;
    }

    try {
      const params = {
        noun,
        verb: 'GetByKey',
        data: {
          [keyField]: key,
          ...additionalParams
        }
      };

      const response = await request(params);

      if (response) {
        // Clear existing record data
        Object.keys(record).forEach(k => delete record[k]);
        // Set new record data
        Object.assign(record, response);
        originalRecord.value = { ...response };
        isNew.value = false;
        isDirty.value = false;
      }
    } catch (err) {
      console.error('Record loading error:', err);
    }
  };

  /**
   * Save the current record
   * @param {Object} additionalData - Additional data to include in save
   * @returns {Promise} - Save promise
   */
  const saveRecord = async (additionalData = {}) => {
    if (!noun) {
      console.warn('useRecord: noun is required for saving');
      return;
    }

    saving.value = true;

    try {
      const verb = isNew.value ? 'Create' : 'Update';
      const saveData = { ...record, ...additionalData };

      const params = {
        noun,
        verb,
        data: saveData
      };

      const response = await request(params);

      if (response) {
        // Update record with response data
        Object.assign(record, response);
        originalRecord.value = { ...response };
        isNew.value = false;
        isDirty.value = false;

        return response;
      }
    } catch (err) {
      console.error('Record save error:', err);
      throw err;
    } finally {
      saving.value = false;
    }
  };

  /**
   * Delete the current record
   * @returns {Promise} - Delete promise
   */
  const deleteRecord = async () => {
    if (!noun || isNew.value) {
      console.warn('useRecord: Cannot delete new or invalid record');
      return;
    }

    try {
      const params = {
        noun,
        verb: 'Delete',
        data: {
          [keyField]: record[keyField]
        }
      };

      await request(params);

      // Navigate back to list if route is provided
      if (listRouteName) {
        await navigateTo({ name: listRouteName });
      }
    } catch (err) {
      console.error('Record delete error:', err);
      throw err;
    }
  };

  /**
   * Duplicate the current record
   */
  const duplicateRecord = () => {
    const duplicatedData = { ...record };
    delete duplicatedData[keyField]; // Remove the key to make it new

    // Clear existing record data
    Object.keys(record).forEach(k => delete record[k]);
    // Set duplicated data
    Object.assign(record, duplicatedData);

    isNew.value = true;
    isDirty.value = true;
  };

  /**
   * Reset record to original state
   */
  const resetRecord = () => {
    Object.keys(record).forEach(k => delete record[k]);
    Object.assign(record, { ...originalRecord.value });
    isDirty.value = false;
  };

  /**
   * Update a field in the record
   * @param {String} field - Field name
   * @param {*} value - New value
   */
  const updateField = (field, value) => {
    record[field] = value;
    isDirty.value = true;
  };

  /**
   * Save and navigate back to list
   */
  const saveAndReturn = async (additionalData = {}) => {
    await saveRecord(additionalData);
    if (listRouteName) {
      await navigateTo({ name: listRouteName });
    }
  };

  // Computed properties
  const recordKey = computed(() => {
    return record[keyField] || null;
  });

  const hasChanges = computed(() => {
    return isDirty.value;
  });

  const canSave = computed(() => {
    return isDirty.value && !saving.value && !loading.value;
  });

  const canDelete = computed(() => {
    return !isNew.value && !saving.value && !loading.value;
  });

  return {
    // Reactive state
    record,
    isNew: computed(() => isNew.value),
    isDirty: computed(() => isDirty.value),
    saving: computed(() => saving.value),
    loading,
    error,

    // Computed properties
    recordKey,
    hasChanges,
    canSave,
    canDelete,

    // Methods
    loadRecord,
    saveRecord,
    deleteRecord,
    duplicateRecord,
    resetRecord,
    updateField,
    saveAndReturn
  };
}
