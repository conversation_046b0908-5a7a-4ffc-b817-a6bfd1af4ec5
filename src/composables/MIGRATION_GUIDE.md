# Composables Migration Guide

This guide shows how to safely migrate existing components to use the new composables without breaking existing functionality.

## Safe Migration Strategy

### Phase 1: Use in New Components Only
- Apply composables to any new components being created
- Test thoroughly in non-critical areas
- Build confidence with the new patterns

### Phase 2: Migrate During Bug Fixes
- When fixing bugs in existing components, optionally refactor to use composables
- This provides a natural opportunity to improve code quality
- Easier to test since you're already working on the component

### Phase 3: Gradual Component Updates
- Slowly migrate existing components when convenient
- No pressure to change everything at once
- Focus on high-value components first

## Migration Examples

### Before: Traditional Options API Component

```vue
<template>
  <div>
    <select v-model="selectedCustomer" @change="handleCustomerChange">
      <option v-for="customer in customers" :key="customer.lKey" :value="customer.lKey">
        {{ customer.vc100Name }}
      </option>
    </select>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      customers: [],
      selectedCustomer: '',
      loading: false
    };
  },
  
  mounted() {
    this.loadCustomers();
  },
  
  methods: {
    ...mapActions(['RECORDS__getAll']),
    
    loadCustomers() {
      this.loading = true;
      this.RECORDS__getAll('Customer', {}, response => {
        this.customers = response;
        this.loading = false;
      });
    },
    
    handleCustomerChange() {
      this.$emit('customer-selected', this.selectedCustomer);
    }
  }
};
</script>
```

### After: Using Composables (Composition API)

```vue
<template>
  <div>
    <ModernSelect
      noun="Customer"
      label="Customer"
      :value="selectedCustomer"
      @input="handleCustomerChange"
      :loading="isLoading"
    />
  </div>
</template>

<script>
import { ref } from '@vue/composition-api';
import { useOptions } from '@/composables';
import ModernSelect from '@/components/inputs/ModernSelect.vue';

export default {
  components: { ModernSelect },
  
  setup(props, { emit }) {
    const selectedCustomer = ref('');
    
    const { isLoading } = useOptions({
      noun: 'Customer',
      autoLoad: true
    });
    
    const handleCustomerChange = (value) => {
      selectedCustomer.value = value;
      emit('customer-selected', value);
    };
    
    return {
      selectedCustomer,
      isLoading,
      handleCustomerChange
    };
  }
};
</script>
```

### Hybrid Approach: Adding Composables to Options API

If you prefer to keep using Options API, you can still use composables:

```vue
<script>
import { useOptions } from '@/composables';

export default {
  data() {
    return {
      selectedCustomer: ''
    };
  },
  
  setup() {
    // Use composables in setup, return to Options API
    const { optionsForSelect, isLoading } = useOptions({
      noun: 'Customer',
      autoLoad: true
    });
    
    return {
      customers: optionsForSelect,
      customersLoading: isLoading
    };
  },
  
  methods: {
    handleCustomerChange() {
      this.$emit('customer-selected', this.selectedCustomer);
    }
  }
};
</script>
```

## Common Migration Patterns

### 1. API Calls
**Before:**
```javascript
methods: {
  ...mapActions(['RECORDS__getAll']),
  loadData() {
    this.loading = true;
    this.RECORDS__getAll('Customer', {}, response => {
      this.data = response;
      this.loading = false;
    });
  }
}
```

**After:**
```javascript
setup() {
  const { request, loading, data } = useApi();
  
  const loadData = async () => {
    await request({
      noun: 'Customer',
      verb: 'GetAll'
    });
  };
  
  return { loadData, loading, data };
}
```

### 2. Form Handling
**Before:**
```javascript
data() {
  return {
    form: { name: '', email: '' },
    errors: {},
    submitting: false
  };
},
methods: {
  updateField(field, value) {
    this.form[field] = value;
    if (this.errors[field]) {
      delete this.errors[field];
    }
  }
}
```

**After:**
```javascript
setup() {
  const { formData, updateField, errors, submitting } = useForm({
    name: '',
    email: ''
  });
  
  return { formData, updateField, errors, submitting };
}
```

## Testing Migration

### 1. Test Composables in Isolation
```javascript
import { useApi } from '@/composables';

describe('useApi', () => {
  it('should handle API requests', async () => {
    const { request, loading, data } = useApi();
    // Test the composable logic
  });
});
```

### 2. Test Components Using Composables
```javascript
import { mount } from '@vue/test-utils';
import MyComponent from './MyComponent.vue';

// Mock composables for testing
vi.mock('@/composables', () => ({
  useApi: () => ({
    loading: { value: false },
    data: { value: [] },
    request: vi.fn()
  })
}));
```

## Benefits of Migration

1. **Better Code Reuse**: Logic can be shared between components
2. **Easier Testing**: Composables can be tested in isolation
3. **Better TypeScript Support**: When you eventually migrate to Vue 3
4. **Cleaner Components**: Separation of concerns between UI and logic
5. **Vue 3 Preparation**: Smooth migration path to Vue 3

## When NOT to Migrate

- Components that are working well and rarely change
- Components scheduled for removal
- During critical deadlines or releases
- When the team is not familiar with Composition API yet

Remember: **Migration is optional and should be done gradually for maximum safety.**
