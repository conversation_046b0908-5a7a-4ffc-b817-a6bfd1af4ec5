import { describe, it, expect } from 'vitest';

describe('Composables Imports', () => {
  it('should import useApi correctly', async () => {
    const { default: useApi } = await import('../useApi.js');
    expect(typeof useApi).toBe('function');
  });

  it('should import useForm correctly', async () => {
    const { default: useForm } = await import('../useForm.js');
    expect(typeof useForm).toBe('function');
  });

  it('should import useNavigation correctly', async () => {
    const { default: useNavigation } = await import('../useNavigation.js');
    expect(typeof useNavigation).toBe('function');
  });

  it('should import useGrid correctly', async () => {
    const { default: useGrid } = await import('../useGrid.js');
    expect(typeof useGrid).toBe('function');
  });

  it('should import useRecord correctly', async () => {
    const { default: useRecord } = await import('../useRecord.js');
    expect(typeof useRecord).toBe('function');
  });

  it('should import useOptions correctly', async () => {
    const { default: useOptions } = await import('../useOptions.js');
    expect(typeof useOptions).toBe('function');
  });

  it('should import all composables from index', async () => {
    const composables = await import('../index.js');
    
    expect(typeof composables.useApi).toBe('function');
    expect(typeof composables.useForm).toBe('function');
    expect(typeof composables.useNavigation).toBe('function');
    expect(typeof composables.useGrid).toBe('function');
    expect(typeof composables.useRecord).toBe('function');
    expect(typeof composables.useOptions).toBe('function');
    expect(typeof composables.useTowTypes).toBe('function');
    expect(typeof composables.useReasons).toBe('function');
    expect(typeof composables.useCustomers).toBe('function');
    expect(typeof composables.useDrivers).toBe('function');
    expect(typeof composables.useTrucks).toBe('function');
  });
});
