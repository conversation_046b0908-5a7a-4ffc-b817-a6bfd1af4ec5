import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createLocalVue } from '@vue/test-utils';
import CompositionApi from '@vue/composition-api';
import useApi from '../useApi.js';

// Mock the StandardRequest
vi.mock('@/api/StandardRequest.js', () => {
  return {
    default: vi.fn().mockImplementation((store, options) => ({
      success: vi.fn().mockImplementation(function(callback) {
        // Simulate successful response
        setTimeout(() => callback({ data: 'test' }), 10);
        return this;
      }),
      error: vi.fn().mockImplementation(function(callback) {
        return this;
      })
    }))
  };
});

// Mock the store
vi.mock('@/store', () => ({
  default: {
    commit: vi.fn(),
    dispatch: vi.fn(),
    getters: {}
  }
}));

const localVue = createLocalVue();
localVue.use(CompositionApi);

describe('useApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with correct default state', () => {
    const { loading, error, data } = useApi();
    
    expect(loading.value).toBe(false);
    expect(error.value).toBe(null);
    expect(data.value).toBe(null);
  });

  it('should handle successful API requests', async () => {
    const { request, loading, data } = useApi();
    
    const requestPromise = request({
      noun: 'Test',
      verb: 'GetAll'
    });
    
    // Should be loading during request
    expect(loading.value).toBe(true);
    
    const result = await requestPromise;
    
    // Should have data and not be loading after success
    expect(loading.value).toBe(false);
    expect(data.value).toEqual({ data: 'test' });
    expect(result).toEqual({ data: 'test' });
  });

  it('should clear state correctly', () => {
    const { clear, loading, error, data } = useApi();
    
    // Set some state
    data.value = { test: 'data' };
    error.value = 'some error';
    loading.value = true;
    
    // Clear state
    clear();
    
    // Should be reset
    expect(loading.value).toBe(false);
    expect(error.value).toBe(null);
    expect(data.value).toBe(null);
  });
});
