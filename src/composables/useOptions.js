import { ref, computed } from '@vue/composition-api';
import { useApi } from './useApi.js';

/**
 * Composable for loading and managing option lists (dropdowns, selects, etc.)
 * Works with existing patterns like TowType, but provides a generic solution
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - Options methods and reactive state
 */
export default function useOptions(options = {}) {
  const {
    noun,
    verb = 'GetAll',
    cacheKey,
    filterActive = true,
    autoLoad = true
  } = options;

  const { request, loading, error } = useApi();
  
  const allOptions = ref([]);
  const isLoaded = ref(false);
  const isLoading = ref(false);

  /**
   * Load options from API
   * @param {Object} additionalParams - Additional parameters for the request
   * @returns {Promise} - Promise that resolves with the options
   */
  const loadOptions = async (additionalParams = {}) => {
    if (!noun) {
      console.warn('useOptions: noun is required for loading options');
      return [];
    }

    if (isLoaded.value || isLoading.value) {
      return allOptions.value;
    }

    isLoading.value = true;

    try {
      const params = {
        noun,
        verb,
        data: additionalParams
      };

      const response = await request(params);
      
      if (Array.isArray(response)) {
        allOptions.value = response;
      } else if (response && Array.isArray(response.Records)) {
        allOptions.value = response.Records;
      } else {
        allOptions.value = [];
      }
      
      isLoaded.value = true;
      return allOptions.value;
    } catch (err) {
      console.error('Options loading error:', err);
      allOptions.value = [];
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Refresh options (force reload)
   */
  const refreshOptions = async () => {
    isLoaded.value = false;
    return await loadOptions();
  };

  /**
   * Find an option by key
   * @param {String|Number} key - Option key to find
   * @returns {Object|null} - Found option or null
   */
  const findOption = (key) => {
    return allOptions.value.find(option => 
      option.lKey === key || 
      option.key === key || 
      option.value === key ||
      option.id === key
    ) || null;
  };

  /**
   * Get option label by key
   * @param {String|Number} key - Option key
   * @returns {String} - Option label or empty string
   */
  const getOptionLabel = (key) => {
    const option = findOption(key);
    return option ? (
      option.label || 
      option.name || 
      option.text || 
      option.description ||
      option.vc100Name ||
      String(key)
    ) : '';
  };

  /**
   * Check if an option is active
   * @param {Object} option - Option object
   * @returns {Boolean} - Whether option is active
   */
  const isOptionActive = (option) => {
    if (!option) return false;
    
    // Check various active field patterns
    return option.bActive !== false && 
           option.active !== false && 
           option.isActive !== false &&
           option.status !== 'inactive';
  };

  // Computed properties
  const activeOptions = computed(() => {
    if (!filterActive) return allOptions.value;
    
    return allOptions.value.filter(isOptionActive);
  });

  const optionsForSelect = computed(() => {
    return activeOptions.value.map(option => ({
      value: option.lKey || option.key || option.value || option.id,
      label: getOptionLabel(option.lKey || option.key || option.value || option.id),
      ...option
    }));
  });

  const hasOptions = computed(() => {
    return allOptions.value.length > 0;
  });

  const hasActiveOptions = computed(() => {
    return activeOptions.value.length > 0;
  });

  // Auto-load if enabled
  if (autoLoad && noun) {
    loadOptions();
  }

  return {
    // Reactive state
    allOptions: computed(() => allOptions.value),
    activeOptions,
    optionsForSelect,
    isLoaded: computed(() => isLoaded.value),
    isLoading: computed(() => isLoading.value),
    loading,
    error,
    
    // Computed properties
    hasOptions,
    hasActiveOptions,
    
    // Methods
    loadOptions,
    refreshOptions,
    findOption,
    getOptionLabel,
    isOptionActive
  };
}

/**
 * Convenience function for common option types
 */
export const useTowTypes = () => useOptions({
  noun: 'TowType',
  cacheKey: 'towTypes'
});

export const useReasons = () => useOptions({
  noun: 'Reason',
  cacheKey: 'reasons'
});

export const useCustomers = () => useOptions({
  noun: 'Customer',
  cacheKey: 'customers'
});

export const useDrivers = () => useOptions({
  noun: 'Driver',
  cacheKey: 'drivers'
});

export const useTrucks = () => useOptions({
  noun: 'Truck',
  cacheKey: 'trucks'
});
