import { ref, computed, reactive } from '@vue/composition-api';

/**
 * Composable for form state management
 * Provides utilities for handling form data, validation, and submission
 * 
 * @param {Object} initialData - Initial form data
 * @param {Object} options - Configuration options
 * @returns {Object} - Form methods and reactive state
 */
export default function useForm(initialData = {}, options = {}) {
  const formData = reactive({ ...initialData });
  const errors = ref({});
  const touched = ref({});
  const submitting = ref(false);

  /**
   * Update a form field value
   * @param {String} field - Field name
   * @param {*} value - New value
   */
  const updateField = (field, value) => {
    formData[field] = value;
    touched.value[field] = true;
    
    // Clear error when field is updated
    if (errors.value[field]) {
      delete errors.value[field];
      errors.value = { ...errors.value };
    }
  };

  /**
   * Set form errors
   * @param {Object} newErrors - Error object
   */
  const setErrors = (newErrors) => {
    errors.value = { ...newErrors };
  };

  /**
   * Clear all errors
   */
  const clearErrors = () => {
    errors.value = {};
  };

  /**
   * Reset form to initial state
   */
  const reset = () => {
    Object.keys(formData).forEach(key => {
      delete formData[key];
    });
    Object.assign(formData, { ...initialData });
    errors.value = {};
    touched.value = {};
    submitting.value = false;
  };

  /**
   * Check if form has any errors
   */
  const hasErrors = computed(() => {
    return Object.keys(errors.value).length > 0;
  });

  /**
   * Check if form is valid (no errors and has been touched)
   */
  const isValid = computed(() => {
    return !hasErrors.value && Object.keys(touched.value).length > 0;
  });

  /**
   * Get error for a specific field
   * @param {String} field - Field name
   * @returns {String|null} - Error message or null
   */
  const getFieldError = (field) => {
    return errors.value[field] || null;
  };

  /**
   * Check if a field has been touched
   * @param {String} field - Field name
   * @returns {Boolean} - Whether field has been touched
   */
  const isFieldTouched = (field) => {
    return !!touched.value[field];
  };

  return {
    // Reactive state
    formData,
    errors: computed(() => errors.value),
    touched: computed(() => touched.value),
    submitting: computed(() => submitting.value),
    hasErrors,
    isValid,
    
    // Methods
    updateField,
    setErrors,
    clearErrors,
    reset,
    getFieldError,
    isFieldTouched,
    
    // Utilities for external control
    setSubmitting: (value) => { submitting.value = value; }
  };
}
