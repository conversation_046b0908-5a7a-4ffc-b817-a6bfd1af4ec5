import { ref, computed, reactive } from '@vue/composition-api';
import { useApi } from './useApi.js';

/**
 * Composable for grid/table functionality
 * Provides utilities for data loading, pagination, sorting, and filtering
 * Works with the existing Grid component patterns
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - Grid methods and reactive state
 */
export default function useGrid(options = {}) {
  const {
    noun,
    verb = 'GetAll',
    pageSize = 50,
    autoLoad = true
  } = options;

  const { request, loading, error } = useApi();
  
  const gridData = ref([]);
  const totalRecords = ref(0);
  const currentPage = ref(1);
  const sortField = ref('');
  const sortDirection = ref('asc');
  const filters = reactive({});
  const selectedRows = ref([]);

  /**
   * Load grid data from API
   * @param {Object} additionalParams - Additional parameters for the request
   */
  const loadData = async (additionalParams = {}) => {
    if (!noun) {
      console.warn('useGrid: noun is required for data loading');
      return;
    }

    try {
      const params = {
        noun,
        verb,
        data: {
          PageSize: pageSize,
          PageNumber: currentPage.value,
          SortField: sortField.value,
          SortDirection: sortDirection.value,
          ...filters,
          ...additionalParams
        }
      };

      const response = await request(params);
      
      if (response && Array.isArray(response.Records)) {
        gridData.value = response.Records;
        totalRecords.value = response.TotalRecords || response.Records.length;
      } else if (Array.isArray(response)) {
        gridData.value = response;
        totalRecords.value = response.length;
      }
    } catch (err) {
      console.error('Grid data loading error:', err);
      gridData.value = [];
      totalRecords.value = 0;
    }
  };

  /**
   * Refresh the current page
   */
  const refresh = () => {
    loadData();
  };

  /**
   * Go to a specific page
   * @param {Number} page - Page number
   */
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
      loadData();
    }
  };

  /**
   * Sort by a field
   * @param {String} field - Field to sort by
   * @param {String} direction - Sort direction ('asc' or 'desc')
   */
  const sortBy = (field, direction = 'asc') => {
    sortField.value = field;
    sortDirection.value = direction;
    currentPage.value = 1; // Reset to first page when sorting
    loadData();
  };

  /**
   * Apply filters
   * @param {Object} newFilters - Filter object
   */
  const applyFilters = (newFilters) => {
    Object.assign(filters, newFilters);
    currentPage.value = 1; // Reset to first page when filtering
    loadData();
  };

  /**
   * Clear all filters
   */
  const clearFilters = () => {
    Object.keys(filters).forEach(key => {
      delete filters[key];
    });
    currentPage.value = 1;
    loadData();
  };

  /**
   * Select/deselect rows
   * @param {Array} rows - Array of row data or keys
   */
  const selectRows = (rows) => {
    selectedRows.value = [...rows];
  };

  /**
   * Toggle row selection
   * @param {*} row - Row data or key
   */
  const toggleRowSelection = (row) => {
    const index = selectedRows.value.findIndex(r => r === row);
    if (index >= 0) {
      selectedRows.value.splice(index, 1);
    } else {
      selectedRows.value.push(row);
    }
  };

  /**
   * Clear row selection
   */
  const clearSelection = () => {
    selectedRows.value = [];
  };

  // Computed properties
  const totalPages = computed(() => {
    return Math.ceil(totalRecords.value / pageSize);
  });

  const hasData = computed(() => {
    return gridData.value.length > 0;
  });

  const hasSelection = computed(() => {
    return selectedRows.value.length > 0;
  });

  const oneRecordIsSelected = computed(() => {
    return selectedRows.value.length === 1;
  });

  // Auto-load data if enabled
  if (autoLoad && noun) {
    loadData();
  }

  return {
    // Reactive state
    gridData: computed(() => gridData.value),
    totalRecords: computed(() => totalRecords.value),
    currentPage: computed(() => currentPage.value),
    sortField: computed(() => sortField.value),
    sortDirection: computed(() => sortDirection.value),
    filters: computed(() => filters),
    selectedRows: computed(() => selectedRows.value),
    loading,
    error,
    
    // Computed properties
    totalPages,
    hasData,
    hasSelection,
    oneRecordIsSelected,
    
    // Methods
    loadData,
    refresh,
    goToPage,
    sortBy,
    applyFilters,
    clearFilters,
    selectRows,
    toggleRowSelection,
    clearSelection
  };
}
