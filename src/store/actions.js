import is from 'is_js';
import axios from 'axios';
import bowser from 'bowser';
import get from 'lodash/get';
import isFunction from 'lodash/isFunction';
import Device from '@/utils/device.js';
import Hub from '@/events/hub.js';
import Cookies from 'cookies-js';
import { fullDateTime } from '@/utils/filters.js';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

import {
  EVENT_ERROR,
  EVENT_LOGGED_OUT,
  PRODUCTKEY_WEB_PORTAL
} from '@/config.js';

const Browser = bowser.getParser(window.navigator.userAgent);

export const __getStates = (context, props) => {
  new CacheableRequest(context, {
    noun: 'Application',
    verb: 'GetStates'
  })
  .success(props.success);
};

export const __getNow = (context, props) => {
  new StandardRequest(context, {
    noun: 'Application',
    verb: 'GetNow'
  })
  .responseMiddleware(response => {
    response.Now = fullDateTime(response.Now);
  })
  .success(props.callback);
};

export const __sendHeartbeat = (context, props) => {
  new StandardRequest(context, {
    noun: 'Instance',
    verb: 'Heartbeat',
    data: {
      viewName: get(props, 'viewName', '')
    }
  })
  .success();
};

export const __signOut = (context, props) => {
  // We want to clear out the instance data before making the request.
  // If we don't then there may be multiple Logout requests made before the first one returns
  let parameters = {
    Operation: {
      Noun: 'Instance',
      Verb: 'Logout',
      ProductKey: context.rootState.product.key,
      OrgUnitKey: context.rootState.orgUnitKey,
      Mode: context.rootState.appMode,
      ResponseData: 'JSON'
    },
    Authentication: {
      UserKey: context.rootState.user.Key,
      InstanceKey: context.rootState.instance.Key,
      AuthenticationKey: context.rootState.instance.Authentication
    },
    Data: {}
  };

  context.commit('CLEAR_INSTANCE_DATA');
  context.commit('CLEAR_USER_DATA');
  context.commit('CLEAR_ROLE_DATA');
  context.commit('SET_ORG_UNIT', '');
  context.commit('SET_PRODUCT', PRODUCTKEY_WEB_PORTAL);
  context.commit('SET_TOPS_COMPANY_SETTINGS');
  context.commit('CACHE_RECORDS');
  context.commit('STORE_VIEW_SETTINGS');

  // Preserve certain values
  let deviceId = window.localStorage.getItem('device.id');
  let lastCompany = window.localStorage.getItem('last_company');

  window.sessionStorage.clear();
  window.localStorage.clear();

  if (deviceId) {
    window.localStorage.setItem('device.id', deviceId);
  }

  if (lastCompany) {
    window.localStorage.setItem('last_company', lastCompany);
  }

  Cookies.set('ignore_txc_SessionID', 'true', { expires: 5 });

  if (context.rootState.appMode === 'DEBUG') {
    console.log(`%crequest → %cInstance, Logout`, 'font-variant: small-caps; color: #0074D9', 'font-weight: bold', parameters);
  }

  axios.post(import.meta.env.VITE_TXI_API, parameters)
    .then(response => {
      if (context.rootState.appMode === 'DEBUG') {
        console.log(`%cresponse → %cInstance, Logout`, 'font-variant: small-caps; color: #39CCCC', 'font-weight: bold', response.data);
      }
      Hub.$emit(EVENT_LOGGED_OUT);
    })
    .catch(function (error) {
      if (context.rootState.appMode === 'DEBUG') {
        console.log(`%cerror → %cInstance, Logout`, 'font-variant: small-caps; color: #FF4136', 'font-weight: bold', error.Message);
      }
      Hub.$emit(EVENT_LOGGED_OUT);
    })
    .then(() => {
      // Note: Force a browser reload to workaround the issue of
      // signing out and in again breaking the user session.
      window.location.assign('');
    });
};

export const __clearSession = (context) => {
  context.commit('CLEAR_INSTANCE_DATA');
  context.commit('CLEAR_USER_DATA');
  context.commit('CLEAR_ROLE_DATA');
};

export const __authenticateUser = async (context, props) => {
  if (is.empty(context.state.apiMode)) {
    Hub.$emit(EVENT_ERROR, 'Application Mode has Not been Set');
    return;
  }

  const device = new Device();

  const ipAddress = await window.fetch('https://ipapi.co/json/', { referrerPolicy: 'no-referrer' })
    .then(response => response.json())
    .then(data => data.ip)
    .catch(error => console.error(error));

  const browser = {
    name: Browser.getBrowserName(),
    version: Browser.getBrowserVersion()
  };
  const os = {
    name: Browser.getOSName(),
    version: Browser.getOSVersion()
  };

  // NOTE: We assume that the instance either doesn't already exist or it expired. If not, __signOut should be called first.
  new StandardRequest(context, {
    noun: 'Instance',
    verb: 'Login',
    data: {
      UserID: props.username,
      Password: props.password,
      Product: Number(props.product),
      OrgUnit: Number(props.orgUnit),
      DeviceID: device.id,
      DeviceInfo: get(props, 'deviceInfo', `Browser: ${browser.name} ${browser.version}; OS: ${os.name} ${os.version}; IP: ${ipAddress}`)
    }
  })
  .success(response => {
    context.commit('SET_INSTANCE_DATA', response);
    context.commit('SET_WINDOW_NAME');

    if ('User' in response) {
      context.commit('SET_USER_DATA', response.User);
    }

    if ('Role' in response) {
      context.commit('SET_ROLE_DATA', response.Role);
    }

    if (isFunction(props.callback)) {
      props.callback(response);
    }
  });
};

export const __changeProductOrgUnit = (context, props) => {
  new StandardRequest(context, {
    noun: 'Instance',
    verb: 'ChangeProductOrgUnit',
    data: {
      Product: props.product,
      OrgUnit: props.orgUnit
    }
  })
  .success(response => {
    context.commit('SET_ORG_UNIT', response.OrgUnit);
    context.commit('SET_PRODUCT', response.Product);
    context.commit('SET_INSTANCE_DATA', response);
    context.commit('SET_WINDOW_NAME');

    if (is.existy(response.OrgUnits)) {
      context.commit('SET_ORG_UNITS', response.OrgUnits);
    }

    if (is.existy(response.Role)) {
      context.commit('SET_ROLE_DATA', response.Role);
    }

    if (is.function(props.callback)) {
      props.callback(response);
    }
  });
};

export const __selectProduct = (context, productKey) => {
  __changeProductOrgUnit(context, { product: productKey, orgUnit: context.state.orgUnitKey });
};

export const __toggleBusy = context => context.commit('TOGGLE_BUSY');

export const __setBusy = context => context.commit('SET_BUSY');

export const __setNotBusy = context => context.commit('SET_NOT_BUSY');

export const __setAppMode = (context, appMode) => context.commit('SET_APP_MODE', appMode);

export const __setProduct = (context, productKey) => context.commit('SET_PRODUCT', productKey);

export const __setCompany = (context, value) => context.commit('SET_ORG_UNIT', value);

export const __selectRecords = (context, value) => context.commit('SET_SELECTED_RECORD', value);

export const __cacheRecords = (context, value) => context.commit('CACHE_RECORDS', value);

export const __setCurrentView = (context, value) => context.commit('SET_CURRENT_VIEW', value);

export const __cacheFilter = (context, value) => context.commit('ADD_CACHED_FILTER', value);

export const __uncacheFilter = (context, value) => context.commit('REMOVE_CACHED_FILTER', value);
