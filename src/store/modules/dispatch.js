import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  DISPATCH__setDispatched (context, props) {
    new StandardRequest(context, {
      noun: 'Dispatch',
      verb: 'SetToDispatched',
      data: { Key: props.key }
    })
    .success(props.callback);
  },

  DISPATCH__getStatuses (context, props) {
    new StandardRequest(context, {
      noun: 'Dispatch',
      verb: 'GetStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
