import { sortBy } from 'lodash';

const state = {
  primaryList: 'drivers', // 'drivers' or 'trucks'
  showBusyDrivers: true,
  showBusyTrucks: true,
  driverStatusFilter: '',
  truckStatusFilter: '',
  dispatchUnitsResponse: {}
};

const getters = {
  'assignCall.drivers' (state) {
    return sortBy(state.dispatchUnitsResponse.Drivers, ['Count', 'Code'])
      .filter(driver => !driver.Count || (state.showBusyDrivers && driver.Count > 0)) || [];
  },

  'assignCall.trucks' (state) {
    return sortBy(state.dispatchUnitsResponse.Trucks, ['Count', 'Number'])
      .filter(truck => !truck.Count || (state.showBusyTrucks && truck.Count > 0)) || [];
  }
};

export default {
  state,
  getters
};
