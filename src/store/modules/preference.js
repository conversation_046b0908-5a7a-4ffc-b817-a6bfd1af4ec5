const state = {
  autoGeocodeLocation: true, // Call Tow Section
  autoGeocodeDestination: true // Call Tow Section
};

const getters = {
  PREFERENCE__state: state => state
};

const actions = {
  PREFERENCE__setTowSection (context, props) {
    context.commit('SET_PREFERENCE_TOW_SECTION', props);
  }
};

const mutations = {
  SET_PREFERENCE_TOW_SECTION (state, props) {
    state.autoGeocodeLocation = props.autoGeocodeLocation;
    state.autoGeocodeDestination = props.autoGeocodeDestination;
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
