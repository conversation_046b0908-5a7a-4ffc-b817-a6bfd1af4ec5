import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  // UTILITY__detachCall (context, props) {
  //   new StandardRequest(context, {
  //     noun: 'Noun',
  //     verb: 'Verb',
  //     data: {
  //       OrgUnitKey: props.locationKey,
  //       Key: props.callKey
  //     }
  //   })
  //   .success(props.callback);
  // }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
