import { get } from 'lodash';
import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  TOWTYPE__getClasses (context, props) {
    new StandardRequest(context, {
      noun: 'TowType',
      verb: 'GetTowClasses',
      data: {
        Parameters: {
          IncludeHeader: true,
          NameValuePairs: false
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
