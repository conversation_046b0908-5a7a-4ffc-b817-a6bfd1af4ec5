import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  LICENSE__parse (context, props) {
    new StandardRequest(context, {
      noun: 'License',
      verb: 'ParseTrackData',
      data: {
        Value: props.value
      }
    })
    .success(props.callback)
    .fail(props.fail)
    .always(props.always);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
