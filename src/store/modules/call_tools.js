import StandardRequest from '@/api/StandardRequest.js';
// import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  CALL_TOOLS__getToolInputs (context, props) {
    new StandardRequest(context, {
      noun: 'CallTools',
      verb: 'GetToolInputs',
      data: {
        ToolID: props.toolId
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  CALL_TOOLS__process (context, { toolId, callKeys, success, ...inputs }) {
    new StandardRequest(context, {
      noun: 'CallTools',
      verb: 'Process',
      data: {
        ...inputs,
        ToolID: toolId,
        CallKeys: callKeys
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
