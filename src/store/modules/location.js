import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  LOCATIONSERVICE__read (context, props) {
    new StandardRequest(context, {
      noun: 'LocationService',
      verb: 'Read',
      data: {
        lServiceKey: props.key
      }
    })
    .responseMiddleware(response => {
      if ('lServiceKey' in response) response.lServiceKey = Number(response.lServiceKey);
      if ('lUnitTypeKey' in response) response.lUnitTypeKey = Number(response.lUnitTypeKey);
      if ('bCalculated' in response) response.bCalculated = response.bCalculated === '1';
      if ('bTaxable' in response) response.bTaxable = response.bTaxable === '1';
      if ('bDiscountable' in response) response.bDiscountable = response.bDiscountable === '1';
      if ('bCommissionable' in response) response.bCommissionable = response.bCommissionable === '1';
      if ('bSurchargeable' in response) response.bSurchargeable = response.bSurchargeable === '1';
      if ('tDisplayOrderOverride' in response) response.tDisplayOrderOverride = Number(response.tDisplayOrderOverride);
      if ('lUserKey' in response) response.lUserKey = Number(response.lUserKey);
      if ('bActive' in response) response.bActive = response.bActive === '1';
    })
    .success(props.success);
  },

  LOCATIONSERVICE__update (context, props) {
    new StandardRequest(context, {
      noun: 'LocationService',
      verb: 'Update',
      data: {
        OverwriteUpdate: true,
        lServiceKey: props.serviceKey,
        vc100Description: props.description,
        lUnitTypeKey: props.unitTypeKey,
        bCalculated: props.calculated,
        bTaxable: props.taxable,
        bDiscountable: props.discountable,
        bCommissionable: props.commissionable,
        ch10LocationGL: props.glNumber,
        bSurchargeable: props.surcharge,
        tDisplayOrderOverride: props.displayOrderOverride,
        vc100MCBillingName: props.billingName,
        lUserKey: props.userKey,
        dDateLastModified: props.dateLastModified,
        bActive: props.active
      }
    })
    .responseMiddleware(response => {
      if ('lServiceKey' in response) response.lServiceKey = Number(response.lServiceKey);
      if ('bActive' in response) response.bActive = response.bActive === '1';
    })
    .success(props.success);
  },

  LOCATIONSERVICE__getDefaults (context, props) {
    new StandardRequest(context, {
      noun: 'LocationService',
      verb: 'GetDefaults',
      data: {
        Key: props.key
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
