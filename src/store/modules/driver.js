import CacheableRequest from '@/api/CacheableRequest.js';

const state = {
  // @Developer
  // These two properties are used to manage the flow of adding a driver which
  // can include a side quest to the employee screen, then push back to
  // the driver screen with a few automations along the way.
  active: false,
  employeeKey: ''
};

const getters = {
  DRIVER__addProcess: state => state
};

const actions = {
  DRIVER__getLicenseClasses (context, props) {
    new CacheableRequest(context, {
      noun: 'Driver',
      verb: 'GetLicenseClasses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  DRIVER__toggleAddProcess (context, props) {
    context.commit('DRIVER_ADD_PROCESS_ACTIVE', props);
  },

  DRIVER__cacheEmployeeKey (context, props) {
    context.commit('DRIVER_ADD_PROCESS_EMPLOYEE_KEY', props);
  }

  // Driver/GetEmployees does not exist. Use TOPSCompany/GetEmployees.
};

const mutations = {
  DRIVER_ADD_PROCESS_ACTIVE (state, props) {
    state.active = props;
  },

  DRIVER_ADD_PROCESS_EMPLOYEE_KEY (state, props) {
    state.employeeKey = props;
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
