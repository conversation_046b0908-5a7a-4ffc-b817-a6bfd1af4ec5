import { get } from 'lodash';
import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  MAP__getCoordinates (context, props) {
    new StandardRequest(context, {
      noun: 'Mapping',
      verb: 'GetCoordinates',
      data: {
        Location: props.location,
        Channel: get(props, 'channel', '') // Default: towXchange_webtops
      }
    })
    .success(props.callback);
  },

  MAP__getPlaceSuggestions (context, props) {
    new StandardRequest(context, {
      noun: 'Mapping',
      verb: 'PlaceAutoComplete',
      data: {
        Place: props.query,
        Latitude: props.latitude,
        Longitude: props.longitude,
        Radius: get(props, 'radius', ''), // Default in Meters: 500
        Channel: get(props, 'channel', '') // Default: towXchange_webtops
      }
    })
    .success(props.callback);
  },

  MAP__coordinatesToAddress (context, props) {
    new StandardRequest(context, {
      noun: 'Mapping',
      verb: 'GetAddressFromCoordinates',
      data: {
        Latitude: props.latitude,
        Longitude: props.longitude,
        Channel: get(props, 'channel', '') // Default: towXchange_webtops
      }
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
