import StandardRequest from '@/api/StandardRequest.js';
import { get } from 'lodash';

const state = {};

const getters = {};

const actions = {
  LABEL__getLayouts (context, props) {
    new StandardRequest(context, {
      noun: 'Label',
      verb: 'GetLabels',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  LABEL__read (context, props) {
    new StandardRequest(context, {
      noun: 'Label',
      verb: 'Read',
      data: {
        vc50Alias: get(props, 'layout', null)
      }
    })
    .responseMiddleware(response => {
      if ('bPortrait' in response) response.bPortrait = response.bPortrait === '1';
      if ('tNumLabelsAcross' in response) response.tNumLabelsAcross = Number(response.tNumLabelsAcross);
      if ('tNumLabelsDown' in response) response.tNumLabelsDown = Number(response.tNumLabelsDown);
      if ('fLabelWidth' in response) response.fLabelWidth = Number(response.fLabelWidth);
      if ('fLabelHeight' in response) response.fLabelHeight = Number(response.fLabelHeight);
      if ('fLeftMargin' in response) response.fLeftMargin = Number(response.fLeftMargin);
      if ('fTopMargin' in response) response.fTopMargin = Number(response.fTopMargin);
      if ('fHztlSpacingBetweenLabels' in response) response.fHztlSpacingBetweenLabels = Number(response.fHztlSpacingBetweenLabels);
      if ('fVertSpacingBetweenLabels' in response) response.fVertSpacingBetweenLabels = Number(response.fVertSpacingBetweenLabels);
      if ('tMaxCharsPerLabel' in response) response.tMaxCharsPerLabel = Number(response.tMaxCharsPerLabel);
      if ('tMaxLinesPerLabel' in response) response.tMaxLinesPerLabel = Number(response.tMaxLinesPerLabel);
      if ('tDeadRowNum' in response) response.tDeadRowNum = Number(response.tDeadRowNum);
      if ('bActive' in response) response.bActive = Number(response.bActive);
      if ('tFontSize' in response) response.tFontSize = Number(response.tFontSize);
    })
    .success(props.success)
    .fail(props.fail);
  },

  LABEL__create (context, props) {
    new StandardRequest(context, {
      noun: 'Label',
      verb: 'CreateLabels',
      data: {
        LabelLines: get(props, 'labels', []),
        Alias: get(props, 'layout', null),
        StartLabel: get(props, 'startPosition', 1)
      }
    })
    .success(props.success)
    .fail(props.fail);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
