import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';
import { get, forEach, castArray } from 'lodash';

const state = {
  selectedTab: 'processes', // processes, labels, search

  selectedProcessKey: '',
  processes: [],

  selectedStepKey: '',
  steps: [],

  selectedLabelLayoutKey: '',
  selectedLabelProfile: {},
  selectedLabelKeys: [],
  labels: [],
  labelLayouts: [],
  assistAddLabelCount: 1,
  assistCopyLabelCount: 1,
  labelStartPosition: 1,
  hasUnprintedLabels: false,

  isReadyCallsVisible: true
};

const getters = {
  'lien.activeProcesses': state => {
    return state.processes.filter(process => process.Active);
  },

  'lien.activeSteps': state => {
    return state.steps.filter(step => step.bActive);
  },

  'lien.selectedStep': (state, getters) => {
    return getters['lien.activeSteps'].find(step => step.lLienStepKey === state.selectedStepKey) || null;
  },

  'lien.activeLabelLayouts': state => {
    return state.labelLayouts.filter(layout => layout.Active);
  },

  'lien.selectedOrAllLabels': (state) => {
    return state.selectedLabelKeys.length
      ? state.labels.filter(label => state.selectedLabelKeys.includes(label.key))
      : state.labels;
  }
};

const actions = {
  LIEN__getStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'Lien',
      verb: 'GetStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  LIENSTEP__getStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'LienStep',
      verb: 'GetStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  LIENSTEP__getTasks (context, props) {
    new StandardRequest(context, {
      noun: 'LienStep',
      verb: 'GetStepTasks',
      data: {
        Key: props.key
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.lLienStepKey = Number(item.lLienStepKey);
        item.lLienTaskKey = Number(item.lLienTaskKey);
        item.tOrder = Number(item.tOrder);
        item.lLienTaskTypeKey = Number(item.lLienTaskTypeKey);
        item.tIterations = Number(item.tIterations);
        item.tIterations_Default = Number(item.tIterations_Default);
        item.bSkipBatch = Number(item.bSkipBatch) === 1;
        item.bActive = Number(item.bActive) === 1;
        item.bAPI = Number(item.bAPI) === 1;
        item.bUsesCertificationNum = Number(item.bUsesCertificationNum) === 1;
      });
    })
    .success(props.callback);
  },

  LIENSTEPTASK__get (context, props) {
    new StandardRequest(context, {
      noun: 'LienStepTask',
      verb: 'GetTasks',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  LIENSTEPTASK__getOwnerTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'LienStepTask',
      verb: 'GetOwnerTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  LIEN__getProcesses (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetLienProcesses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  LIENPROCESS__getSteps (context, props) {
    new StandardRequest(context, {
      noun: 'LienProcess',
      verb: 'GetSteps',
      data: {
        Key: props.processKey,
        bGetTasks: true
      }
    })
    .responseMiddleware(response => {
      forEach(response, step => {
        step.bActive = Number(step.bActive) === 1;
        step.bRequired = Number(step.bRequired) === 1;
        step.iActivationQty = Number(step.iActivationQty);
        step.lActivationBasisTypeKey = Number(step.lActivationBasisTypeKey);
        step.lActivationUnitsTypeKey = Number(step.lActivationUnitsTypeKey);
        step.lLienProcessKey = Number(step.lLienProcessKey);
        step.lLienStepKey = Number(step.lLienStepKey);
        step.tOrder = Number(step.tOrder);

        forEach(step.Tasks, task => {
          task.lLienStepKey = Number(task.lLienStepKey);
          task.lLienTaskKey = Number(task.lLienTaskKey);
          task.tOrder = Number(task.tOrder);
          task.tIterations = Number(task.tIterations);
          task.bSkipBatch = Number(task.bSkipBatch) === 1;
          task.vc100TaskOwners = task.vc100TaskOwners;
          task.bSimpleCertifiedMail = Number(task.bSimpleCertifiedMail) === 1;
          task.bSCMReturnReceipt = Number(task.bSCMReturnReceipt) === 1;
          task.vc50TaskAlias = task.vc50TaskAlias;
          task.lLienTaskTypeKey = Number(task.lLienTaskTypeKey);
          task.lLocationKey = Number(task.lLocationKey);
          task.ch2StateKey = task.ch2StateKey;
          task.vc255Description = task.vc255Description;
          task.tIterations_Default = Number(task.tIterations_Default);
          task.bActive = Number(task.bActive) === 1;
          task.vc100TaskOwners_Default = task.vc100TaskOwners_Default;
          task.bAPI = Number(task.bAPI) === 1;
          task.bUsesCertificationNum = Number(task.bUsesCertificationNum) === 1;
        });
      });
    })
    .success(props.callback);
  },

  LIENBATCH__completeStep (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'CompleteStep',
      data: {
        Key: props.stepKey,
        Calls: castArray(props.callKeys)
      }
    })
    .success(props.callback);
  },

  LIENBATCH__skipStep (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'SkipStep',
      data: {
        Key: props.stepKey,
        Calls: castArray(props.callKeys)
      }
    })
    .success(props.callback);
  },

  LIENBATCH__holdStep (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'PutStepOnHold',
      data: {
        Key: props.stepKey,
        Hours: get(props, 'hours', ''),
        UntilDateTime: get(props, 'until', ''),
        Calls: castArray(props.callKeys)
      }
    })
    .success(props.callback);
  },

  LIENBATCH__activateStep (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'ActivateStep',
      data: {
        Key: props.stepKey,
        Calls: castArray(props.callKeys)
      }
    })
    .success(props.callback);
  },

  LIENBATCH__start (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'StartLien',
      data: {
        Key: props.processKey,
        Calls: castArray(props.callKeys)
      }
    })
    .success(props.callback);
  },

  LIENBATCH__terminate (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'TerminateLien',
      data: {
        RemoveLienPricingItems: props.removePricingItems,
        Calls: castArray(props.callKeys)
      }
    })
    .success(props.callback);
  },

  LIENBATCH__processTask (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'ProcessStepTask',
      returnRawResponse: get(props, 'returnRawResponse', false),
      data: {
        Key: props.stepKey,
        TaskOrder: props.order,
        LettersSentDate: get(props, 'lettersSentDate', ''),
        SetDate: get(props, 'setDate', ''),
        CertificationNum: get(props, 'certificationNumber', ''),
        Calls: castArray(get(props, 'callKeys', [])),
        Other: get(props, 'other', '') // Other Parameters such as New Lien Process Key, etc.
      }
    })
    .success(props.success)
    .fail(props.fail)
    .always(props.always);
  },

  LIENBATCH__processTasks (context, props) {
    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'ProcessStepTasks',
      data: {
        Key: props.stepKey,
        LettersSentDate: get(props, 'lettersSentDate', ''),
        LabelAlias: get(props, 'labelFormat', ''),
        StartLabel: get(props, 'startLabel', ''),
        SetDate: get(props, 'setDate', ''),
        CertificationNum: get(props, 'certificationNumber', ''),
        Calls: castArray(get(props, 'callKeys', [])),
        Other: get(props, 'certificationNumber', '') // Other Parameters such as New Lien Process Key, etc.
      }
    })
    .success(props.success)
    .fail(props.fail)
    .always(props.always);
  },

  LIENBATCH__addLetters (context, props) {
    let payload = {};
    props.calls.forEach(call => {
      payload[call.Key] = {
        Letters: call.Letters
      };
    });

    new StandardRequest(context, {
      noun: 'LienBatch',
      verb: 'AddLetters',
      data: payload
    })
    .success(props.callback);
  },

  LIENSTEP__getActivationUnits (context, props) {
    new StandardRequest(context, {
      noun: 'LienStep',
      verb: 'GetActivationUnits',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  },

  LIENSTEP__getActivationBasises (context, props) {
    new StandardRequest(context, {
      noun: 'LienStep',
      verb: 'GetActivationBasises',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  },

  LIENPROCESS__getAllTasks (context, props) {
    new StandardRequest(context, {
      noun: 'LienProcess',
      verb: 'GetAllTasks',
      data: {
        Key: get(props, 'key', '')
      }
    })
    .responseMiddleware(response => {
      forEach(response, step => {
        step.Key = Number(step.Key);
      });
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
