import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  IMAGE_forCall (context, props) {
    new StandardRequest(context, {
      noun: 'Image',
      verb: 'GetImagesInfoForCall',
      data: {
        Key: props.key
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
