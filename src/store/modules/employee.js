import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  EMPLOYEE__read (context, props) {
    new StandardRequest(context, {
      noun: 'Employee',
      verb: 'Read',
      data: {
        lEmployeeKey: props.employeeKey
      }
    })
    .success(props.success);
  },

  EMPLOYEE__getUsers (context, props) {
    new StandardRequest(context, {
      noun: 'Employee',
      verb: 'GetUsers',
      data: {
        Partial: props.like
      }
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
