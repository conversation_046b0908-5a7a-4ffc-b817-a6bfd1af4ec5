import is from 'is_js';
import { storifyDate } from '@/utils/filters.js';
import { get, isEmpty, forEach } from 'lodash';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {
  isTowPricingGuardActive: false,
  isCallReelEnabled: true
};

const getters = {};

const actions = {
  CALL__getDuplicateDefaults (context, props) {
    new StandardRequest(context, {
      noun: 'Call',
      verb: 'GetNewDefaults',
      data: {
        lCallKey: props.callKey
      }
    })
    .success(props.callback);
  },

  CALL__getStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  CALL__getActions (context, props) {
    new StandardRequest(context, {
      noun: 'Call',
      verb: 'GetPossibleActions',
      data: {
        CallKey: props.callKey,
        DispatchKey: props.dispatchKey,
        CompleteOnly: get(props, 'completeOnly', false)
      }
    })
    .success(props.callback);
  },

  CALL__handleAction (context, props) {
    new StandardRequest(context, {
      noun: 'Call',
      verb: 'HandleAction',
      lastRead: storifyDate(props.lastRead),
      data: props.data
    })
    .success(props.callback);
  },

  CALL__getSketch (context, props) {
    new StandardRequest(context, {
      noun: 'Call',
      verb: 'GetSnapshotViewData',
      data: { lCallKey: props.callKey }
    })
    .success(props.callback);
  },

  CALL__getSubterminals (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetSubterminals',
      data: {
        CallKey: props.callKey,
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  CALL__getFieldOptions (context, props) {
    let makeKey = get(props, 'makeKey', '');

    if (props.verb === 'GetModels' && isEmpty(makeKey)) return;

    new CacheableRequest(context, {
      noun: 'Call',
      verb: props.verb,
      data: { Make: makeKey }
    })
    .success(props.callback);
  },

  CALL__getMakes (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetMakes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  CALL__getModels (context, props) {
    if (is.falsy(props.makeKey)) return;

    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetModels',
      data: {
        Make: props.makeKey,
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  CALL__getTruckTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetTruckTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  CALL__read (context, props) {
    new StandardRequest(context, {
      noun: 'Call',
      verb: 'Read',
      data: { lCallKey: props.callKey }
    })
    .success(props.callback);
  },

  CALL__getDispatchDetails (context, props) {
    if (!props.callKey) return;

    new StandardRequest(context, {
      noun: 'Call',
      verb: 'GetDispatchDetails',
      data: {
        CallKey: props.callKey
        // Retow: get(props, 'retow', false)
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  CALL__getFinalDispositions (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetFinalDispositions',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  CALL__getReasons (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetReasons',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        item.Key = Number(item.Key);
        item.Active = [1, '1', 'True', true].includes(item.Active);
      });
    })
    .success(props.callback);
  },

  CALL__getCancelReasons (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetCancelReasons',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        item.Key = Number(item.Key);
        item.Active = [1, '1', 'True', true].includes(item.Active);
      });
    })
    .success(props.callback);
  },

  CALL__getCustomers (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetCustomers',
      data: {
        Partial: get(props, 'partial', '') // String value for filtering
      }
    })
    .success(props.callback);
  },

  CALL__getColors (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetColors',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  CALL__getTowPaySecret (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetTowPaySecret',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
