import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  ORDERLINE__getServices (context, props) {
    new CacheableRequest(context, {
      noun: 'OrderLine',
      verb: 'GetServices',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  ORDERLINE__getPricingTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'OrderLine',
      verb: 'GetPricingTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
