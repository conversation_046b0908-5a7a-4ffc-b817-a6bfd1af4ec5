import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

import {
  get,
  set,
  forEach,
  isNumber,
  isBoolean
} from 'lodash';

const state = {
  settings: {},
  hasCallInspectionItems: false
};

const getters = {
  TOPSCOMPANY__settings: state => get(state, 'settings', {}),

  TOPSCOMPANY__hasCallInspectionItems: state => state.hasCallInspectionItems
};

const actions = {
  TOPSCOMPANY__getSettings (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetSettings',
      data: {}
    })
    .responseMiddleware(response => {
      if ('ShowCityStateForGeocoding' in response) {
        response.ShowCityStateForGeocoding = Number(response.ShowCityStateForGeocoding) === 1;
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__setSettings (context, props) {
    context.commit('SET_TOPS_COMPANY_SETTINGS', props);
  },

  TOPSCOMPANY__getLots (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetStorageLots',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getLotCoordinates (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetStorageLotCoordinates',
      data: { LotKey: props.lotKey }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getEmployees (context, props) {
    let requestPayload = {
      Parameters: {
        IncludeHeader: false,
        NameValuePairs: true
      }
    };

    // if ('driversOnly' in props) {
    //   requestPayload.DriversOnly = false;
    // }

    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetEmployees',
      data: requestPayload
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getDriverStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetDriverStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getEmployeeTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetEmployeeTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getTrucks (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetTrucks',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getDrivers (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetDrivers',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getTruckStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetTruckStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  TOPSCOMPANY__updateDriverStatus (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'UpdateDriverStatus',
      data: {
        Key: props.key,
        StatusKey: props.statusKey
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__updateTruckStatus (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'UpdateTruckStatus',
      data: {
        Key: props.key,
        StatusKey: props.statusKey
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getReports (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetReports',
      data: {
        ReportTypes: get(props, 'reportTypes', '')
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
        if ('TypeKey' in item) item.TypeKey = Number(item.TypeKey);
        if ('DataAvailable' in item) item.DataAvailable = item.DataAvailable === 'true';
        if ('EmailAvailable' in item) item.EmailAvailable = item.EmailAvailable === 'true';
        if ('EmailToCustomersAvailable' in item) item.EmailToCustomersAvailable = item.EmailToCustomersAvailable === 'true';
      });
    })
    .success(props.success);
  },

  TOPSCOMPANY__getLocationServices (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetLocationServices',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: []
      }
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getCustomers (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetCustomers',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: []
      }
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getCallInspectionItems (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetInspectionItems',
      data: {}
    })
    .responseMiddleware(response => {
      forEach(response, item => {
        if (!isNumber(item.Key)) item.Key = Number(item.Key);
        if (!isNumber(item.ValueTypeKey)) item.ValueTypeKey = Number(item.ValueTypeKey);
        if (!isNumber(item.Order)) item.Order = Number(item.Order);
        if (!isBoolean(item.Required)) item.Required = item.Required === 'true';
        if (!isBoolean(item.Active)) item.Active = item.Active === 'true';

        forEach(item.PossibleValues, value => {
          if (!isBoolean(value.Active)) value.Active = value.Active === 'true';
        });
      });
    })
    .success(props.callback);
  },

  TOPSCOMPANY__getSystemDataTags (context, props) {
    new CacheableRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetSystemDataTags',
      data: {}
    })
    .success(props.success);
  },

  TOPSCOMPANY__setHasCallInspectionItems (context, props) {
    context.commit('SET_HAS_INSPECTION_ITEMS', props);
  },

  TOPSCOMPANY__getCallTools (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetCallTools',
      data: {}
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  TOPSCOMPANY__getSubterminals (context, props) {
    new StandardRequest(context, {
      noun: 'TOPSCompany',
      verb: 'GetSubterminals',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  }
};

const mutations = {
  SET_TOPS_COMPANY_SETTINGS (state, props = {}) {
    state.settings = props;
  },

  SET_HAS_INSPECTION_ITEMS (state, value) {
    set(state, 'hasCallInspectionItems', value);
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
