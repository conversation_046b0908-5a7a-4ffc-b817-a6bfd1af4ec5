import { get } from 'lodash';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  CUSTOMER__readBasic (context, props) {
    new StandardRequest(context, {
      noun: 'Customer',
      verb: 'ReadBasic',
      data: { lCustomerKey: props.customerKey }
    })
    .responseMiddleware(response => {
      response.Key = Number(response.Key);
      response.StatusKey = Number(response.StatusKey);
      response.TypeKey = Number(response.TypeKey);
    })
    .success(props.callback);
  },

  CUSTOMER__getTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'Customer',
      verb: 'GetTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  CUSTOMER__getStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'Customer',
      verb: 'GetStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  CUSTOMER__getTerms (context, props) {
    new CacheableRequest(context, {
      noun: 'Customer',
      verb: 'GetTerms',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  },

  CUSTOMER__getMotorClubs (context, props) {
    new CacheableRequest(context, {
      noun: 'Customer',
      verb: 'GetMotorClubs',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  },

  CUSTOMER__getBillingCustomers (context, props) {
    new CacheableRequest(context, {
      noun: 'Customer',
      verb: 'GetBillingCustomers',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  },

  CUSTOMER__testDuplicate (context, props) {
    new StandardRequest(context, {
      noun: 'Customer',
      verb: 'DoesNameCodeExist',
      data: {
        Name: props.name,
        ShortCode: props.shortcode
      }
    })
    .success(props.callback);
  },

  CUSTOMER__getBillingCalls (context, props) {
    new StandardRequest(context, {
      noun: 'Customer',
      verb: 'GetNumberOfCallsAsBillingCustomer',
      data: {
        Key: props.key
      }
    })
    .success(props.callback);
  },

  CUSTOMER__getRecordViewData (context, props) {
    new StandardRequest(context, {
      noun: 'Customer',
      verb: 'GetRecordViewData',
      data: {
        lCustomerKey: props.key
      }
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
