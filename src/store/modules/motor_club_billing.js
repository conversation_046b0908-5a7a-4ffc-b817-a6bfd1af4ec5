import StandardRequest from '@/api/StandardRequest.js';

const state = {
  selectedTab: 'calls',
  customers: [],

  invoiceFilter: {
    pay: '',
    customer: ''
  }
};

const getters = {};

const actions = {
  MCBILLING__sendInvoice (context, props) {
    new StandardRequest(context, {
      noun: 'MCBilling',
      verb: 'SendInvoice',
      data: {
        Key: props.key
      }
    })
    .responseMiddleware(response => {
      if ('CallKey' in response) {
        response.CallKey = Number(response.CallKey);
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  MCBILLING__getCustomers (context, props) {
    new StandardRequest(context, {
      noun: 'MCBilling',
      verb: 'GetCustomers',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  MCBILLING__previewInvoice (context, props) {
    new StandardRequest(context, {
      noun: 'MCBilling',
      verb: 'GetReview',
      data: {
        Key: props.key
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  MCBILLING__reviewInvoice (context, props) {
    new StandardRequest(context, {
      noun: 'MCBilling',
      verb: 'GetDataSent',
      data: {
        Key: props.key
      }
    })
    .success(props.success)
    .fail(props.fail);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
