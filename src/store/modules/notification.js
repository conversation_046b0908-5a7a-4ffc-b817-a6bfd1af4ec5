import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  NOTIFICATION__getNotifiableEmployees (context, props) {
    new CacheableRequest(context, {
      noun: 'Notification',
      verb: 'GetEmployeesForPaging',
      data: {}
    })
    .success(props.callback);
  },

  NOTIFICATION__getNotifiableTrucks (context, props) {
    new CacheableRequest(context, {
      noun: 'Notification',
      verb: 'GetTrucksForPaging',
      data: {}
    })
    .success(props.callback);
  },

  NOTIFICATION__send (context, props) {
    new StandardRequest(context, {
      noun: 'Notification',
      verb: 'SendPage',
      data: props.data
    })
    .success(props.callback);
  },

  NOTIFICATION__getTemplateFields (context, props) {
    new CacheableRequest(context, {
      noun: 'Notification',
      verb: 'GetPagingTemplateFields',
      data: {}
    })
    .success(props.callback);
  },

  NOTIFICATION__getCallText (context, props) {
    new StandardRequest(context, {
      noun: 'Notification',
      verb: 'GetPagingTextForCall',
      data: { Key: props.callKey }
    })
    .success(props.callback);
  },

  NOTIFICATION__getTemplate (context, props) {
    new StandardRequest(context, {
      noun: 'Notification',
      verb: 'GetPagingTemplate',
      data: {}
    })
    .success(props.callback);
  },

  NOTIFICATION__getDispatchableDriverTruck (context, props) {
    new StandardRequest(context, {
      noun: 'Notification',
      verb: 'GetDriverTruckForDispatch',
      data: { Key: props.dispatchKey }
    })
    .success(props.callback);
  },

  NOTIFICATION__pushToTopic (context, props) {
    new StandardRequest(context, {
      noun: 'Notification',
      verb: 'PublishToTopic',
      data: {
        ObjectName: 'TOPSCall',
        Function: 'Create',
        OrgUnitKey: '1288',
        Key: '9999',
        StatusKey: '109'
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
