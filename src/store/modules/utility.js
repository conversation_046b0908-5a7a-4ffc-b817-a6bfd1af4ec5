import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  UTILITY__getCallToDetach (context, props) {
    new StandardRequest(context, {
      noun: 'Utilities',
      verb: 'GetCallInfoForDetachFromMotorClub',
      data: {
        OrgUnitKey: props.locationKey,
        Key: props.callKey
      }
    })
    .success(props.callback);
  },

  UTILITY__detachCall (context, props) {
    new StandardRequest(context, {
      noun: 'Utilities',
      verb: 'DetachCallFromMotorClub',
      data: {
        OrgUnitKey: props.locationKey,
        Key: props.callKey
      }
    })
    .success(props.callback);
  },

  UTILITY__getTowLienSettingsForOrg (context, props) {
    new StandardRequest(context, {
      noun: 'Utilities',
      verb: 'GetTowLienSettingsForOrg',
      data: {
        Key: props.locationKey
      }
    })
    .success(props.callback);
  },
  UTILITY__setTowLienCredentialsForOrg (context, props) {
    new StandardRequest(context, {
      noun: 'Utilities',
      verb: 'SetTowLienCredentialsForOrg',
      data: {
        Key: props.locationKey,
        sUserID: props.userId,
        sPassword: props.password
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
