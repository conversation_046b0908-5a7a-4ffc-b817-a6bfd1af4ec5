import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  TOWSTATUS__requestLocation (context, props) {
    new StandardRequest(context, {
      noun: 'TowStatus',
      verb: 'SendLocationRequest',
      data: {
        OrgUnitKey: props.companyKey,
        SubterminalKey: props.subcompanyKey,
        PhoneNum: props.phone
      }
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOWSTATUS__getRequestedLocation (context, props) {
    new StandardRequest(context, {
      noun: 'TowStatus',
      verb: 'GetLocationResponse',
      data: {
        Key: props.requestKey
      }
    })
    .responseMiddleware(response => {
      if ('Latitude' in response) response.Latitude = Number(response.Latitude);
      if ('Longitude' in response) response.Longitude = Number(response.Longitude);
    })
    .success(props.success)
    .fail(props.fail);
  },

  TOWSTATUS__sendUpdateRequest (context, props) {
    new StandardRequest(context, {
      noun: 'TowStatus',
      verb: 'SendUpdateRequest',
      data: {
        OrgUnitKey: props.companyKey,
        SubterminalKey: props.subcompanyKey,
        Key: props.callKey,
        PhoneNum: props.phone
      }
    })
    .success(props.success)
    .fail(props.fail);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
