import Hub from '../../events/hub';
import { EVENT_ROLE_CHANGED } from '@/config.js';

const state = {
  Key: '',
  Name: '',
  Rights: []
};

const getters = {
  ROLE__state: state => state,
  ROLE__hasAccess: (state) => (right) => {
    return state.Rights.indexOf(right) > -1;
  }
};

const mutations = {
  SET_ROLE_DATA (state, props) {
    state.Key = props.Key;
    state.Name = props.Name;
    state.Rights = (props.Rights === undefined) ? [] : props.Rights.slice();

    Hub.$emit(EVENT_ROLE_CHANGED);
  },

  CLEAR_ROLE_DATA (state) {
    state.Key = '';
    state.Name = '';
    state.Rights = [];
    Hub.$emit(EVENT_ROLE_CHANGED);
  }
};

export default {
  state,
  getters,
  mutations
};
