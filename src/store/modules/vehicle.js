import CacheableRequest from '@/api/CacheableRequest.js';

/**
 * Todo
 * Started this module but didn't finish it. The goal is to
 * hydrate the makes and models of vehicles up front
 * and refactor form inputs to use these values.
 */

const state = {
  makes: [],
  models: []
};

const getters = {};

const actions = {
  VEHICLE__hydrateMakesAndModels (context, props) {
    context.dispatch('VEHICLE__getMakes', {
      success: response => {
        context.state.makes = response;
      }
    });

    context.dispatch('VEHICLE__getModels', {
      success: response => {
        context.state.models = response;
      }
    });
  },

  VEHICLE__getModels (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetAllModels',
      data: {}
    })
    .responseMiddleware(response => {
      response.forEach(model => {
        model.Key = Number(model.Key);
        model.Make = Number(model.Make);
      });
    })
    .success(props.success);
  },

  VEHICLE__getMakes (context, props) {
    new CacheableRequest(context, {
      noun: 'Call',
      verb: 'GetMakes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(model => {
        model.Key = Number(model.Key);
      });
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
