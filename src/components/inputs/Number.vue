<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <input
      type="number"
      :class="controlClasses"
      v-model.number="valueProxy"
      :placeholder="placeholder"
      :disabled="disabled"
      :required="required"
      :readonly="readonly"
      :tabindex="tabindex"
      autocomplete="off"
      :min="min"
      :max="max"
      @click="emit('click')"
      @keyup.prevent="onKeyup"
      @change="emit('change')"
      @focus="emit('focus')"
      @blur="emit('blur')">
  </label>
</p>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'number-control',

  extends: Control,

  props: {
    min: { default: null },
    max: { default: null }
  },

  methods: {
    onKeyup () {
      const _value = Number(this.valueProxy);

      if (this.min !== null && _value < this.min) {
        this.valueProxy = this.min;
      }

      if (this.max !== null && _value > this.max) {
        this.valueProxy = this.max;
      }

      this.emit('keyup');
    }
  }
};
</script>
