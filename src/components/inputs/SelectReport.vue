<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <span class="select">
      <select
        :id="id"
        v-model="value"
        @change="emit('change')"
        @focus="emit('focus')"
        @blur="emit('blur')"
        @click="emit('click')"
        :readonly="readonly"
        :required="required"
        :disabled="disabled"
        :placeholder="placeholder"
        :tabindex="tabindex">
        <option v-if="showAll" value="-1">All</option>
        <option v-for="(option, index) in options" :value="option[keyAlias]" :key="index">{{ option[valueAlias] }}</option>
      </select>
    </span>
  </label>
</p>
</template>

<script>
import Select from '@/components/inputs/Select.vue';

export default {
  name: 'select-report',

  extends: Select,

  props: {
    showAll: { type: Boolean, default: true }
  }
};
</script>
