<template>
<p class="control">
  <label :tabindex="tabindex" :class="{ '-row': orient === 'row' }">
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <input
      type="checkbox"
      :id="id"
      :class="controlClasses"
      v-model="valueProxy"
      :disabled="disabled"
      :tabindex="tabindex"
      @focus="emit('focus')"
      @blur="emit('blur')"
      @click="emit('click')"
      @change="emit('change')">
  </label>
</p>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'checkbox-control',

  extends: Control,

  props: {
    translateTrue: { default: true },
    translateFalse: { default: false },
    orient: { default: false }
  },

  data () {
    return {
      controlType: 'checkbox'
    };
  },

  computed: {
    valueProxy: {
      get () {
        return this.value === this.translateTrue;
      },
      set (value) {
        this.$emit('input', this.formatValue(value));
      }
    }
  },

  methods: {
    formatValue (value) {
      return value
        ? this.translateTrue
        : this.translateFalse;
    }
  }
};
</script>
