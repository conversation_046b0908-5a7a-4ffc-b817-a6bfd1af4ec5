<template>
  <p class="control">
    <label>
      <div class="-label"><slot></slot><span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span></div>
      <input
        type="text"
        :id="id"
        :class="controlClasses"
        ref="control"
        v-model="userIdProxy"
        :placeholder="placeholder"
        :disabled="disabled"
        :required="required"
        :readonly="true"
        :tabindex="tabindex"
        :autocomplete="autocomplete">
    </label>
  </p>
  </template>

  <script>
  import { mapActions } from 'vuex';
  import Control from '@/components/ancestors/Control.vue';

  export default {
    name: 'user-id-control',

    extends: Control,

    data () {
      return {
        userId: ''
      };
    },

    computed: {
      userIdProxy () {
        return this.$data.userId || '';
      }
    },

    watch: {
      valueProxy: {
        async handler (value) {
          if (value) {
            this.$data.userId = await this.getUserId(value);
          } else {
            this.$data.userId = '';
          }
        },
        immediate: true
      }
    },

    methods: {
      ...mapActions([
        'USER__getID'
      ]),

      getUserId (value) {
        return new Promise(resolve => {
          this.USER__getID({
            keys: value,
            success: response => {
              resolve(response[0].Value);
            }
          });
        });
      }
    }
  };
  </script>
