<template>
<label class="control" :tabindex="tabindex">
  <span>
    <slot></slot>
    <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
  </span>

  <div
    style="overflow-wrap: break-word; hyphens: auto;"
    :class="controlClasses"
    @blur="onInput"
    ref="control"
    :contenteditable="!disabled">
    {{ valueProxy }}
  </div>

  <input
    type="hidden"
    v-model="valueProxy"
    :id="id"
    :maxlength="maxlength"
    :disabled="disabled"
    :tabindex="tabindex"
    autocomplete="off"
    @focus="emit('focus')"
    @blur="emit('blur')"
    @click="emit('click')"
    @change="emit('change')"
    @keyup.ctrl.alt.e.prevent="setValue('Empty')"
    @keyup.ctrl.alt.n.prevent="setValue('Not Empty')">
</label>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'textarea-control',

  extends: Control,

  data () {
    return {
      controlType: 'textarea-why-are-random-things-happening-we-just-need-to-start-clean'
    };
  },

  watch: {
    valueProxy (value) {
      this.$refs.control.innerText = value;
    }
  },

  methods: {
    onInput () {
      this.valueProxy = this.$refs.control.innerText.trim();
    }
  }
};
</script>
