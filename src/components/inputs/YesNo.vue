<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
  </label>
  <span :class="controlClasses">
    <select
      :id="id"
      v-model="valueProxy"
      @change="emit('change')"
      @focus="emit('focus')"
      @blur="emit('blur')"
      @click="emit('click')"
      :class="controlClasses"
      :readonly="readonly"
      :required="required"
      :disabled="disabled"
      :tabindex="tabindex"
      :autofocus="autofocus">
      <option v-if="emptyOption" value=""></option>
      <option v-if="placeholder" value="">{{ placeholder }}</option>
      <option value="Yes">Yes</option>
      <option value="No">No</option>
    </select>
  </span>
</p>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'truefalse-control',

  extends: Control,

  props: {
    emptyOption: { default: true }
  }
};
</script>
