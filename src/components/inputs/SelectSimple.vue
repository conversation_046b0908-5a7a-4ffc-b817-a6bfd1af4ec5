<template>
<p class="control">
  <label class="app-select">
    <div class="-label"><slot></slot><span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span></div>
    <span :class="controlClasses">
      <select
        :id="id"
        v-model="valueProxy"
        @change="emit('change')"
        @focus="emit('focus')"
        @blur="emit('blur')"
        @click="emit('click')"
        :readonly="readonly"
        :required="required"
        :disabled="disabled"
        :tabindex="tabindex"
        :autofocus="autofocus"
        autocomplete="off">
        <option v-if="placeholder" value="">{{ placeholder }}</option>
        <option v-for="(option, index) in options" :value="option" :key="index">{{ option }}</option>
      </select>
    </span>
  </label>
</p>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'select-simple-control',

  extends: Control,

  props: {
    options: { type: Array, required: true }
  },

  data () {
    return {
      controlType: 'select'
    };
  }
};
</script>
