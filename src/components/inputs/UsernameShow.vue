<template>
  <span>{{ username }}</span>
</template>

<script>
export default {
  name: 'username-show',

  props: {
    userKey: {
      type: [String, Number],
      default: ''
    }
  },

  data () {
    return {
      username: ''
    };
  },

  watch: {
    userKey: {
      immediate: true,
      handler () {
        this.getUsername();
      }
    }
  },

  methods: {
    getUsername () {
      if (!this.userKey) return;
      if (this.username) return this.username;

      this.$store.dispatch('USER__getID', {
        keys: this.userKey,
        success: response => {
          this.$data.username = response[0].Value;
        }
      });
    }
  }
};
</script>
