<template>
  <app-shortcode
    id="CAL_lMakeKey"
    v-model="valueProxy"
    :options="$store.state.vehicle.makes"
    keyAlias="Key"
    valueAlias="Value"
    shortCodeAlias="ShortCode">
    Make
  </app-shortcode>
</template>

<script>
export default {
  name: 'vehicle-make-control',

  props: {
    value: { required: true }
  },

  computed: {
    valueProxy: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  }
};
</script>
