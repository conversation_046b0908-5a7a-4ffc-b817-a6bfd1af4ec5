<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>

    <input
      type="text"
      :id="id"
      :class="controlClasses"
      ref="control"
      v-model.trim="username"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
      :required="required"
      :readonly="true"
      :tabindex="tabindex"
      :autocomplete="autocomplete">
  </label>
</p>
</template>

<script>
import { mapActions } from 'vuex';
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'username-control',

  extends: Control,

  data () {
    return {
      username: ''
    };
  },

  watch: {
    value () {
      this.getUsername();
    }
  },

  methods: {
    ...mapActions([
      'USER__getID'
    ]),

    getUsername () {
      if (!this.value) return;

      this.USER__getID({
        keys: this.value,
        success: response => {
          this.$data.username = response[0].Value;
        }
      });
    }
  }
};
</script>
