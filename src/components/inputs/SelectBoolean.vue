<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <span class="select">
      <select :id="id">
        <option value=""></option>
        <option value="True">True</option>
        <option value="False">False</option>
      </select>
    </span>
  </label>
</p>
</template>

<script>
export default {
  name: 'app-select-boolean',

  props: {
    id: { type: String, required: false },
    required: { default: false }
  },

  computed: {
    idProxy () {
      return this.id || this.$_.uniqueId('select_');
    }
  }
};
</script>
