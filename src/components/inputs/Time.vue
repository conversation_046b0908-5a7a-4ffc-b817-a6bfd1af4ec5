<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <div class="time-control">
      <input
        type="text"
        class="input"
        v-model.trim="valueProxy"
        :id="id"
        :placeholder="placeholder"
        :disabled="disabled"
        :required="required"
        :readonly="readonly"
        :tabindex="tabindex"
        ref="control"
        autocomplete="off"
        @keyup="emit('keyup')"
        @focus="emit('focus')"
        @blur="emit('blur')"
        @click="emit('click')"
        @change="emit('change')"
        @keyup.ctrl.alt.e.prevent="setValue('Empty')"
        @keyup.ctrl.alt.n.prevent="setValue('Not Empty')">
      <a class="button is-transparent" @click="getNow" tabindex="-1"><i class="fal fa-clock"></i></a>
    </div>
  </label>
</p>
</template>

<script>
import { format } from 'date-fns';
import dateTimeControl from './DateTime.vue';

export default {
  name: 'time-control',

  extends: dateTimeControl,

  data () {
    return {
      hasDatePicker: false
    };
  },

  methods: {
    formatValue (value) {
      return format(value, 'HH:mm:ss');
    }
  }
};
</script>
