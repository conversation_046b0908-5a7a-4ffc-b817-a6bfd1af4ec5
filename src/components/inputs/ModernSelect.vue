<template>
  <div class="modern-select">
    <label v-if="label" :for="inputId" class="select-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>
    
    <div class="select-wrapper" :class="{ 'has-error': hasError, 'is-loading': isLoading }">
      <select 
        :id="inputId"
        :value="value"
        @change="handleChange"
        :disabled="disabled || isLoading"
        class="select-input"
      >
        <option value="" v-if="placeholder">{{ placeholder }}</option>
        <option 
          v-for="option in selectOptions" 
          :key="option.value" 
          :value="option.value"
        >
          {{ option.label }}
        </option>
      </select>
      
      <div v-if="isLoading" class="loading-indicator">
        <i class="fal fa-spinner fa-spin"></i>
      </div>
    </div>
    
    <div v-if="hasError" class="error-message">
      {{ error }}
    </div>
    
    <div v-if="helpText && !hasError" class="help-text">
      {{ helpText }}
    </div>
  </div>
</template>

<script>
import { computed, ref } from '@vue/composition-api';
import { useOptions } from '@/composables';

export default {
  name: 'ModernSelect',
  
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    noun: {
      type: String,
      required: true
    },
    verb: {
      type: String,
      default: 'GetAll'
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: 'Select an option...'
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    helpText: {
      type: String,
      default: ''
    },
    filterActive: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['input', 'change'],
  
  setup(props, { emit }) {
    // Generate unique ID for accessibility
    const inputId = ref(`modern-select-${Math.random().toString(36).substr(2, 9)}`);
    
    // Use the options composable
    const {
      optionsForSelect,
      isLoading,
      error: optionsError,
      loadOptions
    } = useOptions({
      noun: props.noun,
      verb: props.verb,
      filterActive: props.filterActive,
      autoLoad: true
    });
    
    // Computed properties
    const hasError = computed(() => {
      return !!(props.error || optionsError.value);
    });
    
    const selectOptions = computed(() => {
      return optionsForSelect.value;
    });
    
    // Methods
    const handleChange = (event) => {
      const newValue = event.target.value;
      emit('input', newValue);
      emit('change', newValue);
    };
    
    // Expose methods for parent components
    const refresh = () => {
      return loadOptions();
    };
    
    return {
      inputId,
      selectOptions,
      isLoading,
      hasError,
      handleChange,
      refresh
    };
  }
};
</script>

<style scoped>
.modern-select {
  margin-bottom: 1rem;
}

.select-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.required {
  color: #ef4444;
}

.select-wrapper {
  position: relative;
}

.select-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #374151;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.select-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select-input:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.has-error .select-input {
  border-color: #ef4444;
}

.has-error .select-input:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.is-loading .select-input {
  padding-right: 2.5rem;
}

.loading-indicator {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.error-message {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #ef4444;
}

.help-text {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}
</style>
