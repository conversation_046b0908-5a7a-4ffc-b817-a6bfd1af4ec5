<template>
  <app-shortcode
    v-model.number="$value"
    :options="activeOptions"
    :disabled="!canEdit"
    :required="required"
    :uuid="uuid">
    Tow Type
  </app-shortcode>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'InputTowType',

  extends: Control,

  props: {
    value: {
      type: [Number, String, null],
      required: true
    }
  },

  data () {
    return {
      uuid: this.$_.uniqueId('towtype-'),
      options: []
    };
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        const _value = [null, undefined].includes(value) ? '' : value;
        this.$emit('input', _value);
      }
    },

    activeOptions () {
      if (this.context === 'search') {
        return this.options;
      }

      return this.options.filter(option => option.Active || option.Key === Number(this.value));
    },

    canEdit () {
      if (this.context === 'search') {
        return true;
      }

      if (this.context === 'active') {
        return true;
      }

      const selectedOption = this.options.find(option => option.Key === Number(this.value));
      if (Number(selectedOption) > 0) {
        return selectedOption.Active && !this.disabled;
      }

      return !this.disabled;
    }
  },

  methods: {
    async fetchTowTypes () {
      this.$store.dispatch('CALL__getTowTypes', {
        callback: response => {
          this.options = response;
        }
      });
    }
  },

  async mounted () {
    this.fetchTowTypes();
  }
};
</script>
