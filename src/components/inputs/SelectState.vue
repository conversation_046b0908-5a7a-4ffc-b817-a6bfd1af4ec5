<template>
  <app-select
    v-model="valueProxy"
    :id="id"
    :options="states"
    :readonly="readonly"
    :required="required"
    :disabled="disabled"
    :tabindex="tabindex"
    :autofocus="autofocus"
    @change="$emit('change')"
    @focus="$emit('focus')"
    @blur="$emit('blur')"
    @click="$emit('click')"
    keyAlias="Key"
    valueAlias="Key">
    <slot></slot>
  </app-select>
</template>

<script>
export default {
  name: 'select-state-control',

  props: {
    id: { default: null },
    value: {},
    autofocus: { default: false },
    tabindex: { default: false },
    disabled: { default: false },
    required: { default: false },
    readonly: { default: false }
  },

  data () {
    return {
      states: []
    };
  },

  computed: {
    valueProxy: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  },

  mounted () {
    this.$store.dispatch('__getStates', {
      success: states => {
        this.$data.states = states;
      }
    });
  }
};
</script>
