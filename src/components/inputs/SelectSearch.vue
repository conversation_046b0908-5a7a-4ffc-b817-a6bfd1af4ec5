<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <span class="select">
      <select
        v-model="valueProxy"
        :id="id"
        @change="emit('change')"
        @focus="emit('focus')"
        @blur="emit('blur')"
        @click="emit('click')">
        <option value=""></option>
        <option value="Empty">Is Empty</option>
        <option value="Not Empty">Is Not Empty</option>
        <option disabled="disabled">---</option>
        <option v-for="(option, index) in options" :value="option[valueAlias]" :key="index">{{ option[valueAlias] }}</option>
      </select>
    </span>
  </label>
</p>
</template>

<script>
import Select from '@/components/inputs/Select.vue';

export default {
  name: 'app-select-search',

  extends: Select
};
</script>
