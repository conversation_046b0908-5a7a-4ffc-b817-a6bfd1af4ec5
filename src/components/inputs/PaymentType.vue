<template>
  <component
    :is="context === 'search' ? 'app-select-search' : 'app-select'"
    v-model="valueProxy"
    :options="options"
    :disabled="disabled"
    :tabindex="tabindex"
    @focus="emit('focus')"
    @blur="emit('blur')"
    @click="emit('click')"
    @change="emit('change')"
    keyAlias="Key"
    valueAlias="Value">
    {{ label }} <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
  </component>
</template>

<script>
import { VALUE_ID } from '@/config';
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'input-payment-type',

  extends: Control,

  props: {
    label: { type: String, default: 'Payment Type' },
    value: { type: [Number, String], default: null }
  },

  data () {
    return {
      options: []
    };
  },

  computed: {
    valueProxy: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  },

  methods: {
    async fetchOptions () {
      this.$store.dispatch('PAYMENT__getPaymentTypes', {
        callback: response => {
          this.options = response.filter(option => option.Key !== VALUE_ID.paymentType.arReceivable);
        }
      });
    }
  },

  mounted () {
    this.fetchOptions();
  }
};
</script>
