import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createLocalVue } from '@vue/test-utils';
import CompositionApi from '@vue/composition-api';
import ModernSelect from '../ModernSelect.vue';

// Mock the composables
vi.mock('@/composables', () => ({
  useOptions: vi.fn(() => ({
    optionsForSelect: { value: [
      { value: '1', label: 'Option 1' },
      { value: '2', label: 'Option 2' },
      { value: '3', label: 'Option 3' }
    ]},
    isLoading: { value: false },
    error: { value: null },
    loadOptions: vi.fn()
  }))
}));

const localVue = createLocalVue();
localVue.use(CompositionApi);

describe('ModernSelect', () => {
  let wrapper;

  const defaultProps = {
    noun: 'TestNoun',
    label: 'Test Select',
    value: ''
  };

  beforeEach(() => {
    wrapper = mount(ModernSelect, {
      localVue,
      propsData: defaultProps
    });
  });

  it('renders correctly', () => {
    expect(wrapper.find('.modern-select').exists()).toBe(true);
    expect(wrapper.find('.select-label').text()).toBe('Test Select');
    expect(wrapper.find('.select-input').exists()).toBe(true);
  });

  it('displays options correctly', () => {
    const options = wrapper.findAll('option');
    
    // Should have placeholder + 3 options
    expect(options).toHaveLength(4);
    expect(options.at(0).text()).toBe('Select an option...');
    expect(options.at(1).text()).toBe('Option 1');
    expect(options.at(2).text()).toBe('Option 2');
    expect(options.at(3).text()).toBe('Option 3');
  });

  it('emits input and change events when value changes', async () => {
    const select = wrapper.find('.select-input');
    
    await select.setValue('2');
    
    expect(wrapper.emitted('input')).toBeTruthy();
    expect(wrapper.emitted('change')).toBeTruthy();
    expect(wrapper.emitted('input')[0]).toEqual(['2']);
    expect(wrapper.emitted('change')[0]).toEqual(['2']);
  });

  it('shows required indicator when required prop is true', async () => {
    await wrapper.setProps({ required: true });
    
    expect(wrapper.find('.required').exists()).toBe(true);
    expect(wrapper.find('.required').text()).toBe('*');
  });

  it('shows error message when error prop is provided', async () => {
    await wrapper.setProps({ error: 'This field is required' });
    
    expect(wrapper.find('.error-message').exists()).toBe(true);
    expect(wrapper.find('.error-message').text()).toBe('This field is required');
    expect(wrapper.find('.select-wrapper').classes()).toContain('has-error');
  });

  it('shows help text when provided and no error', async () => {
    await wrapper.setProps({ helpText: 'Choose an option from the list' });
    
    expect(wrapper.find('.help-text').exists()).toBe(true);
    expect(wrapper.find('.help-text').text()).toBe('Choose an option from the list');
  });

  it('hides help text when error is present', async () => {
    await wrapper.setProps({ 
      helpText: 'Choose an option from the list',
      error: 'This field is required'
    });
    
    expect(wrapper.find('.help-text').exists()).toBe(false);
    expect(wrapper.find('.error-message').exists()).toBe(true);
  });

  it('disables select when disabled prop is true', async () => {
    await wrapper.setProps({ disabled: true });
    
    const select = wrapper.find('.select-input');
    expect(select.attributes('disabled')).toBeDefined();
  });

  it('shows custom placeholder when provided', async () => {
    await wrapper.setProps({ placeholder: 'Custom placeholder' });
    
    const firstOption = wrapper.find('option');
    expect(firstOption.text()).toBe('Custom placeholder');
  });
});
