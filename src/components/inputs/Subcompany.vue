<template>
  <p class="control">
    <label class="app-select">
      <div class="-label">
        <slot></slot>
        <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
      </div>

      <span :class="controlClasses">
        <select
          :id="id"
          v-model="valueProxy"
          @change="emit('change')"
          @focus="emit('focus')"
          @blur="emit('blur')"
          @click="emit('click')"
          :readonly="readonly"
          :required="required"
          :disabled="disabled"
          :tabindex="tabindex"
          :autofocus="autofocus"
          autocomplete="off">
          <option v-for="subcompany in subcompanies" :value="subcompany.Key" :key="subcompany.Key">
            {{ subcompany.Value }}
          </option>
        </select>
      </span>
    </label>
  </p>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'subcompany-control',

  extends: Control,

  data () {
    return {
      controlType: 'select',
      subcompanies: []
    };
  },

  methods: {
    getSubcompanies () {
      return new Promise(resolve => {
        this.$store.dispatch('CALL__getSubterminals', {
          callback: response => { resolve(response); }
        });
      });
    }
  },

  async mounted () {
    this.$data.subcompanies = await this.getSubcompanies();
  }
};
</script>
