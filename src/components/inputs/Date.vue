<template>
<p class="control">
  <label>
    <div class="-label">
      <slot></slot>
      <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>
    <div class="date-control">
      <input
        type="text"
        :id="id"
        ref="control"
        class="input"
        v-model.trim="valueProxy"
        :placeholder="placeholder"
        :disabled="disabled"
        :required="required"
        :readonly="readonly"
        :tabindex="tabindex"
        autocomplete="off"
        @keyup="emit('keyup')"
        @focus="emit('focus')"
        @blur="emit('blur')"
        @click="emit('click')"
        @keyup.ctrl.alt.e.prevent="setValue('Empty')"
        @keyup.ctrl.alt.n.prevent="setValue('Not Empty')">
      <a class="button is-transparent" @click.prevent.stop="getNow" tabindex="-1">
        <i class="fal fa-clock"></i>
      </a>
    </div>
  </label>
</p>
</template>

<script>
import Pikaday from 'pikaday';
import { format } from 'date-fns';
import dateTimeControl from './DateTime.vue';

export default {
  name: 'date-control',

  extends: dateTimeControl,

  methods: {
    formatValue (value) {
      return format(value, 'M/D/YYYY');
    },

    initializeDatePicker () {
      if (!this.datePicker) return;

      this.$data.picker = new Pikaday({
        field: this.$refs.control,
        format: 'MM/DD/YYYY',
        theme: 'txi',
        keyboardInput: false
      });
    }
  }
};
</script>
