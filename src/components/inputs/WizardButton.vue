<template>
  <button class="wizard__button" :data-active="active" :data-flavor="flavor" @click="$emit('click')">
    <div>
      <slot></slot>

      <div class="_meta">
        <slot name="meta"></slot>
      </div>
    </div>

    <i :class="iconCss" v-if="icon"></i>
  </button>
</template>

<script>
export default {
  name: 'wizard-button',

  props: {
    active: { default: false },
    busy: { default: false },
    flavor: { default: '' },
    icon: { default: true }
  },

  computed: {
    iconCss () {
      return {
        '_icon': true,
        'far': true,
        'fa-arrow-right': !this.active,
        'fa-times': this.active,
        'fa-spinner-third fa-spin': this.busy
      };
    }
  }
};
</script>

<style scoped>
.wizard__button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;

  padding: 1rem;
  margin-bottom: 0.5rem;
  width: 100%;
  font-weight: bold;
  color: var(--input-fg);
  background-color: hsla(var(--pure-gray-h), var(--pure-gray-s), var(--pure-gray-l), 0.2);
  text-align: left;
  border: none;
  border-radius: var(--border-radius-100);
  outline: 0;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;

  ._meta {
    &:empty {
      display: none;
    }

    margin-top: 0.5em;
    font-weight: normal;
    font-size: var(--font-size-small1);
    letter-spacing: .02em;
  }

  &:hover {
    background-color: hsla(var(--pure-gray-h), var(--pure-gray-s), var(--pure-gray-l), 0.3);
  }

  &[data-active] {
    background-color: var(--selected-background);
  }

  &[data-flavor="default"] {
    justify-content: center;
  }

  &[data-flavor="primary"] {
    justify-content: center;
    color: white;
    background-color: var(--pure-blue);

    &:hover {
      background-color: hsl(var(--pure-blue-h), var(--pure-blue-s), calc(var(--pure-blue-l) + 5%));
    }
  }

  &:disabled {
    opacity: 0.5;
    transform: scale(0.97);
  }
}
</style>
