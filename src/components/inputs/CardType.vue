<template>
  <component
    :is="context === 'search' ? 'app-select-search' : 'app-select'"
    v-model="valueProxy"
    :options="options"
    :disabled="disabled"
    :tabindex="tabindex"
    @focus="emit('focus')"
    @blur="emit('blur')"
    @click="emit('click')"
    @change="emit('change')"
    keyAlias="Key"
    valueAlias="Value">
    {{ label }}
  </component>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'input-card-type',

  extends: Control,

  props: {
    label: { type: String, default: 'Card Type' },
    value: { type: [Number, String], default: null }
  },

  data () {
    return {
      options: []
    };
  },

  computed: {
    valueProxy: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  },

  methods: {
    async fetchOptions () {
      this.$store.dispatch('PAYMENT__getCreditCardTypes', {
        callback: response => {
          this.options = response;
        }
      });
    }
  },

  mounted () {
    this.fetchOptions();
  }
};
</script>
