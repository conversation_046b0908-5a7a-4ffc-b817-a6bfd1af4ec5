<template>
  <div class="composables-demo">
    <app-titlebar title="Composables Demo"></app-titlebar>

    <div class="demo-sections">
      <!-- API Demo -->
      <section class="demo-section">
        <h3>useApi Demo</h3>
        <div class="demo-content">
          <app-button @click="loadApiData" :disabled="apiLoading">
            {{ apiLoading ? 'Loading...' : 'Load Data' }}
          </app-button>

          <div v-if="apiError" class="error">
            Error: {{ apiError }}
          </div>

          <div v-if="apiData" class="success">
            Data loaded successfully! Records: {{ Array.isArray(apiData) ? apiData.length : 'N/A' }}
          </div>
        </div>
      </section>

      <!-- Form Demo -->
      <section class="demo-section">
        <h3>useForm Demo</h3>
        <div class="demo-content">
          <form @submit.prevent="handleFormSubmit">
            <div class="form-group">
              <label>Name:</label>
              <app-text
                :value="formData.name"
                @input="updateField('name', $event)"
                :error="getFieldError('name')"
              />
            </div>

            <div class="form-group">
              <label>Email:</label>
              <app-text
                :value="formData.email"
                @input="updateField('email', $event)"
                :error="getFieldError('email')"
              />
            </div>

            <div class="form-actions">
              <app-button type="primary" :disabled="!isFormValid || formSubmitting">
                {{ formSubmitting ? 'Submitting...' : 'Submit' }}
              </app-button>
              <app-button @click="resetForm">Reset</app-button>
            </div>

            <div v-if="formHasErrors" class="error">
              Form has errors. Please check the fields above.
            </div>
          </form>
        </div>
      </section>

      <!-- Navigation Demo -->
      <section class="demo-section">
        <h3>useNavigation Demo</h3>
        <div class="demo-content">
          <app-button @click="navigateToWelcome">Go to Welcome</app-button>
          <app-button @click="goBackInHistory">Go Back</app-button>

          <div class="current-route">
            Current Route: {{ currentRoute.name || 'Unknown' }}
          </div>
        </div>
      </section>

      <!-- Modern Select Demo -->
      <section class="demo-section">
        <h3>Modern Select Demo (useOptions)</h3>
        <div class="demo-content">
          <ModernSelect
            noun="TowType"
            label="Tow Type"
            placeholder="Select a tow type..."
            :value="selectedTowType"
            @input="selectedTowType = $event"
            help-text="This demonstrates the useOptions composable with a modern select component"
          />

          <div v-if="selectedTowType" class="selected-value">
            Selected Tow Type: {{ selectedTowType }}
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { ref } from '@vue/composition-api';
import { useApi, useForm, useNavigation } from '@/composables';
import ModernSelect from '@/components/inputs/ModernSelect.vue';

export default {
  name: 'ComposablesDemo',

  components: {
    ModernSelect
  },

  setup() {
    // API composable demo
    const {
      request: apiRequest,
      loading: apiLoading,
      error: apiError,
      data: apiData
    } = useApi();

    const loadApiData = async () => {
      try {
        await apiRequest({
          noun: 'Customer', // This will use existing API patterns
          verb: 'GetAll',
          data: { PageSize: 10 }
        });
      } catch (err) {
        console.error('Demo API error:', err);
      }
    };

    // Form composable demo
    const {
      formData,
      updateField,
      reset: resetForm,
      hasErrors: formHasErrors,
      isValid: isFormValid,
      getFieldError,
      setSubmitting,
      submitting: formSubmitting
    } = useForm({
      name: '',
      email: ''
    });

    const handleFormSubmit = async () => {
      setSubmitting(true);

      // Simulate form submission
      setTimeout(() => {
        console.log('Form submitted:', formData);
        setSubmitting(false);
        resetForm();
      }, 1000);
    };

    // Navigation composable demo
    const {
      canNavigateTo,
      currentRoute,
      setCurrentRoute
    } = useNavigation();

    // Set current route info for demo
    setCurrentRoute('ComposablesDemo');

    // Modern Select demo
    const selectedTowType = ref('');

    return {
      // API demo
      loadApiData,
      apiLoading,
      apiError,
      apiData,

      // Form demo
      formData,
      updateField,
      resetForm,
      handleFormSubmit,
      formHasErrors,
      isFormValid,
      getFieldError,
      formSubmitting,

      // Navigation demo
      canNavigateTo,
      currentRoute,

      // Modern Select demo
      selectedTowType
    };
  },

  methods: {
    // Traditional navigation methods for Vue 2 compatibility
    navigateToWelcome() {
      this.$router.push({ name: 'Welcome' });
    },

    goBackInHistory() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped>
.composables-demo {
  padding: 20px;
}

.demo-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.demo-section {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.demo-section h3 {
  margin-top: 0;
  color: #333;
}

.demo-content {
  margin-top: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.error {
  color: #e74c3c;
  margin-top: 10px;
  padding: 10px;
  background-color: #fdf2f2;
  border-radius: 4px;
}

.success {
  color: #27ae60;
  margin-top: 10px;
  padding: 10px;
  background-color: #f2fdf2;
  border-radius: 4px;
}

.current-route {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-family: monospace;
}

.selected-value {
  margin-top: 10px;
  padding: 10px;
  background-color: #e3f2fd;
  border-radius: 4px;
  color: #1976d2;
  font-weight: bold;
}
</style>
