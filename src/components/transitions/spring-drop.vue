<template>
  <transition
    name="very-special-transition"
    mode="out-in"
    @enter="enter"
    @before-leave="leave">
    <slot></slot>
  </transition>
</template>

<script>
export default {
  name: 'springy-drop',

  props: {
    duration: { default: 0.2 }
  },

  methods: {
    enter (element) {
      let timeline = this.$gsap.timeline({
        defaults: {
          duration: this.duration,
          stagger: 0.05
        }
      });

      timeline
        .set(element, {
          autoAlpha: 0,
          scaleX: 1.0,
          scaleY: 0.1
        })
        .to(element, {
          autoAlpha: 1,
          scaleX: 0.9,
          scaleY: 1.2
        })
        .to(element, {
          scaleX: 1,
          scaleY: 1
        });
    },

    leave (element) {
      let timeline = this.$gsap.timeline({
        defaults: {
          duration: 0.2,
          stagger: 0.05
        }
      });

      timeline.to(element, {
        autoAlpha: 0,
        scaleY: 0.1
      });
    }
  },

  mounted () {
    // console.log('*** mounted', this.$root);
    // this.$gsap.set(this.$refs.slot, { autoAlpha: 0 });
  }
};
</script>
