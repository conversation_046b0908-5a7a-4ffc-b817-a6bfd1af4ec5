<template>
  <div class="towlien-view">
    <app-titlebar title="TowLien Setup"></app-titlebar>
    <section class="content">
      <div v-if="sToken !== ''" class="columns is-multiline">
        <div class="column is-6">
          TowLien is Already Setup for this Company
        </div>
      </div>
      <div v-if="sToken === ''" class="columns is-multiline">
        <div class="column is-6">
          <label>User ID</label>
          <div class="field has-addons">
            <div class="control">
              <input v-model="sUserID" type="text" class="input"/>
            </div>
          </div>
        </div>
        <div class="column is-6">
          <label>Password</label>
          <div class="field has-addons">
            <div class="control">
              <input v-model="sPassword" type="text" class="input"/>
            </div>
          </div>
        </div>
        <button :disabled="sUserID === '' || sPassword === ''" @click="setCredentials()" class="button">Set TowLien Credentials</button>
      </div>
    </section> <!-- /content -->
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'towlien-view',

  data () {
    return {
      sUserID: '',
      sPassword: '',
      sToken: ''
    };
  },

  computed: {
    ...mapGetters(['__state'])
  },

  methods: {
    ...mapActions([
      'UTILITY__getTowLienSettingsForOrg',
      'UTILITY__setTowLienCredentialsForOrg'
    ]),
    getSettings () {
      this.reset();
      this.UTILITY__getTowLienSettingsForOrg({
        locationKey: this.__state.orgUnitKey,
        callback: response => {
          this.$data.sToken = response.sToken;
        }
      });
    },
    reset () {
      this.sUserID = '';
      this.sPassword = '';
      this.sToken = '';
    },
    setCredentials () {
      this.UTILITY__setTowLienCredentialsForOrg({
        locationKey: this.__state.orgUnitKey,
        userId: this.$data.sUserID,
        password: this.$data.sPassword,
        callback: response => {
          this.getSettings();
        }
      });
    }
  },

  mounted () {
    this.reset();
    this.getSettings();
  }
};
</script>

<style scoped>

</style>
