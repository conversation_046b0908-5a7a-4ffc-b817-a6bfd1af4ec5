<template>
  <div id="records-view" data-page="customers">
    <grid
      title="Customers"
      key="grid"
      :grid="gridSettings"
      :data="gridData"
      :noun="viewConfig.noun"
      :refreshedAt="refreshedAt"
      :config="viewConfig"
      :multiselect="true"
      :access="gridAccess"
      :show-loader="showLoader"
      @refresh="refresh"
      @openRecord="openRecord"
      @exportData="exportData"
      @save="save">
      <template slot="context-tools">
        <spotlight-control
          :noun="viewConfig.noun"
          @setFilters="setFilters"
          @resetFilters="getSettings">
        </spotlight-control>

        <app-button type="white" v-if="manyRecordsAreSelected" @click="trimToSelection" title="Trim to Selection"><i class="fal fa-cut"></i></app-button>
        <app-button type="white" v-if="canDelete" @click="deleteRecord" title="Delete"><i class="far fa-trash-alt"></i></app-button>
        <app-button type="white" v-if="canRestore" @click="undeleteRecord" title="Restore"><i class="fal fa-undo"></i></app-button>
        <app-button type="white" v-if="canDuplicate" @click="duplicate" title="Duplicate"><i class="fal fa-clone"></i></app-button>
        <app-button type="white" @click="toggleInactiveRecords" title="Toggle Deleted Records"><i :class="inactiveToggleClasses"></i></app-button>
      </template>

      <template slot="floating-tools">
        <app-button type="success" size="normal" @click="add" v-if="canAdd">
          <i class="far fa-plus"></i>&nbsp;Add
        </app-button>
      </template>
    </grid>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div> <!-- /customers-view -->
</template>

<script>
import Access from '@/access';
import Grid from '../features/Grid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';

export default {
  name: 'customers-view',

  extends: RecordsView,

  components: {
    Grid
  },

  data () {
    return {
      actionableRecord: {},
      actionsVisible: false,

      viewConfig: {
        key: null,
        uuid: 'customers-screen',
        noun: 'Customer',
        requireFilters: true,
        recordKeyName: 'lCustomerKey',
        readRouteName: 'Customer',
        addRouteName: 'AddCustomer',
        trimField: {
          key: 'lCustomerKey',
          name: 'CUSlCustomerKey'
        }
      }
    };
  },

  computed: {
    canDelete () {
      if (!this.isDeletable) return false;

      return this.oneRecordIsSelected && Access.has('customers.delete');
    },

    canRestore () {
      if (this.isDeletable) return false;

      return this.oneRecordIsSelected && Access.has('customers.delete');
    },

    canAdd () {
      return Access.has('customers.edit');
    },

    canDuplicate () {
      return this.canAdd && this.oneRecordIsSelected;
    },

    gridAccess () {
      return {
        export: Access.has('customers.export')
      };
    }
  }
};
</script>
