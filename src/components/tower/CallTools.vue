<template>
  <div id="records-view" data-page="call-tools">
    <section class="tool-bar">
      <wizard-button class="_process" @click="goToDataSheet" flavor="default" :icon="false" data-flip-id="back-button">
        <i class="fas fa-phone"></i>&nbsp;&nbsp;&nbsp;Back to calls
      </wizard-button>

      <div class="_tools">
        <wizard-button
          v-for="tool in visibleTools"
          @click="setTool(tool)"
          :active="isActive(tool)"
          :key="tool.ToolID"
          :data-flip-id="tool.ToolID">
          {{ tool.Name }}
          <template slot="meta">{{ tool.Description }}</template>
        </wizard-button>
      </div>

      <app-grid-form class="_inputs" context="inline" v-if="inputs.length" data-flip-id="inputs">
        <div class="columns is-multiline">
          <div class="column is-12 is-left" v-for="(input, index) in inputs" :key="input.Name">
            <component
              :is="input.Control"
              :id="input.Name"
              v-model="input.Value"
              :required="input.Required"
              :options="input.Options"
              :keyAlias="input.KeyAlias"
              :valueAlias="input.ValueAlias"
              :shortCodeAlias="input.ShortCodeAlias"
              :tabindex="index"
              :empty-option="input.EmptyOption">
              {{ input.Label }}
            </component>
          </div>
        </div>
      </app-grid-form>

      <wizard-button class="_process" @click="process" flavor="primary" :icon="false" v-if="toolId" :disabled="!canProcess" data-flip-id="process-button">
        Process {{ __selectedRecords.length }} call<span v-show="__selectedRecords.length !== 1">s</span>
      </wizard-button>
    </section>

    <grid
      class="_data"
      title=""
      key="grid"
      :grid="gridSettings"
      :data="gridData"
      :refreshedAt="refreshedAt"
      :config="viewConfig"
      :multiselect="true"
      :show-loader="showLoader"
      :filters-button-visible="false"
      :columns-button-visible="false"
      @refresh="refresh"
      @openRecord="openRecord"
      @exportData="exportData"
      @save="save">
    </grid>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import { EVENT_INFO } from '@/config.js';
import InputFactory from '@/input_factory.js';
import Grid from '@/components/features/Grid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import WizardButton from '@/components/inputs/WizardButton.vue';

export default {
  name: 'call-tools',

  extends: RecordsView,

  components: {
    Grid,
    WizardButton
  },

  data () {
    return {
      viewConfig: {
        uuid: 'call-tools-screen',
        noun: 'CallTools',
        recordKeyName: 'CAL_lCallKey',
        readRouteName: 'CallReel',
        requireFilters: false,
        shouldImmediatelyLoadData: false
      },

      toolId: null,
      tools: [],
      inputs: [],
      processedCalls: [],

      inputModel: {
        DRIVER_KEY: {
          label: 'Driver',
          control: 'app-select',
          action: 'TOPSCOMPANY__getDrivers',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        TRUCK_KEY: {
          label: 'Truck',
          control: 'app-select',
          action: 'TOPSCOMPANY__getTrucks',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        LOT_KEY: {
          label: 'Lot',
          control: 'app-select',
          action: 'TOPSCOMPANY__getLots',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        LOAD_NUMBER: {
          label: 'Load Number',
          control: 'app-text',
          isVisible: true
        }
      }
    };
  },

  computed: {
    ...mapGetters(['RECORDS__settings']),

    anyRecordsAreSelected () {
      return this.__selectedRecords.length >= 1;
    },

    canProcess () {
      let remainers = this.$_.filter(this.$data.inputs, input => {
        return input.Required && this.$_.isEmpty(input.Value);
      });

      return this.$data.toolId &&
        remainers.length === 0 &&
        this.anyRecordsAreSelected;
    },

    visibleTools () {
      return this.$data.toolId
        ? this.$_.filter(this.$data.tools, ['ToolID', this.$data.toolId])
        : this.$data.tools;
    }
  },

  mounted () {
    this.getTools();
  },

  methods: {
    ...mapActions([
      'CALL_TOOLS__getToolInputs',
      'CALL_TOOLS__process',
      'TOPSCOMPANY__getCallTools',
      'TOPSCOMPANY__getDrivers',
      'TOPSCOMPANY__getTrucks',
      'TOPSCOMPANY__getLots'
    ]),

    goToDataSheet () {
      this.$router.push({ name: 'Calls' });
    },

    getTools () {
      this.TOPSCOMPANY__getCallTools({
        success: response => {
          this.$data.tools = response;
        }
      });
    },

    setTool (tool) {
      this.$data.toolId = this.$data.toolId === tool.ToolID
        ? null
        : tool.ToolID;

      if (!this.$data.toolId) {
        this.reset();
        return;
      }

      // Ignore pre-existing filters
      let settings = {
        ToolID: this.$data.toolId,
        ...this.RECORDS__settings
      };

      this.$_.set(settings, 'Grids[0].Filters', []);

      this.loadData(settings);

      this.$nextTick(() => {
        this.getInputs(this.$data.toolId);
      });
    },

    isActive ({ ToolID }) {
      return this.$data.toolId === ToolID;
    },

    getInputs (toolId) {
      this.CALL_TOOLS__getToolInputs({
        toolId: toolId,
        success: response => {
          this.$data.inputs = this.hydrateInputs(response);

          // let inputFactory = new InputFactory({ target: '._inputs' });
          // this.$data.inputs = inputFactory.hydrate(response);
        }
      });
    },

    hydrateInputs (inputs) {
      return this.$_.forEach(inputs, input => {
        this.$_.set(input, 'VueKey', this.$_.uniqueId());
        this.$_.set(input, 'Label', this.$_.get(this.$data.inputModel[input.Name], 'label', ''));
        this.$_.set(input, 'Control', this.$_.get(this.$data.inputModel[input.Name], 'control', ''));
        this.$_.set(input, 'Action', this.$_.get(this.$data.inputModel[input.Name], 'action', ''));
        this.$_.set(input, 'KeyAlias', this.$_.get(this.$data.inputModel[input.Name], 'keyAlias', ''));
        this.$_.set(input, 'ValueAlias', this.$_.get(this.$data.inputModel[input.Name], 'valueAlias', ''));
        this.$_.set(input, 'ShortCodeAlias', this.$_.get(this.$data.inputModel[input.Name], 'shortCodeAlias', ''));
        this.$_.set(input, 'EmptyOption', this.$_.get(this.$data.inputModel[input.Name], 'emptyOption', ''));
        this.$_.set(input, 'IsVisible', this.$_.get(this.$data.inputModel[input.Name], 'isVisible', ''));
        this.$_.set(input, 'Value', this.$_.get(this.$data.inputModel[input.Name], 'value', ''));
        this.$_.set(input, 'Options', []);

        input.Required = this.$_.includes(['true', '1', true, 1], input.Required);

        if (input.Control === 'app-checkbox') {
          input.Value = this.$_.includes(['true', '1', true, 1], input.Value);
        }

        if (this.$_.isEmpty(input.Action)) return;

        this[input.Action]({
          callback: response => {
            this.$set(input, 'Options', response);
          }
        });
      });
    },

    process () {
      let requestPayload = {
        toolId: this.$data.toolId,
        callKeys: [],
        success: response => {
          this.$data.processedCalls = response;

          this.pushErrorsIntoGrid();

          this.$hub.$emit(EVENT_INFO, 'Calls may or may not have been processed depending on their status.');
        }
      };

      requestPayload.callKeys = this.$_.map(this.__selectedRecords, record => {
        if (this.$_.has(record, this.$data.viewConfig.recordKeyName)) return record[this.$data.viewConfig.recordKeyName];
      });

      this.$_.forEach(this.$data.inputs, input => {
        this.$_.set(requestPayload, input.Name, input.Value);
      });

      this.CALL_TOOLS__process(requestPayload);
    },

    pushErrorsIntoGrid () {
      this.$_.forEach(this.$data.processedCalls, call => {
        if (!this.$_.has(call, 'Errors')) return;

        let remoteCall = this.$_.find(this.gridData, [this.$data.viewConfig.recordKeyName, call.CallKey]);

        this.$set(remoteCall, 'Errors', this.$_.get(call, 'Errors', []));
      });
    },

    reset () {
      this.__selectRecords([]);
      this.$data.viewData = {};
      this.$data.processedCalls = [];
      this.$data.inputs = [];
    }
  }
};
</script>
