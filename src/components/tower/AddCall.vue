<template>
  <div id="record-view" data-page="call">
    <app-titlebar title="Call"></app-titlebar>

    <section class="content-section">
      <div class="_liner">
        <main class="sections">
          <section class="pinned-sections">
            <div class="_liner">
              <form-section
                v-for="(section, name) in pinnedSections"
                :key="name"
                :id="name"
                :title="section.title"
                :fixed="true">
                <component
                  :is="section.componentName"
                  :call="call">
                </component>
              </form-section>
            </div>
          </section>

          <section class="floating-sections">
            <form-section
              v-for="(section, name) in floatingSections"
              :key="name"
              :id="name"
              :title="section.title"
              :fixed="true">
              <component
                :is="section.componentName"
                :call="call"
                :price-repository="priceRepository">
              </component>
            </form-section>
          </section>
        </main>

        <aside class="mini-map">
          <div class="_liner">
            <ul class="_links">
              <li class="_link"
                v-for="(section, name) in sectionsProxy"
                @click="lookAt(name)"
                :key="name">
                {{ section.title }}
              </li>
            </ul>
          </div>
        </aside>
      </div>
    </section>

    <app-footerbar>
      <div slot="left">
        <app-button @click="save(false)" type="primary" :disabled="!canSave">Save</app-button>
        <app-button @click="save(true)" type="default" :disabled="!canSaveAndStay">Save &amp; Assign</app-button>
        <app-button @click="cancel" type="default">Close</app-button>
      </div>
    </app-footerbar>
  </div>
</template>

<script>
import is from 'is_js';
import datefns from 'date-fns';
import Access from '@/utils/access.js';
import debounce from 'lodash/debounce';
import throttle from 'lodash/throttle';
import { fullDateTime } from '@/utils/filters.js';
import { mapGetters } from 'vuex';
import { callMixin } from '@/mixins/call_mixin.js';

import TowSection from '../call/TowSection.vue';
import NotesSection from '../call/NotesSection/Index.vue';
import HoldsSection from '../call/HoldsSection.vue';
import PaymentSection from '../call/PaymentSection.vue';
import PricingSection from '../call/PricingSection/Index.vue';
import MileageSection from '../call/MileageSection.vue';
import VehicleSection from '../call/VehicleSection.vue';
import DispatchSection from '../call/DispatchSection.vue';
import AccountingSection from '../call/AccountingSection.vue';
import InspectionSection from '../call/InspectionsSection.vue';
import SubterminalSection from '../call/SubterminalSection.vue';
import MiscellaneousSection from '../call/MiscellaneousSection.vue';

import {
  EVENT_ERROR,
  EVENT_SUCCESS,

  CALL_SECTION_TOW,
  CALL_SECTION_MISCELLANEOUS,
  CALL_SECTION_NOTES,
  CALL_SECTION_VEHICLE,
  CALL_SECTION_ACCOUNTING,
  CALL_SECTION_TOW_PRICING,
  CALL_SECTION_TOW_PAYMENT,
  CALL_SECTION_SUBTERMINAL,
  CALL_SECTION_TOW_DISPATCH,
  VALUE_ID
} from '@/config.js';

export default {
  name: 'add-call',

  components: {
    TowSection,
    NotesSection,
    HoldsSection,
    MileageSection,
    PaymentSection,
    PricingSection,
    VehicleSection,
    DispatchSection,
    AccountingSection,
    InspectionSection,
    SubterminalSection,
    MiscellaneousSection
  },

  mixins: [callMixin],

  data () {
    return {
      callDefaults: {},
      canSave: false,

      sections: {
        subterminal: {
          title: 'Company',
          componentName: 'subterminal-section',
          landingPoint: 'CAL_lSubterminalKey',
          complete: false,
          enabled: true,
          pinned: true
        },
        tow: {
          title: 'Tow',
          componentName: 'tow-section',
          landingPoint: 'CAL_vc30ContactName',
          jumpBackPoint: 'CAL_dCallTaken',
          complete: false,
          enabled: true,
          pinned: true
        },
        vehicle: {
          title: 'Vehicle',
          componentName: 'vehicle-section',
          landingPoint: 'CAL_iYear',
          jumpBackPoint: 'CAL_vc25VIN',
          complete: false,
          enabled: true,
          pinned: false
        },
        mileage: {
          title: 'Mileage',
          componentName: 'mileage-section',
          landingPoint: '',
          jumpBackPoint: '',
          complete: false,
          enabled: true,
          pinned: false
        },
        miscellaneous: {
          title: 'Miscellaneous',
          componentName: 'miscellaneous-section',
          landingPoint: 'CAL_tPriority',
          complete: false,
          enabled: true,
          pinned: false
        },
        towdispatch: {
          title: 'Dispatches',
          componentName: 'dispatch-section',
          landingPoint: '',
          enabled: false,
          pinned: false
        },
        towpricing: {
          title: 'Pricing',
          componentName: 'pricing-section',
          landingPoint: 'pricing_AddService',
          complete: false,
          enabled: true,
          pinned: false
        },
        towpayment: {
          title: 'Payments',
          componentName: 'payment-section',
          landingPoint: 'payment.add',
          complete: false,
          enabled: true,
          pinned: false
        },
        inspection: {
          title: 'Inspections',
          componentName: 'inspection-section',
          landingPoint: 'inspection.add',
          complete: false,
          enabled: true,
          pinned: false
        },
        notes: {
          title: 'Notes',
          componentName: 'notes-section',
          landingPoint: 'note.add',
          complete: false,
          enabled: true,
          pinned: false
        },
        accounting: {
          title: 'Accounting',
          componentName: 'accounting-section',
          landingPoint: 'CAL_vc255AccountingNotes',
          complete: false,
          enabled: true,
          pinned: false
        },
        holds: {
          title: 'Holds',
          componentName: 'holds-section',
          landingPoint: 'hold.add',
          complete: false,
          enabled: true,
          pinned: false
        }
      },

      priceRepository: {
        tow: {
          TaxRateOverride: '',
          TaxRate: '',
          DiscountPct: '',
          Total: '',
          TaxTotal: '',
          DiscountTotal: '',
          TaxExempt: false
        },
        sale: {
          TaxRateOverride: '',
          TaxRate: '',
          Total: '',
          TaxTotal: '',
          DiscountTotal: '',
          TaxExempt: false
        }
      },

      call: {
        dETA: '',
        iYear: '',
        Holds: [],
        Notes: [],
        ch5Zone: '',
        vc25VIN: '',
        lMakeKey: '',
        tPriority: '9',
        lModelKey: '',
        vc20PONum: '',
        vc20RONum: '',
        dCallTaken: '',
        dTagExpiry: '',
        fDiscountPct: 0,
        lTowTypeKey: '',
        TowPayments: [],
        bNoCharge: false,
        lCustomerKey: '',
        lBodyTypeKey: '',
        ch2StateKey: '',
        dAppointment: '',
        TowTicketNum: '',
        vc10Odometer: '',
        lColorTypeKey: '',
        lTruckTypeKey: '',
        TowOrderLines: [],
        vc20PoliceNum: '',
        vc30OwnerName: '',
        vc100Location: '',
        lReasonTypeKey: '',
        vc15LicenseNum: '',
        vc20OwnerPhone: '',
        vc10PoliceBeat: '',
        CallTakerNotes: '',
        InspectionItems: [],
        lSubterminalKey: '',
        vc30ContactName: '',
        dExpirationDate: '',
        bOwnerWithVehicle: 0,
        vc50UserDefined2: '',
        vc50UserDefined3: '',
        vc100Destination: '',
        vc255DriverNotes: '',
        fTaxRate_Override: '',
        gcLocationLatitude: '',
        vc20MembershipNum: '',
        vc100UserDefined1: '',
        gcLocationLongitude: '',
        bPortalToPortal: false,
        vc255DispatchNotes: '',
        vc255DispatchNotes2: '',
        vc255DispatchNotes3: '',
        vc255DispatchNotes4: '',
        lControlCustomerKey: '',
        bMileageRequired: false,
        bSecondCommission: false,
        vc20ContactPhoneNum: '',
        vc255AccountingNotes: '',
        gcDestinationLatitude: '',
        vc30ExtraVehicleInfo: '',
        vc50EquipmentRequired: '',
        gcDestinationLongitude: '',
        InvoiceNumEqualCallNum: false,
        DefaultPricingRetrieved: false,
        fMileageUnloaded: '',
        fMileageLoaded: '',
        fMileageReturn: '',
        Dispatches: [{
          lDispatchKey: '',
          lCallKey: '',
          lDriverKey: '',
          lTruckKey: '',
          lDispatchStatusTypeKey: VALUE_ID.dispatchStatus.completed,
          dLastStatusChange: '',
          dAssigned: '',
          lUserKey_Assigned: '',
          dDispatched: '',
          lUserKey_Dispatched: '',
          dAcknowledged: '',
          lUserKey_Acknowledged: '',
          dArrived: '',
          lUserKey_Arrived: '',
          dHooked: '',
          lUserKey_Hooked: '',
          dDropped: '',
          lUserKey_Dropped: '',
          dCompleted: '',
          lUserKey_Completed: '',
          tcCommission: '',
          fCommission1: '',
          fCommission2: '',
          lTotalMileage: '',
          bRetow: false,
          vc50LoadNum: '',
          lOdometer_Acknowledged: '',
          lOdometer_Arrived: '',
          lOdometer_Hooked: '',
          lOdometer_Dropped: '',
          lOdometer_Completed: '',
          DriverNum: '',
          TruckNum: '',
          DriverTruckPair: ''
        }]
      }
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings']),

    sectionsProxy () {
      let sections = {};
      let excludedSections = [];

      if (Number(this.TOPSCOMPANY__settings.NumSubterminals) <= 1) {
        excludedSections.push(CALL_SECTION_SUBTERMINAL);
      }

      if (!Access.has('calls.price')) {
        excludedSections.push(CALL_SECTION_TOW_PRICING);
      }

      if (!Access.has('calls.notes')) {
        excludedSections.push(CALL_SECTION_NOTES);
      }

      if (Access.has('calls.restrictDispatch')) {
        excludedSections.push(CALL_SECTION_TOW_DISPATCH);
      }

      if (Access.has('calls.restrictAccounting')) {
        excludedSections.push(CALL_SECTION_ACCOUNTING);
      }

      this.$_.forEach(this.sections, (section, key) => {
        if (section.enabled && !this.$_.includes(excludedSections, key)) {
          this.$_.set(sections, key, section);
        }
      });

      return sections;
    },

    pinnedSections () {
      let sections = {};

      this.$_.forEach(this.sectionsProxy, (section, key) => {
        if (section.pinned) {
          this.$_.set(sections, key, section);
        }
      });

      return sections;
    },

    floatingSections () {
      let sections = {};

      this.$_.forEach(this.sectionsProxy, (section, key) => {
        if (!section.pinned) {
          this.$_.set(sections, key, section);
        }
      });

      return sections;
    },

    recordToDuplicate () {
      return this.$route.query.duplicate;
    },

    canSaveAndStay () {
      return this.canSave && !this.sections.towdispatch.enabled;
    }
  },

  watch: {
    call: {
      deep: true,
      handler: throttle(function () {
        this.handleEtaAppointments();
        this.checkCompleteness();
        this.toggleDispatchSection();
      }, 1000)
    },

    'call.lCustomerKey' () {
      this.getNewCallDefaults();
    }
  },

  methods: {
    /**
     * Toggles the dispatch section based on the call taken time
     * and user access. AKA "Quick Add Workflow".
     */
    toggleDispatchSection () {
      let callTaken = this.$_.get(this.call, 'dCallTaken', '');
      let now = new Date();

      if (!callTaken) return;

      callTaken = datefns.format(callTaken, 'YYYY/MM/DD HH:mm:ss');
      now = datefns.format(now, 'YYYY/MM/DD HH:mm:ss');

      let callTakenExpiry = datefns.subHours(now, 3);
      callTakenExpiry = datefns.format(callTakenExpiry, 'YYYY/MM/DD HH:mm:ss');

      const isCallCurrent = datefns.isAfter(callTaken, callTakenExpiry);
      const isQuickAddWorkflow = Access.has('calls.editCallTaken') && !isCallCurrent;

      this.sections.towdispatch.enabled = isQuickAddWorkflow;
    },

    lookAt (section) {
      let content = window.getComputedStyle(document.querySelector('.content-section'));
      let offset = Number(content.paddingTop.replace('px', ''));

      this.$gsap.to('.content-section', {
        duration: 0.8,
        ease: 'power4.out',
        scrollTo: {
          y: `#${section}`,
          offsetY: offset
        }
      });
    },

    checkCompleteness () {
      const location = this.$_.get(this.call, 'vc100Location', '');
      const towPayments = this.$_.get(this.call, 'TowPayments', []);
      const towOrderLines = this.$_.get(this.call, 'TowOrderLines', []);
      const notes = this.$_.get(this.call, 'Notes', []);

      this.sections[CALL_SECTION_SUBTERMINAL].complete = !!this.call.lSubterminalKey;

      this.sections[CALL_SECTION_TOW].complete = is.all.truthy([
        location.length > 4,
        this.call.lTowTypeKey,
        this.call.lCustomerKey
      ]);

      this.sections[CALL_SECTION_VEHICLE].complete = is.truthy(this.call.lMakeKey);
      this.sections[CALL_SECTION_MISCELLANEOUS].complete = is.truthy(this.call.vc255DispatchNotes);
      this.sections[CALL_SECTION_TOW_PRICING].complete = towOrderLines.length > 0;
      this.sections[CALL_SECTION_TOW_PAYMENT].complete = towPayments.length > 0;
      this.sections[CALL_SECTION_NOTES].complete = notes.length > 0;

      this.canSave = is.all.truthy([
        this.call.lSubterminalKey,
        this.call.lCustomerKey,
        location.length,
        this.call.lTowTypeKey
      ]);
    },

    handleEtaAppointments () {
      if (!this.$_.isEmpty(this.call.dAppointment)) {
        this.call.dETA = '';
      }

      if (!this.$_.isEmpty(this.call.dETA)) {
        this.call.dAppointment = '';
      }
    },

    getDuplicateCallDefaults () {
      if (!this.recordToDuplicate) return;

      this.$store.dispatch('CALL__getDuplicateDefaults', {
        callKey: this.recordToDuplicate,
        callback: response => {
          this.callDefaults = response;

          this.fillDefaultValues();
        }
      });
    },

    getNewCallDefaults () {
      if (!this.call.lControlCustomerKey) return;

      this.$store.dispatch('TOPSCALL__getDefaultsForNew', {
        customerKey: this.call.lControlCustomerKey,
        callback: response => {
          this.callDefaults = response;

          if (is.truthy(response.CallTakerNotes)) {
            this.$alert(response.CallTakerNotes, 'Call Taker Note', {
              confirmButtonText: 'Acknowledge',
              type: 'info',
              callback: () => {
                setTimeout(() => {
                  document.querySelector('#CAL_vc100Location').focus();
                }, 500);
              }
            });
          }

          this.fillDefaultValues();
        }
      });
    },

    getNow () {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('__getNow', {
          callback: response => { resolve(response.Now); },
          errorCallback: error => { reject(error); }
        });
      });
    },

    async fillDefaultValues () {
      for (const property in this.callDefaults) {
        if (property === 'vc255DispatchNotes') {
          this.dispatchNotesProxy = this.callDefaults[property];
        } else {
          this.call[property] = this.callDefaults[property];
        }
      }

      let eta = this.$_.get(this.call, 'dETA', '');
      if (eta) {
        this.$set(this.call, 'dETA', await this.getEtaTimestamp(eta));
      }
    },

    cancel () {
      this.$router.go(-1);
    },

    async getEtaTimestamp (value, now = null) {
      if (value.length === 0 || value.length > 5) {
        return value;
      }

      const etaInMinutes = Number(value);
      let calculatedEta = now || await this.getNow();

      calculatedEta = datefns.parse(calculatedEta);
      calculatedEta = datefns.addMinutes(calculatedEta, etaInMinutes);
      calculatedEta = fullDateTime(calculatedEta.toString());

      return calculatedEta;
    },

    save: debounce(async function (assign) {
      if (!this.canSave) return;

      if (this.$_.isEmpty(this.call.dCallTaken)) {
        this.call.dCallTaken = await this.getNow();
      }

      if (!this.sections.towdispatch.enabled) {
        this.call.Dispatches = [];
      }

      this.$store.dispatch('TOPSCALL__add', {
        data: this.call,
        callback: response => {
          if (is.falsy(response.Key)) {
            this.$hub.$emit(EVENT_ERROR, 'The call was not added.');
            return;
          }

          this.$set(this.call, 'lCallKey', response.Key);
          this.$store.dispatch('__selectRecords', this.call);

          this.$hub.$emit(EVENT_SUCCESS, `Call ${response.Key} was added.`);

          if (this.$store.state.addCall.shouldStayOnSave) {
            this.$router.replace({ name: 'Call', params: { key: response.Key } });
            return;
          }

          if (assign) {
            this.$router.push({ name: 'AssignCall', params: { key: response.Key } });
          } else {
            this.$router.go(-1);
          }
        }
      });
    }, 400)
  },

  mounted () {
    this.getDuplicateCallDefaults();

    this.$store.dispatch('TOPSCOMPANY__getSettings', {
      callback: response => {
        this.$store.dispatch('TOPSCOMPANY__setSettings', response);

        // If the company has more than one subterminal then show the Subterminal section; otherwise, just the set the value
        if (Number(this.TOPSCOMPANY__settings.NumSubterminals) === 1) {
          this.$set(this.call, 'lSubterminalKey', this.TOPSCOMPANY__settings.SubterminalKey);
        }
      }
    });

    this.$nextTick(() => {
      if (Number(this.TOPSCOMPANY__settings.NumSubterminals) > 1) {
        // Focus is handled in SubterminalSection to fix issue with
        // iPadOS Safari prematurely focusing on input.
      } else {
        document.querySelector('.customer__search-input input').focus();
      }
    });
  },

  beforeDestroy () {
    // @TODO
    // store out preferences like last customer selected, etc.
  }
};
</script>
