<template>
  <div id="record-view" data-page="price" :data-search-mode="searchMode" :data-is-nested="isNested">
    <app-titlebar title="Price" v-if="titleBarIsVisible"></app-titlebar>

    <div class="content-section">
      <app-grid-form>
        <section class="_section-group">
          <form-section title="Detail" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-4">
                <app-select v-model="serviceKeyProxy" :options="$store.state.price.services" keyAlias="Key" valueAlias="Name" :required="true">
                  Service
                </app-select>
              </div>
              <div class="column is-4">
                <app-customer v-model="record.lCustomerKey" id="customer-key">
                  Customer
                </app-customer>
              </div>
              <div class="column is-4">
                <app-select v-model="record.lSubterminalKey" :options="subterminals" keyAlias="Key" valueAlias="Value">
                  Company
                </app-select>
              </div>
              <div class="column is-4">
                <InputTowType v-model="record.lTowTypeKey" />
              </div>
              <div class="column is-4">
                <app-number v-model="record.fMinimumQty">
                  Minimum Quantity
                </app-number>
              </div>
              <div class="column is-4">
                <app-number v-model="record.fMaximumQty">
                  Maximum Quantity
                </app-number>
              </div>

              <div class="column is-4">
                <app-checkbox v-model="record.bRatesOverridable">
                  Rates Overridable
                </app-checkbox>
              </div>
              <div class="column is-4">
                <app-checkbox v-model="record.bCompounded">
                  Rates Compounded
                </app-checkbox>
              </div>
              <div class="column is-4">
                <app-checkbox v-model="record.bAlwaysAvailable">
                  Always Available
                </app-checkbox>
              </div>
            </div>
          </form-section>

          <form-section title="Rates" :fixed="true">
            <table class="structure">
              <tr>
                <th></th>
                <th>Flat</th>
                <th>Quantity</th>
                <th>Rate</th>
                <th>Unit</th>
              </tr>

              <tr>
                <td>Initial</td>
                <td>
                  <input type="checkbox" v-model="record.bInitialFlatRate" />
                </td>
                <td>
                  <input type="number" v-model="record.fInitialQty" />
                </td>
                <td>
                  <input type="number" v-model="record.scInitialRate" />
                </td>
                <td>
                  <select v-model="record.lInitialUnitTypeKey">
                    <option value=""></option>
                    <option v-for="unitType in unitTypes" :value="unitType.Key" :key="unitType.Key">
                      {{ unitType.Value }}
                    </option>
                  </select>
                </td>
              </tr>

              <tr>
                <td>Secondary</td>
                <td>
                  <input type="checkbox" v-model="record.bSecondaryFlatRate" />
                </td>
                <td>
                  <input type="number" v-model="record.fSecondaryQty" />
                </td>
                <td>
                  <input type="number" v-model="record.scSecondaryRate" />
                </td>
                <td>
                  <select v-model="record.lSecondaryUnitTypeKey">
                    <option value=""></option>
                    <option v-for="unitType in unitTypes" :value="unitType.Key" :key="unitType.Key">
                      {{ unitType.Value }}
                    </option>
                  </select>
                </td>
              </tr>

              <tr>
                <td>Tertiary</td>
                <td>
                  <input type="checkbox" v-model="record.bTertiaryFlatRate" />
                </td>
                <td>
                  <input type="text" value="N/A" disabled />
                </td>
                <td>
                  <input type="number" v-model="record.scTertiaryRate" />
                </td>
                <td>
                  <select v-model="record.lTertiaryUnitTypeKey">
                    <option value=""></option>
                    <option v-for="unitType in unitTypes" :value="unitType.Key" :key="unitType.Key">
                      {{ unitType.Value }}
                    </option>
                  </select>
                </td>
              </tr>
            </table>
          </form-section>

          <app-modified-metadata
            class="modified-metadata"
            v-if="!isNewRecord"
            :record="record"
            :config="viewConfig"
            modifiedAtAlias="dDateLastModified"
            modifiedByAlias="lUserKey">
          </app-modified-metadata>
        </section>
      </app-grid-form>
    </div>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button type="primary" @click="save" :disabled="!canSave">
          Save
        </app-button>
        <app-button type="default" @click="cancel">
          Close
        </app-button>
      </template>

      <app-button v-if="!isNewRecord" type="default" @click="duplicate" :disabled="!canDuplicate">
        Duplicate
      </app-button>
      <app-button v-if="isDeletable" type="danger" :outlined="true" @click="deleteRecord" :disabled="!canDelete">
        Delete
      </app-button>
    </app-footerbar>
  </div>
</template>

<script>
import Access from '@/utils/access.js';
import RecordView from '@/components/ancestors/RecordView.vue';

export default {
  name: 'price-view',

  extends: RecordView,

  props: {
    serviceKey: { type: [String, Number], default: '' },
    presets: { type: Object, default: null }
  },

  data () {
    return {
      viewConfig: {
        noun: 'Price',
        recordKeyName: 'lPriceKey',
        returnRouteName: 'Prices',
        addRouteName: 'AddPrice'
      },

      record: {
        lPriceKey: '',
        lServiceKey: '',
        lCustomerKey: '',
        lTowTypeKey: '',
        lSubterminalKey: '',
        fMinimumQty: '',
        fMaximumQty: '',
        fInitialQty: '',
        scInitialRate: '',
        bInitialFlatRate: '',
        lInitialUnitTypeKey: '',
        fSecondaryQty: '',
        scSecondaryRate: '',
        bSecondaryFlatRate: '',
        lSecondaryUnitTypeKey: '',
        scTertiaryRate: '',
        bTertiaryFlatRate: '',
        lTertiaryUnitTypeKey: '',
        bRatesOverridable: '',
        bCompounded: '',
        bAlwaysAvailable: '',
        lUserKey: '',
        dDateLastModified: '',
        bActive: ''
      },

      unitTypes: [],
      subterminals: []
    };
  },

  computed: {
    titleBarIsVisible () {
      if (this.recordKey) return false;
      if (this.isNested) return false;

      return !this.searchMode;
    },

    canSave () {
      return Access.has('price.edit');
    },

    canDuplicate () {
      return Access.has('price.edit');
    },

    canDelete () {
      return Access.has('price.edit') && Access.has('price.delete');
    },

    serviceKeyProxy: {
      get () {
        return this.record.lServiceKey || this.serviceKey;
      },
      set (value) {
        this.record.lServiceKey = value;
      }
    }
  },

  methods: {
    beforeSave () {
      return new Promise(resolve => {
        this.record.lServiceKey = this.serviceKeyProxy;

        resolve();
      });
    },

    async duplicate () {
      this.record.lPriceKey = '';
      this.record.lCustomerKey = '';
      this.$emit('duplicate', this.record);

      document.querySelector('#customer-key input').focus();
    },

    getSubterminals () {
      this.$store.dispatch('CALL__getSubterminals', {
        callback: response => {
          this.subterminals = response;
        }
      });
    },

    getUnitTypes () {
      this.$store.dispatch('PRICE__getUnitTypes', {
        serviceKey: this.serviceKeyProxy,
        success: response => {
          this.unitTypes = response;
        }
      });
    },

    applyPresets () {
      if (!this.presets) return;

      Object.keys(this.presets).forEach(key => {
        if (key in this.record) {
          this.record[key] = this.presets[key];
        }
      });
    }
  },

  async mounted () {
    this.getUnitTypes();
    this.getSubterminals();

    setTimeout(() => {
      this.applyPresets();
    }, 1000);
  }
};
</script>
