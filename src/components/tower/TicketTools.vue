<template>
  <div id="ticket-tools">
    <app-titlebar title="Ticket Tools">
      <app-button type="white" @click="goToDataSheet"><i class="far fa-arrow-left"></i>&nbsp;Back to tickets</app-button>
    </app-titlebar>

    <section class="scene">
      <section class="paths">
        <wizard-button v-for="path in visiblePaths" @click="setPath(path.id)" :active="isActive(path)" :data-flip-id="path.id" :key="path.id">
          {{ path.label }}
        </wizard-button>
      </section>

      <app-grid-form class="form-inputs" v-if="pathId">
        <div class="columns is-multiline">
          <div class="column is-12 is-left" v-if="parameters.newPrefix.isVisible">
            <app-text v-model="parameters.newPrefix.value">
              New Prefix
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.voidReason.isVisible">
            <app-text v-model="parameters.voidReason.value">
              Reason
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.ticketNumber.isVisible">
            <app-text v-model="parameters.ticketNumber.value" maxlength="20">
              Ticket Number
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.driverKey.isVisible">
            <app-select v-model="parameters.driverKey.value" :options="drivers" keyAlias="Key" valueAlias="Value">
              Driver
            </app-select>
          </div>
          <div class="column is-12 is-left" v-if="parameters.lotKey.isVisible">
            <app-select v-model="parameters.lotKey.value" :options="lots" keyAlias="Key" valueAlias="Value">
              Lot
            </app-select>
          </div>
          <div class="column is-12 is-left" v-if="parameters.companyKey.isVisible">
            <app-select v-model="parameters.companyKey.value" :options="companies" keyAlias="Key" valueAlias="Value">
              Company
            </app-select>
          </div>
          <div class="column is-12 is-left" v-if="parameters.quantity.isVisible">
            <app-number v-model="parameters.quantity.value" min="0" max="10000">
              How many tickets?
            </app-number>
          </div>
          <div class="column is-12 is-left" v-if="parameters.startNumber.isVisible">
            <app-text v-model="parameters.startNumber.value" maxlength="20">
              Start with ticket number
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.targetPrefix.isVisible">
            <app-text v-model="parameters.targetPrefix.value">
              Prefix
            </app-text>
          </div>
          <!-- <div class="column is-4 is-bottom" v-if="parameters.quantity.isVisible">
            <app-text v-model="rangePreview" :readonly="true">
              Will Make
            </app-text>
          </div> -->
        </div>
      </app-grid-form>

      <wizard-button flavor="primary" @click="trigger" :icon="false" v-if="pathId">
        Process
      </wizard-button>
    </section>
  </div>
</template>

<script>
import Access from '@/access.js';
import { EVENT_SUCCESS } from '@/config.js';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'ticket-tools',

  data () {
    return {
      pathId: '',

      paths: [
        {
          id: 'open',
          label: 'Open tickets',
          isVisible: true
        },
        {
          id: 'assign-to-driver',
          label: 'Assign tickets to a driver',
          isVisible: true
        },
        {
          id: 'assign-to-lot',
          label: 'Assign tickets to a lot',
          isVisible: true
        },
        {
          id: 'assign-to-company',
          label: 'Assign tickets to a company',
          isVisible: true
        },
        {
          id: 'void',
          label: 'Void tickets',
          isVisible: true
        },
        {
          id: 'reopen',
          label: 'Reopen voided tickets',
          isVisible: true
        },
        {
          id: 'change-prefix',
          label: 'Change prefix on tickets',
          isVisible: true
        }
      ],

      parameters: {
        targetPrefix: {
          value: '',
          isVisible: true,
          relevantPaths: ['open', 'assign-to-driver', 'assign-to-lot', 'assign-to-company', 'void', 'reopen', 'change-prefix']
        },
        newPrefix: {
          value: '',
          isVisible: true,
          relevantPaths: ['change-prefix']
        },
        ticketNumber: {
          value: '',
          isVisible: true,
          relevantPaths: []
        },
        startNumber: {
          value: '',
          isVisible: true,
          relevantPaths: ['open', 'assign-to-driver', 'assign-to-lot', 'assign-to-company', 'reopen', 'change-prefix', 'void']
        },
        quantity: {
          value: '',
          isVisible: true,
          relevantPaths: ['open', 'assign-to-driver', 'assign-to-lot', 'assign-to-company', 'reopen', 'change-prefix', 'void']
        },
        driverKey: {
          value: '',
          isVisible: true,
          relevantPaths: ['assign-to-driver']
        },
        lotKey: {
          value: '',
          isVisible: true,
          relevantPaths: ['assign-to-lot']
        },
        companyKey: {
          value: '',
          isVisible: true,
          relevantPaths: ['assign-to-company']
        },
        voidReason: {
          value: '',
          isVisible: true,
          relevantPaths: ['void']
        }
      },

      drivers: [],
      lots: [],
      companies: []
    };
  },

  computed: {
    ...mapGetters([
      'TOPSCOMPANY__settings'
    ]),

    canEditPrefix () {
      return this.TOPSCOMPANY__settings.bAllowTowTicketPrefix === '1';
    },

    visiblePaths () {
      return this.$data.pathId
        ? this.$_.filter(this.$data.paths, ['id', this.$data.pathId])
        : this.$_.filter(this.$data.paths, ['isVisible', true]);
    },

    currentPath () {
      return this.$_.find(this.$data.paths, ['id', this.$data.pathId]) || null;
    },

    rangePreview () {
      let prefix = this.$data.parameters.targetPrefix.value;
      let startNumber = Number(this.$data.parameters.startNumber.value);
      let quantity = Number(this.$data.parameters.quantity.value);
      let endNumber = '';

      if (!startNumber && !quantity) return 'None';
      if (startNumber && quantity < 1) return 'None';
      if (startNumber && quantity === 1) return `${prefix}${startNumber}`;

      endNumber = startNumber + quantity - 1;

      return `${prefix}${startNumber}–${prefix}${endNumber}`;
    }
  },

  mounted () {
    this.$_.forEach(this.$data.paths, path => {
      if (path.id === 'open') {
        path.isVisible = Access.has('tickets.add');
      }

      if (path.id === 'change-prefix') {
        path.isVisible = this.canEditPrefix;
      }
    });

    this.TOPSCOMPANY__getDrivers({
      callback: response => {
        this.$data.drivers = response;
      }
    });

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.$data.lots = response;
      }
    });

    this.CALL__getSubterminals({
      callback: response => {
        this.$data.companies = response;
      }
    });
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getDrivers',
      'TOPSCOMPANY__getLots',
      'CALL__getSubterminals',
      'TOWTICKET__create',
      'TOWTICKET__changePrefix',
      'TOWTICKET__assignDriver',
      'TOWTICKET__assignLot',
      'TOWTICKET__assignCompany',
      'TOWTICKET__void',
      'TOWTICKET__unvoid'
    ]),

    goToDataSheet () {
      this.$router.push({ name: 'Tickets' });
    },

    setPath (pathId) {
      this.$data.pathId = this.$data.pathId === pathId
        ? null
        : pathId;

      this.$_.forEach(this.$data.parameters, (parameter, attribute) => {
        // Reset all values on path reset
        if (!pathId) {
          parameter.value = '';
        }

        if (attribute === 'targetPrefix') {
          parameter.isVisible = this.canEditPrefix && this.$_.includes(parameter.relevantPaths, pathId);
        } else {
          parameter.isVisible = this.$_.includes(parameter.relevantPaths, pathId);
        }
      });

      return;
    },

    isActive ({ id }) {
      return this.$data.pathId === id;
    },

    trigger () {
      switch (this.currentPath.id) {
        case 'open':
          this.create();
          break;
        case 'assign-to-driver':
          this.assignDriver();
          break;
        case 'assign-to-lot':
          this.assignLot();
          break;
        case 'assign-to-company':
          this.assignCompany();
          break;
        case 'void':
          this.void();
          break;
        case 'reopen':
          this.unvoid();
          break;
        case 'change-prefix':
          this.changePrefix();
          break;
      }
    },

    create () {
      this.TOWTICKET__create({
        startNumber: this.$data.parameters.startNumber.value,
        prefix: this.$data.parameters.targetPrefix.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    },

    changePrefix () {
      this.TOWTICKET__changePrefix({
        newPrefix: this.$data.parameters.newPrefix.value,
        targetPrefix: this.$data.parameters.targetPrefix.value,
        startNumber: this.$data.parameters.startNumber.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    },

    assignDriver () {
      this.TOWTICKET__assignDriver({
        driverKey: this.$data.parameters.driverKey.value,
        targetPrefix: this.$data.parameters.targetPrefix.value,
        startNumber: this.$data.parameters.startNumber.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    },

    assignLot () {
      this.TOWTICKET__assignLot({
        lotKey: this.$data.parameters.lotKey.value,
        targetPrefix: this.$data.parameters.targetPrefix.value,
        startNumber: this.$data.parameters.startNumber.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    },

    assignCompany () {
      this.TOWTICKET__assignCompany({
        companyKey: this.$data.parameters.companyKey.value,
        targetPrefix: this.$data.parameters.targetPrefix.value,
        startNumber: this.$data.parameters.startNumber.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    },

    void () {
      this.TOWTICKET__void({
        voidReason: this.$data.parameters.voidReason.value,
        targetPrefix: this.$data.parameters.targetPrefix.value,
        startNumber: this.$data.parameters.startNumber.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    },

    unvoid () {
      this.TOWTICKET__unvoid({
        targetPrefix: this.$data.parameters.targetPrefix.value,
        startNumber: this.$data.parameters.startNumber.value,
        quantity: this.$data.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, this.$_.get(response.RESULT, 'Tickets updated.'));
        }});
    }
  }
};
</script>
