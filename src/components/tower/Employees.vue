<template>
  <div id="records-view" data-page="employees">
    <transition name="fade" mode="out-in">
      <grid
        title="Employees"
        key="grid"
        :grid="gridSettings"
        :data="gridData"
        :refreshedAt="refreshedAt"
        :noun="viewConfig.noun"
        :config="viewConfig"
        :multiselect="true"
        :show-loader="showLoader"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @save="save">
        <template slot="context-tools">
          <spotlight-control
            :noun="viewConfig.noun"
            @setFilters="setFilters"
            @resetFilters="getSettings">
          </spotlight-control>

          <app-button type="white" v-if="canDelete" @click="deleteEmployee" title="Delete"><i class="far fa-trash-alt"></i></app-button>
          <app-button type="white" v-if="canRestore" @click="undeleteRecord" title="Restore"><i class="fal fa-undo"></i></app-button>
          <app-button type="white" v-if="canDuplicate" @click="duplicate" title="Duplicate"><i class="fal fa-clone"></i></app-button>
          <app-button type="white" @click="toggleInactiveRecords" title="Toggle Deleted Records"><i :class="inactiveToggleClasses"></i></app-button>
        </template>

        <template slot="floating-tools">
          <app-button type="success" size="normal" @click="add" v-if="canAdd">
            <i class="far fa-plus"></i>&nbsp;Add
          </app-button>
        </template>
      </grid>
    </transition>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div> <!-- /employees-view -->
</template>

<script>
import Access from '@/utils/access';
import Grid from '../features/Grid.vue';
import RecordsView from '../ancestors/RecordsView.vue';

export default {
  name: 'employees-view',

  extends: RecordsView,

  components: { Grid },

  data () {
    return {
      actionableRecord: {},
      actionsVisible: false,

      viewConfig: {
        key: null,
        uuid: 'employees-screen',
        noun: 'Employee',
        requireData: false,
        requireFilters: false,
        recordKeyName: 'lEmployeeKey',
        readRouteName: 'Employee',
        addRouteName: 'AddEmployee'
      }
    };
  },

  computed: {
    canDelete () {
      if (!this.isDeletable) return false;

      return this.oneRecordIsSelected && Access.has('employees.delete');
    },

    canRestore () {
      if (this.isDeletable) return false;

      return this.oneRecordIsSelected && Access.has('employees.delete');
    },

    canAdd () {
      return Access.has('employees.edit');
    },

    canDuplicate () {
      return this.oneRecordIsSelected && this.canAdd;
    }
  },

  methods: {
    deleteEmployee () {
      this.$confirm('Deleting an employee DOES NOT remove their access to the program. Please use the User Management application to revoke access.', 'Important', {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.deleteRecord();
      }).catch(() => {
        // Handle cancel
      });
    }
  }
};
</script>
