<template>
  <div id="record-view" data-page="tow-ticket" :data-search-mode="searchMode">
    <app-titlebar title="Tow Ticket" v-if="!searchMode"></app-titlebar>

    <div class="content-section">
      <app-grid-form>
        <section class="_section-group">
          <form-section title="Ticket" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-text v-model="record.lNumber" maxlength="20" :disabled="!canEditNumber" :required="true">
                  Number
                </app-text>
              </div>
              <div class="column is-6">
                <app-select v-model="record.lTicketStatusTypeKey" :options="statuses" keyAlias="Key" valueAlias="Value" :disabled="!canEdit">
                  Status
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-select v-model="record.lDriverKey" :options="drivers" keyAlias="Key" valueAlias="Value" :disabled="!canEdit">
                  Driver
                </app-select>
              </div>
              <div class="column is-6">
                <app-select v-model="record.lStorageLotKey" :options="lots" keyAlias="Key" valueAlias="Value" :disabled="!canEdit">
                  Lot
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-select v-model="record.lSubterminalKey" :options="subterminals" keyAlias="Key" valueAlias="Value" :disabled="!canEdit">
                  Company
                </app-select>
              </div>
              <div class="column is-6">
                <app-text v-model="record.lCallKey" :disabled=true>
                  Call Number
                </app-text>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="record.vc100Reason" maxlength="100" :disabled="!canEdit" :required="isReasonRequired">
                  Reason
                </app-textarea>
              </div>
            </div>
          </form-section>

          <app-modified-metadata
            class="modified-metadata"
            v-if="!isNewRecord"
            :record="record"
            :config="viewConfig"
            modifiedAtAlias="dDateLastModified"
            modifiedByAlias="lUserKey">
          </app-modified-metadata>
        </section>
      </app-grid-form>
    </div>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button type="primary" @click="save" :disabled="!canSave">Save</app-button>
        <app-button type="default" @click="cancel">Close</app-button>
      </template>
    </app-footerbar>
  </div> <!-- /driver-view -->
</template>

<script>
import Access from '@/utils/access';
import { mapActions } from 'vuex';
import { VALUE_ID } from '@/config';
import RecordView from '@/components/ancestors/RecordView.vue';

export default {
  name: 'tow-ticket-view',

  extends: RecordView,

  data () {
    return {
      viewConfig: {
        noun: 'TowTicket',
        recordKeyName: 'lTowTicketKey',
        returnRouteName: 'Tickets',
        addRouteName: 'AddTicket'
      },

      record: {
        LastRead: '',
        lTowTicketKey: '',
        lNumber: '',
        vc10Prefix: '',
        lDriverKey: '',
        lStorageLotKey: '',
        lTicketStatusTypeKey: '',
        lSubterminalKey: '',
        lCallKey: '',
        vc100Reason: '',
        lUserKeyLastModified: '',
        dDateLastModified: '',
        bActive: ''
      },

      drivers: [],
      lots: [],
      statuses: [],
      subterminals: [],
      previouslyUsed: false
    };
  },

  computed: {
    canEdit () {
      return !this.previouslyUsed;
    },

    canEditNumber () {
      return this.canEdit && Access.has('tickets.add');
    },

    isReasonRequired () {
      return Number(this.record.lTicketStatusTypeKey) === VALUE_ID.ticketStatus.voided;
    },

    canSave () {
      if (this.isReasonRequired) {
        return this.record.vc100Reason;
      }

      return this.canEdit;
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getDrivers',
      'TOWTICKET__getStatuses',
      'TOPSCOMPANY__getLots',
      'CALL__getSubterminals'
    ]),

    afterGetViewData () {
      this.previouslyUsed = Number(this.record.lTicketStatusTypeKey) === VALUE_ID.ticketStatus.used;
    }
  },

  mounted () {
    this.TOPSCOMPANY__getDrivers({
      callback: response => {
        this.drivers = response;
      }
    });

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.lots = response;
      }
    });

    this.TOWTICKET__getStatuses({
      success: response => {
        this.statuses = response;
      }
    });

    this.CALL__getSubterminals({
      callback: response => {
        this.subterminals = response;
      }
    });
  }
};
</script>
