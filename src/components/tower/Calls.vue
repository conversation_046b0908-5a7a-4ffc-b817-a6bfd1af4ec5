<template>
  <div id="records-view" data-page="calls">
    <grid
      title="Calls"
      key="grid"
      :grid="gridSettings"
      :data="gridData"
      :refreshedAt="refreshedAt"
      :config="viewConfig"
      :multiselect="true"
      :actions="true"
      :show-loader="showLoader"
      @refresh="refresh"
      @openRecord="openRecord"
      @exportData="exportData"
      @save="save"
      @getActions="getActions">
      <template slot="context-tools">
        <app-button type="white" @click="goToTools">Tools</app-button>
        <spotlight-control
          :noun="viewConfig.noun"
          @setFilters="setFilters"
          @resetFilters="getSettings">
        </spotlight-control>
        <app-button type="white" v-if="manyRecordsAreSelected" @click="trimToSelection" title="Trim to Selection"><i class="fal fa-cut"></i></app-button>
        <app-button type="white" v-if="canDuplicate" @click="duplicate" title="Duplicate"><i class="fal fa-clone"></i></app-button>
      </template>

      <template slot="floating-tools">
        <app-button type="success" size="normal" @click="addCall" v-if="canAddCall">
          <i class="far fa-plus"></i>&nbsp;Add
        </app-button>
      </template>
    </grid>

    <actions
      :show="actionsVisible"
      :record="actionableRecord"
      :call-key="actionableRecord.lCallKey"
      :subterminal-key="actionableRecord.lSubterminalKey"
      :dispatch-key="actionableRecord.lDispatchKey"
      @close="toggleActions"
      @notify="notify">
    </actions>

    <app-modal title="Payments" @close="$_PaymentMixin_togglePayments" :show="paymentsVisible">
      <payment-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="paymentsVisible"
        @close="$_PaymentMixin_togglePayments(false)">
      </payment-section>
    </app-modal>

    <app-modal title="Holds" @close="$_HoldsMixin_toggleHolds" :show="holdsVisible">
      <holds-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="holdsVisible"
        @close="$_HoldsMixin_toggleHolds(false)">
      </holds-section>
    </app-modal>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div> <!-- /calls-view -->
</template>

<script>
import { get } from 'lodash';
import Access from '@/access.js';
import Grid from '../features/Grid.vue';
import { COMPANY_ID } from '@/config';
import Actions from '../features/Actions.vue';
import { mapGetters, mapActions } from 'vuex';
import HoldsSection from '../call/HoldsSection.vue';
import RecordsView from '../ancestors/RecordsView.vue';
import PaymentSection from '../call/PaymentSection.vue';
import HoldsMixin from '@/mixins/holds_mixin.js';
import PaymentMixin from '@/mixins/payment_mixin.js';

export default {
  name: 'calls-view',

  extends: RecordsView,

  mixins: [
    HoldsMixin,
    PaymentMixin
  ],

  components: {
    Grid,
    Actions,
    HoldsSection,
    PaymentSection
  },

  data () {
    return {
      actionableRecord: {},
      actionsVisible: false,

      viewConfig: {
        key: null,
        uuid: 'calls-screen',
        noun: 'CallSearch',
        requireFilters: true,
        recordKeyName: 'CAL_lCallKey',
        readRouteName: 'CallReel',
        addRouteName: 'AddCall',
        trimField: {
          key: 'CAL_lReferenceNum',
          name: 'CALlReferenceNum'
        },
        dataAdditional: {
          SubcompanyKey: '' // Set in mounted()
        }
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'RECORDS__settings'
    ]),

    canDuplicate () {
      return !Access.has('calls.duplicate') && this.oneRecordIsSelected;
    },

    canAddCall () {
      if (Number(this.__state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT) {
        return !Access.has('calls.duplicate');
      }

      return true;
    }
  },

  methods: {
    ...mapActions(['__selectRecords']),

    addCall () {
      this.$router.replace({ name: 'AddCall' });
    },

    getActions (record) {
      this.$data.actionableRecord = {
        lCallKey: record.CAL_lCallKey || '',
        lDispatchKey: record.DIS_lDispatchKey || '',
        vehicleIsOnHold: this.isOnHold(record)
      };

      this.toggleActions();
    },

    isOnHold (record) {
      let isOnHold1 = get(record, 'HLD_bHoldOn', 0);
      let isOnHold2 = get(record, 'HLD_sHoldOn', 0);

      return this.$_.toString(isOnHold1) === '1' || isOnHold2 === 'On';
    },

    toggleActions (value = !this.$data.actionsVisible) {
      if (!value) this.refresh();

      this.$data.actionsVisible = value;
    },

    notify (payload) {
      this.$router.push({
        name: 'Notify',
        query: {
          callKey: this.$_.get(payload, 'callKey', ''),
          dispatchKey: this.$_.get(payload, 'dispatchKey', ''),
          dispatchDriverKey: this.$_.get(payload, 'dispatchDriverKey', ''),
          dispatchTruckKey: this.$_.get(payload, 'dispatchTruckKey', ''),
          dispatchEmployeeKey: this.$_.get(payload, 'dispatchEmployeeKey', ''),
          reason: this.$_.get(payload, 'reason', ''),
          returnTo: 'CallReel'
        }
      });
    },

    goToTools () {
      this.__selectRecords([]);

      this.$router.push({ name: 'CallTools' });
    }
  },

  mounted () {
    /**
     * Modify the workflow to stay on the call after saving
     * only when in the context of the calls screen
     */
    this.$store.state.addCall.shouldStayOnSave = true;

    this.$data.viewConfig.dataAdditional.SubcompanyKey = this.__state.user.Profile.lSubterminalKey || '';
  }
};
</script>
