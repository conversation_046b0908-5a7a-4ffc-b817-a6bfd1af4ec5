<template>
  <div id="motor-club-billing-view">
    <section id="tabs">
      <tab-group v-model="$store.state.motorClubBilling.selectedTab">
        <tab-item value="calls">Calls</tab-item>
        <tab-item value="invoices">Invoices</tab-item>
      </tab-group>
    </section>

    <section id="tools">
      <template v-if="$store.state.motorClubBilling.selectedTab === 'calls'">
        <div class="_description">Select from the list of calls that are ready to be invoiced.</div>
        <div class="v-space"></div>

        <wizard-button flavor="primary" :icon="false" @click="invoiceCalls" :disabled="!anyRecordsAreSelected || isInvoicingCalls">
          Invoice selected calls
        </wizard-button>
      </template>

      <template v-if="$store.state.motorClubBilling.selectedTab === 'invoices'">
        <div class="_label is-upper is-small">Filter</div>

        <div class="v-space"></div>

        <label class="_radio-control" :data-active="$store.state.motorClubBilling.invoiceFilter.pay === ''">
          <div>All</div>
          <input type="radio" v-model="$store.state.motorClubBilling.invoiceFilter.pay" name="filter_invoices" value="">
        </label>
        <label class="_radio-control" :data-active="$store.state.motorClubBilling.invoiceFilter.pay === 'paid'">
          <div>Paid</div>
          <input type="radio" v-model="$store.state.motorClubBilling.invoiceFilter.pay" name="filter_invoices" value="paid">
        </label>
        <label class="_radio-control" :data-active="$store.state.motorClubBilling.invoiceFilter.pay === 'unpaid'">
          <div>Unpaid</div>
          <input type="radio" v-model="$store.state.motorClubBilling.invoiceFilter.pay" name="filter_invoices" value="unpaid">
        </label>

        <div class="v-space"></div>

        <span class="select">
          <select v-model="$store.state.motorClubBilling.invoiceFilter.customer">
            <option value="">Customer...</option>
            <option v-for="customer in $store.state.motorClubBilling.customers" :value="customer.Value" :key="customer.Key">
              {{ customer.Value }}
            </option>
          </select>
        </span>
      </template>
    </section>

    <section id="data-sheet">
      <grid
        v-if="$store.state.motorClubBilling.selectedTab === 'calls'"
        title=""
        key="grid"
        class="grid"
        :grid="callsSettings"
        :data="callsProxy"
        :refreshedAt="refreshedAt"
        :config="viewConfig"
        :multiselect="true"
        :show-loader="showLoader"
        :invoice-preview-control="true"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @viewInvoice="viewInvoice"
        @save="save">
        <template slot="context-tools"></template>
      </grid>

      <grid
        v-if="$store.state.motorClubBilling.selectedTab === 'invoices'"
        title=""
        key="grid"
        class="grid"
        :grid="invoicesSettings"
        :data="invoicesProxy"
        :refreshedAt="refreshedAt"
        :config="viewConfig"
        :multiselect="false"
        :show-loader="showLoader"
        :invoice-review-control="true"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @viewInvoice="viewInvoice"
        @save="save">
        <template slot="context-tools"></template>
      </grid>
    </section>

    <invoice-preview
      :callKey="invoicePreview.callKey"
      :invoiceKey="invoicePreview.key"
      v-if="invoicePreview.isModalVisible"
      @close="invoicePreview.isModalVisible = false">
    </invoice-preview>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>
  import { mapActions } from 'vuex';
  import Access from '@/access';
  import Grid from '../features/Grid.vue';
  import RecordsView from '@/components/ancestors/RecordsView.vue';
  import InvoicePreview from '../features/InvoicePreview.vue';
  import { GRID_KEY } from '@/config';

  export default {
    name: 'motor-club-billing-view',

    extends: RecordsView,

    components: {
      Grid,
      InvoicePreview
    },

    data () {
      return {
        viewConfig: {
          uuid: 'motor-club-billing-screen',
          noun: 'MCBilling',
          readRouteName: 'CallReel',
          recordKeyName: 'lCallKey'
        },

        isInvoicingCalls: false,
        invoicePreview: {
          key: null,
          callKey: null,
          isModalVisible: false
        },

        gridKey: {
          calls: GRID_KEY.motorClubCallsToInvoice,
          invoices: GRID_KEY.motorClubInvoices
        }
      };
    },

    computed: {
      canEditThings () {
        return Access.has('motorClubBilling.read');
      },

      callsProxy () {
        return this.$_.get(this.$data.viewData, `Grids[${this.$data.gridKey.calls}]`, []);
      },

      callsSettings () {
        let settings = this.$_.find(this.RECORDS__settings.Grids, ['Key', this.$data.gridKey.calls]);

        return settings || {};
      },

      invoicesProxy () {
        let invoices = this.$_.get(this.$data.viewData, `Grids[${this.$data.gridKey.invoices}]`, []);

        if (this.$store.state.motorClubBilling.invoiceFilter.pay === 'paid') {
          invoices = this.$_.filter(invoices, ['tcBalance', '0.00']);
        }

        if (this.$store.state.motorClubBilling.invoiceFilter.pay === 'unpaid') {
          invoices = this.$_.reject(invoices, ['tcBalance', '0.00']);
        }

        if (this.$store.state.motorClubBilling.invoiceFilter.customer) {
          invoices = this.$_.filter(invoices, ['sCustomer', this.$store.state.motorClubBilling.invoiceFilter.customer]);
        }

        return invoices;
      },

      invoicesSettings () {
        let settings = this.$_.find(this.RECORDS__settings.Grids, ['Key', this.$data.gridKey.invoices]);

        return settings || {};
      }
    },

    methods: {
      ...mapActions([
        'MCBILLING__sendInvoice',
        'MCBILLING__getCustomers'
      ]),

      async invoiceCalls () {
        let promises = [];

        this.$data.isInvoicingCalls = true;

        this.__selectedRecords.forEach(record => {
          promises.push(new Promise((resolve, reject) => {
            this.MCBILLING__sendInvoice({
              key: record.lCallKey,
              success: response => resolve(response),
              fail: response => reject(response)
            });
          }));
        });

        const responses = await Promise.all(promises.map(promise => promise.catch(response => response)));

        this.$data.isInvoicingCalls = false;

        responses.forEach(response => {
          if ('CallKey' in response) {
            let targetCall = this.$_.find(this.callsProxy, [this.$data.viewConfig.recordKeyName, response.CallKey.toString()]);

            if (targetCall) {
              targetCall.isRequested = true;
            }
          } else {
            let targetCall = this.$_.find(this.callsProxy, [this.$data.viewConfig.recordKeyName, response.Context.toString()]);

            if (targetCall) {
              targetCall.Errors = this.$_.castArray({ Message: response.Message });
            }
          }
        });

        this.__selectRecords([]);
      },

      viewInvoice (record) {
        this.$data.invoicePreview.callKey = this.$_.get(record, 'lCallKey', null);
        this.$data.invoicePreview.key = this.$_.get(record, 'lKey', null);

        this.$data.invoicePreview.isModalVisible = !!this.$data.invoicePreview.callKey || !!this.$data.invoicePreview.key;
      }
    },

    mounted () {
      this.$store.state.addCall.shouldStayOnSave = false;

      this.MCBILLING__getCustomers({
        success: response => {
          this.$store.state.motorClubBilling.customers = response;
        }
      });
    }
  };
</script>
