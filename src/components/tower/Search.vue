<template>
  <div id="search">
    <app-titlebar :title="viewConfig.title">
      <app-button @click="goToCallTools" v-if="isCallScreen" type="white">
        Tools
      </app-button>
      <app-button @click="goToTicketTools" v-if="canGoToTicketTools" type="white" data-flip-id="ticket-tools-button">
        Tools
      </app-button>

      <spotlight-control
        v-if="viewConfig.hasSpotlight"
        :noun="viewConfig.noun"
        @setFilters="cancel()">
      </spotlight-control>
    </app-titlebar>

    <section class="clauses" @keyup.enter="search" v-if="!stash.formViewVisible">
      <svg class="whitespace-placeholder" v-if="grid.Filters.length === 0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <g stroke-linecap="round" stroke="#000" fill="none" stroke-linejoin="round">
          <path d="M20.132 3.868c4.49 4.491 4.49 11.773 0 16.264-4.491 4.49-11.773 4.49-16.264 0-4.49-4.491-4.49-11.773 0-16.264 4.491-4.49 11.773-4.49 16.264 0M3.87 3.87l16.26 16.26M3.87 20.13L20.13 3.87"/>
        </g>
      </svg>

      <ul>
        <li class="_clause"
          v-for="(clause, index) in grid.Filters"
          :key="clause.vueKey"
          :style="{ '--offset-x': clause.nestDepth }"
          :data-flip-id="clause.vueKey">

          <section class="_and-or-section">
            <button class="_and-or" v-if="index !== 0" @click="toggleAndOr(clause)">
              <transition name="flip" mode="out-in">
                <div v-if="clause.And" key="and">And</div>
                <div v-else key="or">Or</div>
              </transition>
            </button>

            <button class="_not" @click="clause.Not = !clause.Not">
              <transition name="flip" mode="out-in">
                <div v-if="!clause.Not" key="include">Include</div>
                <div v-else key="exclude">Exclude</div>
              </transition>
            </button>
          </section>

          <button class="_open-paren" :data-active="clause.OpenParen" :data-visible="hasManyClauses" @click="toggleOpenParen(clause)">
            [
          </button>

          <section class="_field-value-section">
            <select
              class="_field"
              v-model="clause.FieldID"
              @change="getPresets(clause)">
              <option value="">Field name</option>
              <option v-for="field in eligibleFieldsProxy" :value="field.ID" :key="field.Name">{{ field.Name }}</option>
            </select>

            <div class="_preset">
              <select v-model="clause.presetValue" @change="applyPreset(clause)">
                <option v-for="preset in clausePresets[clause.FieldID]" :key="preset.Display">
                  {{ preset.Display }}
                </option>
              </select>
              <div class="_trigger">
                <i class="_icon far fa-ellipsis-h"></i>
              </div>
            </div>

            <input
              class="_operator"
              v-model="clause.Operator"
              placeholder="Operator"
              type="text">

            <input
              class="_value"
              :class="clause.FieldID"
              v-model="clause.DisplayValue"
              type="text"
              @paste.prevent="parseClipboard($event, clause)"
              :placeholder="clause.Placeholder || 'Search value'">
          </section>

          <button class="_close-paren" :data-active="clause.CloseParen" :data-visible="hasManyClauses" @click="toggleCloseParen(clause)">
            ]
          </button>

          <section class="_controls-section">
            <button class="_clone" @click="copyClause(index)">
              <i class="far fa-clone"></i>
            </button>
            <button class="_remove" @click="removeClause(index)">
              <i class="far fa-trash-alt"></i>
            </button>
          </section>
        </li>
      </ul>

      <div class="_bookend"></div>
    </section>

    <app-footerbar>
      <div slot="left">
        <app-button @click="search" type="primary">
          Get Results
        </app-button>

        <span v-if="!stash.formViewVisible" class="select is-small width-auto">
          <select v-model="fieldId" @change="addClause">
            <option value="">Add a filter&hellip;</option>
            <option v-for="field in eligibleFieldsProxy" :value="field.ID" :key="field.ID">{{ field.Name }}</option>
          </select>
        </span>

        <app-button v-if="!stash.formViewVisible" @click="advancedFieldsVisible = !advancedFieldsVisible" type="default">
          <i :class="advancedFieldClasses"></i>
        </app-button>
        <app-button v-if="!stash.formViewVisible" @click="clear" type="default">
          Clear filters
        </app-button>

        <app-button v-if="viewConfig.hasFormView && stash.formViewVisible" @click="toggleGuide">
          Previous search
        </app-button>
        <app-button v-if="viewConfig.hasFormView && !stash.formViewVisible" @click="toggleGuide">
          View form
        </app-button>

        <app-button v-if="stash.formViewVisible" @click="preview" type="default" data-flip-id="preview-button">
          Advanced
        </app-button>

        <app-button @click="cancel" type="default" data-flip-id="cancel-button">
          Close
        </app-button>
      </div>

      <app-button v-if="canAddCall" @click="addCall" type="success" size="normal" data-flip-id="add-call-button" style="border-radius: 10rem;">
        <i class="far fa-plus"></i>&nbsp;Add
      </app-button>
      <app-button v-if="canAddCustomer" @click="addCustomer" type="success" size="normal" data-flip-id="add-customer-button" style="border-radius: 10rem;">
        <i class="far fa-plus"></i>&nbsp;Add
      </app-button>
    </app-footerbar>

    <div class="guide-wrapper" @keyup.enter.capture="search">
      <component
        :is="viewConfig.formName"
        class="guide"
        v-if="stash.formViewVisible"
        :search-mode="true">
      </component>
    </div>

    <quick-views
      v-if="viewConfig.hasQuickViews"
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="goToResults">
    </quick-views>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { newLineToCsv } from '../../string_utils.js';

import {
  FIELD_DATA_TYPE,
  EVENT_SEARCH_THROW_BOOMERANG,
  EVENT_SEARCH_CATCH_BOOMERANG,
  COMPANY_ID
} from '@/config.js';

import Access from '@/access.js';
import QuickViews from '../features/QuickViews.vue';
import LotForm from '../../components/tower/Lot.vue';
import StashMixin from '../../mixins/stash_mixin.js';
import CallForm from '../../components/tower/Call.vue';
import TruckForm from '../../components/tower/Truck.vue';
import DriverForm from '../../components/tower/Driver.vue';
import CustomerForm from '../../components/tower/Customer.vue';
import EmployeeForm from '../../components/tower/Employee.vue';
import SpotlightControl from '../features/SpotlightControl.vue';

export default {
  name: 'search',

  components: {
    LotForm,
    CallForm,
    TruckForm,
    DriverForm,
    QuickViews,
    EmployeeForm,
    CustomerForm,
    SpotlightControl
  },

  mixins: [
    StashMixin
  ],

  data () {
    return {
      viewConfigPresets: [
        {
          title: 'Filter',
          noun: 'Default'
        },
        {
          title: 'Dispatches',
          noun: 'Dispatches',
          returnRouteName: 'Dispatch'
        },
        {
          title: 'Trucks',
          noun: 'Truck',
          formName: 'TruckForm',
          uuid: 'truckSearch',
          returnRouteName: 'Trucks',
          hasFormView: true,
          hasSpotlight: true,
          hasQuickViews: true
        },
        {
          title: 'Drivers',
          noun: 'Driver',
          formName: 'DriverForm',
          uuid: 'driverSearch',
          returnRouteName: 'Drivers',
          hasFormView: true,
          hasSpotlight: true,
          hasQuickViews: true
        },
        {
          title: 'Lots',
          noun: 'StorageLot',
          formName: 'LotForm',
          uuid: 'lotSearch',
          returnRouteName: 'Lots',
          hasFormView: true,
          hasSpotlight: true,
          hasQuickViews: true
        },
        {
          title: 'Employees',
          noun: 'Employee',
          formName: 'EmployeeForm',
          uuid: 'employeeSearch',
          returnRouteName: 'Employees',
          hasFormView: true,
          hasSpotlight: true,
          hasQuickViews: true
        },
        {
          title: 'Calls',
          noun: 'CallSearch',
          formName: 'CallForm',
          uuid: 'callSearch',
          returnRouteName: 'Calls',
          hasFormView: true,
          hasSpotlight: true,
          hasQuickViews: true,
          requireFilters: true
        },
        {
          title: 'Customers',
          noun: 'Customer',
          formName: 'CustomerForm',
          uuid: 'customerSearch',
          returnRouteName: 'Customers',
          hasFormView: true,
          hasSpotlight: true,
          hasQuickViews: true,
          requireFilters: true
        },
        {
          title: 'Liens',
          noun: 'LienBatch',
          uuid: 'lienBatchSearch'
        },
        {
          title: 'Checkout',
          noun: 'Checkout',
          uuid: 'checkoutSearch'
        },
        {
          title: 'Tickets',
          noun: 'TowTicket',
          uuid: 'towTicket',
          returnRouteName: 'Tickets',
          requireFilters: true,
          hasQuickViews: true
        }
      ],

      viewConfig: {
        key: null,
        uuid: null,
        title: '',
        noun: this.$route.query.noun,
        formName: '',
        hasFormView: false,
        hasSpotlight: false,
        hasQuickViews: false,
        requireFilters: false,
        returnRouteName: ''
      },

      grid: {
        Filters: []
      },

      andOrOptions: [
        { key: 'and', value: 'And' },
        { key: 'or', value: 'Or' },
        { key: 'neither', value: '' }
      ],

      domState: null,
      fieldId: '',
      filterName: '',
      clausePresets: {},
      eligibleFields: [],
      fieldSearchTerm: '',
      noun: this.$route.query.noun,
      advancedFieldsVisible: false,
      cursorPlaceholder: '<cursor>'
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'RECORDS__settings'
    ]),

    isCallScreen () {
      return this.$route.query.noun === 'CallSearch';
    },

    isCustomerScreen () {
      return this.$route.query.noun === 'Customer';
    },

    isTicketScreen () {
      return this.$route.query.noun === 'TowTicket';
    },

    canAddCall () {
      if (!this.isCallScreen) return false;

      if (Number(this.__state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT) {
        return !Access.has('calls.duplicate');
      }

      return true;
    },

    canAddCustomer () {
      return this.isCustomerScreen && Access.has('customers.edit');
    },

    canGoToTicketTools () {
      return this.isTicketScreen && Access.has('tickets.edit');
    },

    viewKey () {
      return this.$route.query.view || this.RECORDS__settings.Key;
    },

    viewMode () {
      return this.$route.query.mode;
    },

    gridKey () {
      let value = this.$route.params.key;

      if (!value) {
        value = this.$route.query.grid;
      };

      if (!value) {
        value = this.$data.grid.Key;
      }

      return value;
    },

    eligibleFieldsProxy () {
      return this.$data.advancedFieldsVisible
        ? this.$data.eligibleFields
        : this.$_.reject(this.$data.eligibleFields, field => {
          return !this.$_.includes(this.activeFieldIds, field.ID) &&
            this.$_.includes([FIELD_DATA_TYPE.FOREIGN_KEY, FIELD_DATA_TYPE.PRIMARY_KEY], Number(field.Type));
        });
    },

    activeFieldIds () {
      return this.$_.map(this.$data.grid.Filters, clause => {
        return clause.FieldID;
      });
    },

    advancedFieldClasses () {
      return {
        'far': this.$data.advancedFieldsVisible,
        'fas': !this.$data.advancedFieldsVisible,
        'fa-filter': true
      };
    },

    hasManyClauses () {
      return this.$data.grid.Filters.length > 1;
    }
  },

  watch: {
    'grid': {
      deep: true,
      immediate: true,
      handler: function () {
        this.$nextTick(() => {
          this.cascadeNestedDepths();
          this.$forceUpdate();
        });
      }
    }
  },

  methods: {
    ...mapActions([
      'RECORDS__saveSettings',
      'RECORDS__getFilterPresets',
      'RECORD__createFilterClauses',
      'USERVIEW__getAvaliableFields'
    ]),

    addCall () {
      this.$router.push({ name: 'AddCall' });
    },

    addCustomer () {
      this.$router.push({ name: 'AddCustomer' });
    },

    goToTicketTools () {
      this.$router.push({ name: 'TicketTools' });
    },

    stringToBoolean (value) {
      if (this.$_.isBoolean(value)) return value;

      return this.$_.toLower(value) === 'true';
    },

    populateExistingFilters () {
      if (!this.RECORDS__settings.Grids) return;

      let targetGrid = this.$_.find(this.RECORDS__settings.Grids, ['Key', this.gridKey]);

      if (!targetGrid) return;

      let filters = this.$_.get(targetGrid, 'Filters', []);

      if (filters.length === 0) {
        this.addClause();
        return;
      }

      this.$_.set(targetGrid, 'Filters', this.$_.castArray(filters));

      this.$_.forEach(targetGrid.Filters, clause => {
        clause.And = this.stringToBoolean(clause.And);
        clause.Or = this.stringToBoolean(clause.Or);
        clause.Not = this.stringToBoolean(clause.Not);
        clause.OpenParen = this.stringToBoolean(clause.OpenParen);
        clause.CloseParen = this.stringToBoolean(clause.CloseParen);
        clause.vueKey = this.$_.uniqueId('clause-');
        clause.presetValue = '';

        if (!clause.And && !clause.Or) {
          clause.And = true;
        }

        this.getPresets(clause);
      });

      this.$data.grid = targetGrid;
    },

    getEligibleFields () {
      this.USERVIEW__getAvaliableFields({
        viewKey: this.viewKey,
        gridKey: this.gridKey,
        callback: response => {
          this.$data.eligibleFields = response;
        }
      });
    },

    cancel () {
      if (this.$data.viewConfig.returnRouteName) {
        this.$router.push({ name: this.$data.viewConfig.returnRouteName });
      } else {
        this.$router.go(-1);
      }
    },

    preview () {
      this.$hub.$emit(EVENT_SEARCH_THROW_BOOMERANG, () => {
        this.stash.formViewVisible = false;
      });
    },

    search () {
      if (this.stash.formViewVisible) {
        this.$hub.$emit(EVENT_SEARCH_THROW_BOOMERANG, () => {
          this.save();
        });
      } else {
        this.save();
      }
    },

    async compileClauses (props) {
      if (!this.$_.get(props, 'noun', null)) return;
      if (!this.$_.get(props, 'data', null)) return;

      return new Promise((resolve, reject) => {
        this.RECORD__createFilterClauses({
          noun: props.noun,
          data: props.data,
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    async save () {
      this.$data.grid.Filters = await this.finalizeClauses(this.$data.grid.Filters);
      let settingsPayload = await this.getSettingsPayload();

      if (this.$data.viewConfig.requireFilters && !this.$data.grid.Filters.length) {
        this.$notify.warning({
          title: 'Filters Required',
          message: 'Add one or more filters to continue.'
        });

        return;
      }

      this.RECORDS__saveSettings({
        noun: this.$data.noun,
        data: settingsPayload,
        callback: () => {
          this.goToResults();
        }
      });
    },

    goToResults () {
      if (this.$data.viewConfig.returnRouteName) {
        this.$router.push({ name: this.$data.viewConfig.returnRouteName });
      } else {
        this.$router.go(-1);
      }
    },

    async finalizeClauses (filters) {
      if (filters.length === 0) return filters;

      filters = this.$_.castArray(filters);

      filters[0].And = false;
      filters[0].Or = false;

      if (filters.length === 1) {
        filters[0].OpenParen = false;
        filters[0].CloseParen = false;
      }

      for (let index = 0; index < filters.length; index++) {
        filters[index] = await this.pruneProperties(filters[index]);
      }

      return filters;
    },

    pruneProperties ({ FieldName, Value, ...filter }) {
      return filter;
    },

    getSettingsPayload () {
      let settings = this.RECORDS__settings;
      let gridCount = this.$_.get(settings, 'Grids', []).length;

      for (let index = 0; index < gridCount; index++) {
        if (settings.Grids[index].Key === this.gridKey) {
          settings.Grids[index].Filters = this.$data.grid.Filters;
        }
      }

      return settings;
    },

    addClause () {
      let clause = {
        And: true,
        Or: false,
        Not: false,
        OpenParen: false,
        FieldID: this.$data.fieldId || '',
        FieldName: '',
        preset: '',
        Operator: '',
        Value: '',
        DisplayValue: '',
        CloseParen: false,
        vueKey: this.$_.uniqueId('clause-'),
        presetValue: ''
      };

      this.$data.grid.Filters.push(clause);
      this.$data.fieldId = '';

      this.getPresets(clause);

      this.scrollToEnd();
    },

    scrollToEnd () {
      this.$nextTick(() => {
        let parent = document.querySelector('.clauses');
        let bookend = document.querySelector('._bookend');

        if (bookend) {
          this.$gsap.to(parent, {
            duration: 0.8,
            ease: 'power4.out',
            scrollTo: bookend
          });
        }
      });
    },

    removeClause (index) {
      this.$data.grid.Filters.splice(index, 1);
    },

    copyClause (index) {
      let clause = this.$_.clone(this.$data.grid.Filters[index]);
      clause.vueKey = this.$_.uniqueId('clause-');

      this.$data.grid.Filters.push(clause);

      this.scrollToEnd();
    },

    clear () {
      this.$data.grid.Filters = [];
    },

    getPresets (clause) {
      if (!clause.FieldID) return;
      if (this.$_.has(this.$data.clausePresets, clause.FieldID)) return;

      this.RECORDS__getFilterPresets({
        noun: this.$data.noun,
        field: clause.FieldID,
        gridKey: this.gridKey,
        callback: response => {
          this.$set(this.clausePresets, clause.FieldID, response);
        }
      });
    },

    applyPreset (clause) {
      let presets = this.$_.get(this.$data.clausePresets, clause.FieldID, null);

      if (!presets) return;

      let preset = this.$_.find(presets, ['Display', clause.presetValue]);

      if (!preset) return;

      clause.Operator = preset.Operator;
      clause.Value = this.predictValue(preset);
      clause.Placeholder = preset.Display;
      clause.DisplayValue = this.predictValue(preset);

      let $input = document.querySelector(`.${clause.FieldID}`);
      let cursorPosition = clause.DisplayValue.search(this.$data.cursorPlaceholder);

      clause.Value = clause.Value.replace(this.$data.cursorPlaceholder, '');
      clause.DisplayValue = clause.DisplayValue.replace(this.$data.cursorPlaceholder, '');

      setTimeout(() => {
        $input.focus();
        $input.setSelectionRange(cursorPosition, cursorPosition);
      }, 300);
    },

    predictValue ({ Display, Value }) {
      if (Value) return Value;

      Display = this.$_.trim(this.$_.toLower(Display));

      if (this.$_.includes(Display, 'start')) return `${this.$data.cursorPlaceholder}*`;
      if (this.$_.includes(Display, 'end')) return `*${this.$data.cursorPlaceholder}`;
      if (this.$_.includes(Display, 'anywhere')) return `*${this.$data.cursorPlaceholder}*`;

      return '';
    },

    toggleAndOr (filter) {
      filter.And = !filter.And;
      filter.Or = !filter.Or;
    },

    toggleGuide () {
      this.stash.formViewVisible = !this.stash.formViewVisible;
    },

    parseClipboard (event, clause) {
      let value = event.clipboardData.getData('text');

      if (!value) return;

      clause.DisplayValue += newLineToCsv(value);
    },

    toggleOpenParen (clause) {
      clause.OpenParen = !clause.OpenParen;
    },

    toggleCloseParen (clause) {
      clause.CloseParen = !clause.CloseParen;
    },

    cascadeNestedDepths () {
      let depth = 0;

      this.$data.grid.Filters.forEach((clause, index) => {
        clause.nestDepth = depth;

        if (clause.OpenParen) ++depth;

        if (clause.CloseParen) {
          --depth;

          if (depth < 0) depth = 0;
        }
      });
    },

    setViewConfig () {
      let preset = this.$_.find(this.$data.viewConfigPresets, ['noun', this.$data.noun]);

      if (!preset) {
        preset = this.$_.find(this.$data.viewConfigPresets, ['noun', 'Default']);
      }

      this.$data.viewConfig.key = this.viewKey;
      this.$data.viewConfig.uuid = preset.uuid || this.$data.viewConfig.uuid;
      this.$data.viewConfig.title = preset.title || this.$data.viewConfig.title;
      this.$data.viewConfig.formName = preset.formName || this.$data.viewConfig.formName;
      this.$data.viewConfig.returnRouteName = preset.returnRouteName || this.$data.viewConfig.returnRouteName;
      this.$data.viewConfig.hasFormView = preset.hasFormView || this.$data.viewConfig.hasFormView;
      this.$data.viewConfig.hasSpotlight = preset.hasSpotlight || this.$data.viewConfig.hasSpotlight;
      this.$data.viewConfig.hasQuickViews = preset.hasQuickViews || this.$data.viewConfig.hasQuickViews;
      this.$data.viewConfig.requireFilters = preset.requireFilters || this.$data.viewConfig.requireFilters;

      if (preset.uuid) {
        this.setStashId(preset.uuid);
        this.stash = {
          formViewVisible: this.$data.viewConfig.hasFormView && this.viewMode === 'form'
        };
      }
    },

    goToCallTools () {
      this.$router.push({ name: 'CallTools' });
    }
  },

  mounted () {
    this.setViewConfig();
    this.getEligibleFields();
    this.populateExistingFilters();

    this.$hub.$on(EVENT_SEARCH_CATCH_BOOMERANG, this.$_.debounce(async payload => {
      this.$data.grid.Filters = await this.compileClauses(payload);

      if (this.$_.isFunction(payload.onSuccess)) payload.onSuccess();
    }, 400));
  },

  beforeDestroy () {
    this.$hub.$off(EVENT_SEARCH_CATCH_BOOMERANG);
  }
};
</script>
