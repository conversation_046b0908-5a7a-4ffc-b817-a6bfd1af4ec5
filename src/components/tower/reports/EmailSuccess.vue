<template>
  <div class="email-results">
    <div class="summary">
      <div class="successes">
        <i class="icon fal fa-envelope-circle-check pure-green"></i>
        <div class="count">{{ results.filter(result => result.Success).length }}</div>
        <div class="description">Emails sent</div>
      </div>
      <div class="errors">
        <i class="icon fal fa-triangle-exclamation pure-red"></i>
        <div class="count">{{ results.filter(result => !result.Success).length }}</div>
        <div class="description">Emails failed</div>
      </div>
    </div>

    <div class="v-space"></div>

    <details class="itemized-results">
      <summary>
        Details
      </summary>

      <div class="content">
        <template v-for="(result, index) in results">
          <AppTip v-if="!result.Success" :key="index">
            <i class="fas fa-triangle-exclamation pure-red" slot="icon"></i>
            <p>{{ result.Error }} {{ result.Customer }} <template v-if="result.Email">( {{ result.Email }} )</template></p>
          </AppTip>
        </template>
        <template v-for="(result, index) in results">
          <AppTip v-if="result.Success" :key="index">
            <i class="fas fa-check pure-green" slot="icon"></i>
            <p>{{ result.Customer }} <template v-if="result.Email">({{ result.Email }}) recieved email</template></p>
          </AppTip>
        </template>
      </div>
    </details>
  </div>
</template>

<script>
export default {
  name: 'email-results',

  props: {
    results: { type: Array, default: () => [] }
  }
};
</script>

<style scoped>
.summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  .successes, .errors {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    grid-template-areas:
      "count icon"
      "description description";

    padding: 1rem;
    color: color-mix(in oklch, var(--panel-color) 60%, black);
    background-color: color-mix(in oklch, var(--panel-color) 10%, transparent);
    border: 1px solid color-mix(in oklch, var(--panel-color) 20%, transparent);
    border-radius: 0.5rem;
  }

  .successes {
    --panel-color: var(--pure-green);
  }

  .errors {
    --panel-color: var(--pure-red);
  }

  .count {
    grid-area: count;
    place-self: start;

    font-weight: bold;
  }

  .icon {
    grid-area: icon;
    justify-self: end;
  }

  .description {
    grid-area: description;

    padding: 0;
    margin: 0 !important;
  }
}

.itemized-results {
  summary {
    padding: 0.5rem;
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
