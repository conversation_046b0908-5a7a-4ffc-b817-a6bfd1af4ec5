<template>
  <form id="reports-view" method="get" :action="txiApiUrl" target="_blank">
    <input v-for="(value, name) in flattenedPayload" type="hidden" :name="name" :value="value" :key="name">

    <app-titlebar title="Reports"></app-titlebar>

    <section class="pool">
      <span class="select is-large">
        <select
          v-model="selectedReportKey"
          v-if="reports.length"
          @change="getParameters"
          class="report-control"
          tabindex="1">
          <option value="">Select a Report —</option>
          <option v-for="report in reports" :value="report.Key" :key="report.Key">{{ report.Name }}</option>
        </select>
      </span>

      <div class="description">
        <transition name="fade" mode="out-in" appear>
          <AppTip>
            <p class="small">{{ selectedReport.Description || 'Please select a report to view its description.' }}</p>
          </AppTip>
        </transition>
      </div>
    </section>

    <section class="parameters">
      <transition name="flip" mode="out-in" appear>
        <div class="parameters-liner" v-if="parameters" :key="selectedReport.Key">
          <div class="columns" v-for="(parameter, index) in visibleParameters" :key="parameter.VueKey">
            <div class="column">
              <div class="field">
                <component
                  :is="parameter.Control"
                  :showAll="parameter.showAll"
                  :id="parameter.Name"
                  :required="requiredIndicatorIsVisible(parameter)"
                  v-model="parameter.Value"
                  :options="parameter.Options"
                  :keyAlias="parameter.KeyAlias"
                  :valueAlias="parameter.ValueAlias"
                  :shortCodeAlias="parameter.ShortCodeAlias"
                  :tabindex="index + 2"
                  :empty-option="parameter.EmptyOption"
                  @change="setValue"
                  @keyup="setValue">
                  {{ parameter.Label }}
                </component>
              </div>
            </div>

            <div class="column">
              <div class="field" v-if="shouldShowStartTimeControl(parameter)">
                <app-time
                  id="startTime"
                  v-model="$store.state.report.startTime"
                  placeholder="hh:mm:ss"
                  @change="affectBeginDate">
                  Start Time
                </app-time>
              </div>

              <div class="field" v-if="shouldShowEndTimeControl(parameter)">
                <app-time
                  id="endTime"
                  v-model="$store.state.report.endTime"
                  placeholder="hh:mm:ss"
                  @change="affectEndDate">
                  End Time
                </app-time>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </section>

    <app-footerbar>
      <input-button
        id="email-report-trigger"
        popovertarget="email-report-dialog"
        :disabled="!canEmailReport"
        type="button">
        Send to email
      </input-button>
      <input-button
        data-variant="blue"
        :disabled="!canViewReport"
        type="submit">
        View report
      </input-button>
      <button v-show="false" id="view-report-proxy"></button>
    </app-footerbar>

    <EmailReportModal
      v-if="selectedReport"
      :report="selectedReport"
      :parameters="parameters" />
  </form>
</template>

<script>
import datefns from 'date-fns';
import { mapGetters, mapActions } from 'vuex';
import EmailReportModal from './EmailReportModal.vue';
import InputButton from '@/components/tower/liens/inputs/Button.vue';

// Note: The process on this page is roundabout with the goal of opening
// the report in a new tab. The standard XHR request is hijacked while the data
// from that request is transformed and injected into the <form>.

export default {
  name: 'reports-view',

  components: {
    EmailReportModal,
    InputButton
  },

  data () {
    return {
      reports: [],
      payload: {},
      txiApiUrl: '',
      parameters: [],
      temporaryValues: {},
      selectedReportKey: '',

      // Todo: Shift this to API
      parameterModel: {
        INVOICE_NUM: {
          label: 'Invoice Number',
          control: 'app-text',
          isVisible: true
        },
        BEGIN_DATE_TIME: {
          label: 'Start Date',
          control: 'app-date',
          value: datefns.format(new Date(), 'MM/DD/YYYY'),
          isVisible: true
        },
        END_DATE_TIME: {
          label: 'End Date',
          control: 'app-date',
          value: datefns.format(new Date(), 'MM/DD/YYYY'),
          isVisible: true
        },
        CUSTOMER_KEY: {
          label: 'Customer',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getCustomers',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        CUSTOMER_NAME: {
          // Same as CUSTOMER_KEY but returns the name value instead of the key
          label: 'Customer',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getCustomers',
          value: -1,
          keyAlias: 'Value',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        BILLING_CUSTOMER: {
          label: 'Customer Source',
          control: 'app-select-report',
          showAll: false,
          action: 'customerSources',
          value: 0,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          emptyOption: false,
          isVisible: true
        },
        CUSTOMER_TYPE: {
          label: 'Customer Type',
          control: 'app-select-report',
          showAll: true,
          action: 'CUSTOMER__getTypes',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        CUSTTYPE_NAME: {
          // Same as CUSTOMER_TYPE but returns the name value instead of the key
          label: 'Customer Type',
          control: 'app-select-report',
          showAll: true,
          action: 'CUSTOMER__getTypes',
          value: -1,
          keyAlias: 'Value',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        LOT_KEY: {
          label: 'Lot',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getLots',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        SUBTERMINAL_KEY: {
          label: 'Company',
          control: 'app-select-report',
          showAll: true,
          action: 'CALL__getSubterminals',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        CALL_NUM: {
          label: 'Call Number',
          control: 'app-text',
          isVisible: true
        },
        CALLS_NUMS: {
          label: 'Call Numbers',
          control: 'app-text',
          placeholder: 'e.g. 123, 456, 789',
          isVisible: true
        },
        DRIVER_KEY: {
          label: 'Driver',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getDrivers',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        TRUCK_KEY: {
          label: 'Truck',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getTrucks',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        PO_REQD: {
          label: 'P.O. Number Required',
          control: 'app-checkbox',
          isVisible: true
        },
        RO_REQD: {
          label: 'R.O. Number Required',
          control: 'app-checkbox',
          isVisible: true
        },
        VIN_REQD: {
          label: 'VIN Required',
          control: 'app-checkbox',
          isVisible: true
        },
        ODOMETER_REQD: {
          label: 'Odometer Required',
          control: 'app-checkbox',
          isVisible: true
        },
        HOLD_REASON_TYPE: {
          label: 'Hold Reason',
          control: 'app-select-report',
          showAll: true,
          action: 'HOLD__getReasons',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        TOW_CLASS: {
          label: 'Tow Class',
          control: 'app-select-report',
          showAll: true,
          action: 'TOWTYPE__getClasses',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        COMPLETION_REVENUE_DATE: {
          label: 'Based on Completion Date (Otherwise, Revenue Date)',
          control: 'app-checkbox',
          isVisible: true
        },
        DRIVER_STATUS: {
          label: 'Driver Status',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getDriverStatuses',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        EMP_KEY: {
          label: 'Employee',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getEmployees',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        CERTIFICATION_NUM: {
          label: 'Certification Number',
          control: 'app-text',
          isVisible: true
        },
        EMPLOYEE_TYPE: {
          label: 'Employee Type',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getEmployeeTypes',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        JUST_DATE: {
          // Works like BEGIN_DATE_TIME but hides the time section (default to 00:00:00)
          label: 'Start Date',
          control: 'app-date',
          value: datefns.format(new Date(), 'MM/DD/YYYY'),
          isVisible: true
        },
        REVENUE_RECONCILIATION_DATE: {
          label: 'Based on Revenue Date (Otherwise, Reconciliation Date)',
          control: 'app-checkbox',
          isVisible: true
        },
        INVOICES_NUMS: {
          label: 'Invoice Numbers',
          control: 'app-text',
          placeholder: 'e.g. 123, 456, 789',
          isVisible: true
        },
        BARCODE_NUM: {
          label: 'Bar Code Number',
          control: 'app-text',
          isVisible: true
        },
        BARCODES_NUMS: {
          label: 'Bar Code Numbers',
          control: 'app-text',
          placeholder: 'e.g. 123, 456, 789',
          isVisible: true
        },
        REASON_TYPE: {
          label: 'Reason',
          control: 'app-select-report',
          showAll: true,
          action: 'CALL__getReasons',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        MONTH: {
          label: 'Calendar Month',
          control: 'app-select-report',
          showAll: true,
          action: 'getMonths',
          value: datefns.format(datefns.subMonths(new Date(), 1), 'M'),
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        YEAR: {
          label: 'Calendar Year',
          control: 'app-select-report',
          showAll: true,
          action: 'getYears',
          value: datefns.format(new Date(), 'YYYY'),
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        CONFIRMED_LAST_30: {
          label: 'Only Show Unconfirmed Calls and Calls Confirmed in Last 30 Days',
          control: 'app-checkbox',
          isVisible: true
        },
        SERVICE_KEY: {
          label: 'Service',
          control: 'app-select-report',
          showAll: true,
          action: 'TOPSCOMPANY__getLocationServices',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        INCLUDE_CANCELED_CALLS: {
          label: 'Include Canceled Calls',
          control: 'app-checkbox',
          isVisible: true
        },
        BAAN_NUM: {
          label: 'Group Companies Based on Accounting Number',
          control: 'app-checkbox',
          isVisible: true
        }

        // @TODO
        // SELLERTYPE_KEY
        // SELLER_KEY
        // BIDDER_KEY
        // LOAD_NUMS
        // VEHICLE_YR
        // VEHICLE_MAKE
        // VEHICLE_MODEL
        // SWIPE_LICENSE
        // STATE_CODE
        // DMV_ORDER
        // TICKET_PREFIX
        // TICKET_START
        // TICKET_END
        // CONTACT_ORDER_NUM
        // GENERIC_TEXT_
        // USER_KEY
        // EMAIL_TO
      }
    };
  },

  computed: {
    ...mapGetters(['__state']),

    flattenedPayload () {
      let parameters = {};

      this.$_.forEach(this.$data.payload, payloadSection => {
        this.$_.forEach(payloadSection, (value, name) => {
          if (typeof value === 'object') {
            this.$_.forEach(value, (value, name) => {
              switch (name) {
                case 'BEGIN_DATE_TIME':
                  parameters[name] = value + ' ' + this.$store.state.report.startTime;
                  break;
                case 'END_DATE_TIME':
                  parameters[name] = value + ' ' + this.$store.state.report.endTime;
                  break;
                default:
                  parameters[name] = this.$_.isBoolean(value) ? Number(value) : value;
                  break;
              }
            });
          } else {
            parameters[name] = this.$_.isBoolean(value) ? Number(value) : value;
          }
        });
      });

      return parameters;
    },

    selectedReport () {
      let report = this.$_.find(this.$data.reports, ['Key', this.$data.selectedReportKey]);

      return this.$_.isEmpty(report) ? { Key: '', Description: '' } : report;
    },

    visibleParameters () {
      return this.$_.filter(this.$data.parameters, parameter => this.controlIsVisible(parameter));
    },

    transformedParameters () {
      let parameterObject = {};

      this.$_.forEach(this.$data.parameters, parameter => {
        this.$_.set(parameterObject, parameter.Name, parameter.Value);
      });

      return parameterObject;
    },

    canViewReport () {
      if (!this.$data.parameters.length) return false;

      // Todo: Unable to check checkbox in Safari
      let remainers = this.$data.parameters.filter(parameter => {
        return parameter.Required &&
          !['app-checkbox', 'app-select-report'].includes(parameter.Control) &&
          !parameter.Value;
      });

      return !remainers.length;
    },

    canEmailReport () {
      if (!this.canViewReport) return false;
      if (!this.selectedReport.EmailAvailable && !this.selectedReport.EmailToCustomersAvailable) return false;

      return true;
    }
  },

  watch: {
    transformedParameters () {
      this.renderRequest();
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getTrucks',
      'TOPSCOMPANY__getDrivers',
      'CALL__getReasons',
      'TOPSCOMPANY__getReports',
      'TOPSCOMPANY__getCustomers',
      'TOPSCOMPANY__getEmployees',
      'TOWTYPE__getClasses',
      'HOLD__getReasons',
      'TOPSCOMPANY__getLots',
      'CALL__getSubterminals',
      'CUSTOMER__getTypes',
      'TOPSCOMPANY__getEmployeeTypes',
      'TOPSCOMPANY__getDriverStatuses',
      'REPORT__getCreate',
      'REPORT__getParameters',
      'TOPSCOMPANY__getLocationServices'
    ]),

    hasParameter (name) {
      return !this.$_.isNil(this.getParameter(name));
    },

    controlIsVisible (parameter) {
      if (!this.$_.get(parameter, 'IsVisible', true)) return false;

      if (parameter.Control !== 'app-select-report') return true;

      return this.$_.get(parameter, 'Options', []).length > 1;
    },

    requiredIndicatorIsVisible (parameter) {
      return this.controlIsVisible(parameter) && parameter.Required;
    },

    getParameter (name) {
      let target = this.$_.find(this.$data.parameters, ['Name', name]);

      return this.$_.isNil(target) ? false : target;
    },

    affectBeginDate (input) {
      this.setTemporaryValue(input);

      if (!this.getParameter('BEGIN_DATE_TIME').Value) {
        this.setParameter('BEGIN_DATE_TIME', datefns.format(new Date(), 'MM/DD/YYYY'));
      }

      return;
    },

    affectEndDate (input) {
      this.setTemporaryValue(input);

      if (!this.getParameter('END_DATE_TIME').Value) {
        this.setParameter('END_DATE_TIME', datefns.format(new Date(), 'MM/DD/YYYY'));
      }

      return;
    },

    setParameter (name, value) {
      let target = this.getParameter(name);

      this.$set(target, 'Value', value);

      return target;
    },

    shouldShowStartTimeControl (parameter) {
      return parameter.Name === 'BEGIN_DATE_TIME';
    },

    shouldShowEndTimeControl (parameter) {
      return parameter.Name === 'END_DATE_TIME';
    },

    getReports () {
      this.TOPSCOMPANY__getReports({
        success: response => {
          this.$data.reports = response;

          const reportControl = document.querySelector('.report-control');

          if (reportControl) {
            reportControl.focus();
          }

          if (this.$route.query.preselectreport) {
            this.$data.selectedReportKey = this.$route.query.preselectreport;
            this.getParameters();
          }
        }
      });
    },

    getParameters () {
      if (!this.$data.selectedReportKey) {
        this.$data.parameters = [];
        return;
      }

      this.REPORT__getParameters({
        reportKey: this.$data.selectedReportKey,
        callback: response => {
          // The server will handle these.
          let filteredResponse = this.$_.filter(response, parameter => {
            if (!this.$_.includes(['CURRENT_USER', 'LOC_KEY'], parameter.Name)) return parameter;
          });

          this.$data.parameters = this.hydrateParameters(filteredResponse);
        }
      });
    },

    hydrateParameters (parameters) {
      return this.$_.forEach(parameters, parameter => {
        this.$_.set(parameter, 'VueKey', this.$_.uniqueId());
        this.$_.set(parameter, 'Required', this.$_.includes(['true', '1', true, 1], parameter.Required));
        this.$_.set(parameter, 'Label', this.$_.get(this.$data.parameterModel[parameter.Name], 'label', ''));
        this.$_.set(parameter, 'Control', this.$_.get(this.$data.parameterModel[parameter.Name], 'control', ''));
        this.$_.set(parameter, 'showAll', this.$_.get(this.$data.parameterModel[parameter.Name], 'showAll', true));
        this.$_.set(parameter, 'Action', this.$_.get(this.$data.parameterModel[parameter.Name], 'action', ''));
        this.$_.set(parameter, 'KeyAlias', this.$_.get(this.$data.parameterModel[parameter.Name], 'keyAlias', ''));
        this.$_.set(parameter, 'ValueAlias', this.$_.get(this.$data.parameterModel[parameter.Name], 'valueAlias', ''));
        this.$_.set(parameter, 'ShortCodeAlias', this.$_.get(this.$data.parameterModel[parameter.Name], 'shortCodeAlias', ''));
        this.$_.set(parameter, 'EmptyOption', this.$_.get(this.$data.parameterModel[parameter.Name], 'emptyOption', ''));
        this.$_.set(parameter, 'IsVisible', this.$_.get(this.$data.parameterModel[parameter.Name], 'isVisible', ''));

        let temporaryValue = this.$_.get(this.$data.temporaryValues, parameter.Name, '');
        let defaultValue = this.$_.get(this.$data.parameterModel[parameter.Name], 'value', '');
        this.$_.set(parameter, 'Value', temporaryValue || defaultValue);

        if (parameter.Control === 'app-checkbox') {
          parameter.Value = this.$_.includes(['true', '1', true, 1], parameter.Value);
        }

        if (this.$_.isEmpty(parameter.Action)) return;

        this[parameter.Action]({
          callback: response => {
            this.$set(parameter, 'Options', response);
          }
        });
      });
    },

    setValue (input) {
      this.setTemporaryValue(input);

      if (this.$_.includes(['BEGIN_DATE_TIME', 'END_DATE_TIME'], input.id)) return;

      let target = this.$_.find(this.$data.parameters, ['Name', input.id]);

      this.$set(target, 'Value', input.value);
    },

    setTemporaryValue (input) {
      this.$_.set(this.$data.temporaryValues, input.id, input.value);
    },

    renderRequest () {
      this.REPORT__getCreate({
        reportKey: this.$data.selectedReportKey,
        parameters: this.transformedParameters
      })
      .then(request => {
        this.$data.txiApiUrl = request.url;
        this.$data.payload = request.parameters;
      });
    },

    customerSources (props) {
      // No API method available, so hardcoding options for this parameter.
      let values = [
        { Key: 0, Value: 'Control Customer' },
        { Key: 1, Value: 'Billing Customer' },
        { Key: 2, Value: 'Sale Customer' }
      ];

      props.callback(values);
    },

    getMonths (props) {
      // No API method available, so hardcoding options for this parameter.
      let values = [
        { Key: 0, Value: 'January' },
        { Key: 1, Value: 'February' },
        { Key: 2, Value: 'March' },
        { Key: 3, Value: 'April' },
        { Key: 4, Value: 'May' },
        { Key: 5, Value: 'June' },
        { Key: 6, Value: 'July' },
        { Key: 7, Value: 'August' },
        { Key: 8, Value: 'September' },
        { Key: 9, Value: 'October' },
        { Key: 10, Value: 'November' },
        { Key: 11, Value: 'December' }
      ];

      props.callback(values);
    },

    getYears (props) {
      // No API method available, so hardcoding options for this parameter.
      let startYear = datefns.format(datefns.subYears(new Date(), 50), 'YYYY');
      let endYear = datefns.format(datefns.addYears(new Date(), 2), 'YYYY');

      let values = this.$_.map(this.$_.range(startYear, endYear), year => {
        return { Key: year, Value: year };
      });

      props.callback(this.$_.reverse(values));
    }
  },

  mounted () {
    this.getReports();
  }
};
</script>

<style scoped>
#reports-view {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-template-rows: 4rem 1fr 4rem;
  grid-template-areas:
    "titlebar titlebar"
    "pool parameters"
    "footerbar footerbar";

  width: 100%;
  height: 100dvh;

  section {
    padding: 2rem;
  }

  .pool {
    grid-area: pool;

    display: flex;
    flex-direction: column;

    background: var(--body-border);
    overflow-y: scroll;

    & > * {
      max-width: 400px;
    }
  }

  .parameters {
    grid-area: parameters;
    overflow-y: scroll;

    & > * {
      max-width: 800px;
    }
  }

  .description {
    margin-top: 2em;
  }

  .title-bar {
    grid-area: titlebar;
  }

  .footer-bar {
    grid-area: footerbar;
  }
}
</style>
