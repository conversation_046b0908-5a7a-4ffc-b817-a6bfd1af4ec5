<template>
  <div id="record-view" data-page="payment" :data-search-mode="searchMode" :data-is-nested="isNested">
    <app-titlebar title="Payment" v-if="titleBarIsVisible"></app-titlebar>

    <div class="content-section" :data-is-nested="isNested">
      <app-grid-form>
        <section class="_section-group">
          <form-section title="Detail" :fixed="true">
            <div class="columns is-multiline">
              <template v-if="Number($store.state.topsCompany.settings.NumSubterminals) > 1">
                <div class="column is-12">
                  <app-subcompany id="amount" v-model="record.lSubterminalKey" :required="true">
                    Subcompany
                  </app-subcompany>
                </div>
              </template>
              <div class="column is-6">
                <app-customer v-model="record.lCustomerKey" :required="true">
                  Customer
                </app-customer>
              </div>
              <div class="column is-6">
                <app-text id="amount" v-model="record.tcAmount" :min="amountMinimum" :required="true" @change="onAmountChange">
                  Amount
                </app-text>
              </div>
              <div class="column is-3">
                <app-select id="paymentType" v-model="record.lPaymentTypeKey" :options="activePaymentTypes" keyAlias="Key" valueAlias="Value" :required="true">
                  Type
                </app-select>
              </div>
              <div class="column is-3">
                <app-date-time v-model="record.dReceived" :disabled="!canEditReceivedDate" :prepopulate="true" :required="true">
                  Received At
                </app-date-time>
              </div>
              <div class="column is-6">
                <app-number v-model="record.tcUnappliedAmount" :disabled="true">
                  Balance
                </app-number>
              </div>
              <div class="column is-4">
                <InputCardType v-model="record.lCreditCardTypeKey" />
              </div>
              <div class="column is-8">
                <app-text v-model="record.vc20PaymentInfo" maxlength="20" @change="maskPaymentInfo">
                  Check / Card Number
                </app-text>
              </div>
              <div class="column is-12">
                <app-text v-model="record.vc30CardholderName" maxlength="30">
                  Cardholder Name
                </app-text>
              </div>
              <div class="column is-4">
                <app-text v-model="record.ch4ExpiryDate" maxlength="4" placeholder="MMYY">
                  Expiry
                </app-text>
              </div>
              <div class="column is-4">
                <app-text v-model="record.vc20AuthorizationInfo" maxlength="20">
                  Authorization Number
                </app-text>
              </div>
              <div class="column is-4">
                <app-text v-model="record.vc20TransactionTag" maxlength="20">
                  Transaction Number
                </app-text>
              </div>
              <div class="column is-6">
                <app-select v-model="record.lReceiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value" :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6">
                <app-select v-model="record.lReceivedBy" :options="employees" keyAlias="Key" valueAlias="Value" :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-6">
                <app-date-time v-model="record.dReconciled" :disabled="true">
                  Reconciled Date
                </app-date-time>
              </div>
              <div class="column is-6">
                <app-username v-model="record.lReconciledBy">
                  Reconciled By
                </app-username>
              </div>
              <div class="column is-12">
                <app-textarea v-model="record.vc255Notes">
                  Notes
                </app-textarea>
              </div>
            </div>
          </form-section>

          <form-section title="Applications" :fixed="true" v-if="applications.length > 0">
            <ul class="applications">
              <li class="application" v-for="application in applications" :key="application.lApplicationKey">
                <div class="_amount">
                  {{ application.tcAmount | usd }} <span class="_type is-small">{{ application.bTow | towSale }}</span>
                </div>
                <div class="_meta is-small">
                  <a href="#" @click.prevent="viewCall(application.lCallKey)">
                    Invoice: <invoice-number :callKey="application.lCallKey" />
                  </a>
                </div>
                <div class="_actions is-small">
                  {{ application.dApplied | simpleDateTime }} by <username-show :user-key="application.lAppliedBy"></username-show>
                </div>
              </li>
            </ul>
          </form-section>
        </section>
      </app-grid-form>
    </div>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button type="primary" @click="save" :disabled="!canSave">
          Save
        </app-button>
        <app-button type="default" @click="cancel">
          Close
        </app-button>
      </template>

      <app-button v-if="isDeletable" type="danger" :outlined="true" @click="deleteRecord" :disabled="!canDelete">
        Delete
      </app-button>
    </app-footerbar>
  </div>
</template>

<script>
import Access from '@/utils/access.js';
import { mapActions } from 'vuex';
import { VALUE_ID } from '@/config.js';
import RecordView from '@/components/ancestors/RecordView.vue';
import UsernameShow from '@/components/inputs/UsernameShow.vue';
import InvoiceNumber from '@/components/features/InvoiceNumber.vue';

export default {
  name: 'price-view',

  extends: RecordView,

  props: {
    customerKey: {
      type: [String, Number],
      required: false
    }
  },

  components: {
    UsernameShow,
    InvoiceNumber
  },

  data () {
    return {
      viewConfig: {
        noun: 'Payment',
        recordKeyName: 'lPaymentKey',
        returnRouteName: 'Payments',
        addRouteName: 'AddPayment'
      },

      record: {
        lPaymentKey: '',
        lReceiptTypeKey: '',
        tcAmount: '',
        lPaymentTypeKey: '',
        vc20PaymentInfo: '',
        vc20AuthorizationInfo: '',
        dReceived: '',
        lReceivedBy: '',
        dReconciled: '',
        lReconciledBy: '',
        lCreatedBy: '',
        bTransferredToFES: '',
        bActive: '',
        dTransferredToFES: '',
        dDeposited: '',
        lDepositedBy: '',
        lProcessingTypeKey: '',
        vc30CardholderName: '',
        ch4ExpiryDate: '',
        dProcessed: '',
        vc20TransactionTag: '',
        vc20SequenceNum: '',
        lCreditCardTypeKey: '',
        vc255Notes: '',
        lCustomerKey: '',
        tcUnappliedAmount: '',
        lSubterminalKey: '',
        lCardStatusTypeKey: ''
      },

      applications: [],
      receiptTypes: [],
      paymentTypes: [],
      companies: [],
      cardStatuses: [],
      employees: []
    };
  },

  computed: {
    activePaymentTypes () {
      return this.paymentTypes.filter(paymentType => paymentType.Active);
    },

    titleBarIsVisible () {
      if (this.recordKey) return false;
      if (this.isNested) return false;

      return !this.searchMode;
    },

    canSave () {
      return Access.has('payments.edit');
    },

    canEditPayment () {
      if (this.record.dReconciled) return false;
      if (Access.has('payments.forceEdit')) return true;

      return Access.has('payments.edit') &&
        this.$_.toString(this.__state.user.Key) === this.$_.toString(this.record.lCreatedBy);
    },

    canEditReceivedDate () {
      if (!Access.has('payments.historicalReceivedDate')) return false;

      return this.canEditPayment;
    },

    canDelete () {
      return Access.has('payments.edit');
    },

    amountMinimum () {
      return Access.has('payments.restrictNegativePayments') ? 0 : false;
    }
  },

  methods: {
    ...mapActions([
      'PAYMENT__getReceiptTypes',
      'PAYMENT__getPaymentTypes',
      'PAYMENT__getApplications',
      'TOPSCOMPANY__getEmployees'
    ]),

    viewCall (key) {
      this.$router.push({
        name: 'Call',
        params: { key }
      });
    },

    maskPaymentInfo () {
      const MIN_LENGTH = 15;
      const { vc20PaymentInfo } = this.record;

      if (vc20PaymentInfo.length < MIN_LENGTH) return;

      const digits = vc20PaymentInfo.split('');
      const lastFour = digits.splice(-4);

      this.record.vc20PaymentInfo = '*'.repeat(digits.length) + lastFour.join('');
    },

    afterGetViewData () {
      this.PAYMENT__getApplications({
        key: this.record.lPaymentKey,
        success: response => {
          this.applications = response;
        }
      });
    },

    onAmountChange () {
      if (!this.record.tcUnappliedAmount) {
        this.record.tcUnappliedAmount = this.record.tcAmount;
      }
    },

    afterFillDefaultValues () {
      if (this.isNewRecord) {
        this.record.lCustomerKey = this.customerKey;
        this.record.lReceiptTypeKey = VALUE_ID.paymentReceiptType.receivable;
      }
    },

    getNow () {
      return new Promise(resolve => {
        this.$store.dispatch('__getNow', {
          callback: response => { resolve(response.Now); }
        });
      });
    },

    async fillReceivedAt () {
      if (this.isNewRecord) {
        this.record.dReceived = await this.getNow();
      }
    }
  },

  mounted () {
    this.$nextTick(() => {
      document.querySelector('#amount').focus();
    });

    this.PAYMENT__getReceiptTypes({
      callback: response => {
        this.receiptTypes = response;
      }
    });

    this.PAYMENT__getPaymentTypes({
      callback: response => {
        this.paymentTypes = response;
      }
    });

    this.TOPSCOMPANY__getEmployees({
      callback: response => {
        this.employees = response;
      }
    });

    this.fillReceivedAt();
  }
};
</script>
