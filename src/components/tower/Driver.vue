<template>
  <div id="record-view" data-page="driver" :data-search-mode="searchMode">
    <app-titlebar title="Driver" v-if="!searchMode"></app-titlebar>

    <div class="content-section">
      <app-grid-form>
        <section class="_section-group">
          <form-section title="Driver" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-4">
                <span class="control">
                  <label class="-label">Employee <i class="-required fas fa-circle-small"></i></label>
                </span>
                <div class="field has-addons" v-if="employees.length > 0">
                  <p class="control">
                    <app-select id="driver.lEmployeeKey" v-model="record.lEmployeeKey" :options="employees" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" activeAlias="Active"></app-select>
                  </p>
                  <p class="control width-auto">
                    <a @click.prevent="addEmployee" tabindex="-1" class="button is-small" :disabled="!canEditDriver">Add</a>
                  </p>
                </div>
              </div>
              <div class="column is-4">
                <app-text v-model="record.ch5DriverCode" id="driver.ch5DriverCode" maxlength="5" :disabled="isRestrictedToStatus" :required="true">
                  Driver Number
                </app-text>
              </div>
              <div class="column is-2">
                <app-shortcode v-model="record.lTruckKey" id="driver.lTruckKey" :options="trucks" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" activeAlias="Active">
                  Truck
                </app-shortcode>
              </div>
              <div class="column is-2">
                <app-select v-model="record.lDriverStatusKey" id="driver.lDriverStatusKey" :options="statuses" keyAlias="Key" valueAlias="Value" :required="true">
                  Status
                </app-select>
              </div>

              <div v-if="canSeeCommission" class="column is-6 is-left">
                <app-number v-model="record.fCommission1" id="driver.fCommission1" :disabled="isRestrictedToStatus" :required="true">
                  Commission 1 (Percent)
                </app-number>
              </div>
              <div v-if="canSeeCommission" class="column is-6">
                <app-number v-model="record.fCommission2" id="driver.fCommission2" :disabled="isRestrictedToStatus" :required="true">
                  Commission 2 (Percent)
                </app-number>
              </div>

              <div class="column is-4 is-left">
                <app-date-time v-model="record.dDOTPhysical" id="driver.dDOTPhysical" :formatter="!searchMode" :disabled="isRestrictedToStatus">
                  DOT Physical
                </app-date-time>
              </div>
              <div class="column is-4 is-button">
                <app-checkbox v-model.number="record.bPoliceCertification" id="driver.bPoliceCertification" :disabled="isRestrictedToStatus">
                  Police Certified
                </app-checkbox>
              </div>
              <div class="column is-4">
                <app-date-time v-model="record.dPoliceCertificationExpiration" id="driver.dPoliceCertificationExpiration" :formatter="!searchMode" :disabled="isRestrictedToStatus">
                  Certification Expiry
                </app-date-time>
              </div>

              <div class="column is-4 is-left">
                <app-text v-model="record.vc50UserDefined1" id="driver.vc50UserDefined1" :disabled="isRestrictedToStatus">
                  {{ TOPSCOMPANY__settings.vc15Label_Driver_UserDef1 }}
                </app-text>
              </div>
              <div class="column is-4">
                <app-text v-model="record.vc50UserDefined2" id="driver.vc50UserDefined2" :disabled="isRestrictedToStatus">
                  {{ TOPSCOMPANY__settings.vc15Label_Driver_UserDef2 }}
                </app-text>
              </div>
              <div class="column is-4">
                <app-select v-if="subterminals.length > 1" v-model="record.lSubterminalKey" :options="subterminals" id="truck.lSubterminalKey" keyAlias="0" valueAlias="1" :disabled="isRestrictedToStatus">
                  Company
                </app-select>
              </div>

              <div class="column is-12 is-left">
                <app-textarea v-model="record.vc255Notes" :maxlength="255" id="driver.vc255Notes" :disabled="isRestrictedToStatus">
                  Notes
                </app-textarea>
              </div>

              <div class="column is-4 is-left is-bottom">
                <app-text v-model="record.sPagerEmail" :disabled="true">
                  Pager
                </app-text>
              </div>
              <div class="column is-4 is-bottom">
                <app-phone v-model="record.sPhone1" :disabled="true">
                  Phone 1
                </app-phone>
              </div>
              <div class="column is-4 is-bottom">
                <app-phone v-model="record.sPhone2" :disabled="true">
                  Phone 2
                </app-phone>
              </div>
            </div> <!-- /columns -->
          </form-section>
        </section>

        <section class="_section-group">
          <form-section title="License 1" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-text v-model="record.vc20LicenseNum1" id="driver.vc20LicenseNum1" :disabled="isRestrictedToStatus">
                  Number
                </app-text>
              </div>
              <div class="column is-6">
                <app-select-state v-model="record.ch2StateKey_License1" id="driver.ch2StateKey_License1" :disabled="isRestrictedToStatus">
                  State
                </app-select-state>
              </div>
              <div class="column is-6 is-left is-bottom">
                <app-shortcode id="driver.lLicenseClassTypeKey1" v-model="record.lLicenseClassTypeKey1" :options="licenseClasses" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" activeAlias="Active">
                  Class
                </app-shortcode>
              </div>
              <div class="column is-6 is-bottom">
                <app-date-time v-model="record.dLicenseExpiration1" id="driver.dLicenseExpiration1" :formatter="!searchMode" :disabled="isRestrictedToStatus">
                  Expiry
                </app-date-time>
              </div>
            </div> <!-- /columns -->
          </form-section>

          <form-section title="License 2" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-text v-model="record.vc20LicenseNum2" id="driver.vc20LicenseNum2" :disabled="isRestrictedToStatus">
                  Number
                </app-text>
              </div>
              <div class="column is-6">
                <app-select-state v-model="record.ch2StateKey_License2" id="driver.ch2StateKey_License2" :disabled="isRestrictedToStatus">
                  State
                </app-select-state>
              </div>
              <div class="column is-6 is-left is-bottom">
                <app-shortcode id="driver.lLicenseClassTypeKey2" v-model="record.lLicenseClassTypeKey2" :options="licenseClasses" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" activeAlias="Active">
                  Class
                </app-shortcode>
              </div>
              <div class="column is-6 is-bottom">
                <app-date-time v-model="record.dLicenseExpiration2" id="driver.dLicenseExpiration2" :formatter="!searchMode" :disabled="isRestrictedToStatus">
                  Expiry
                </app-date-time>
              </div>
            </div> <!-- /columns -->
          </form-section>

          <app-modified-metadata
            class="modified-metadata"
            v-if="!isNewRecord"
            :record="record"
            :config="viewConfig"
            modifiedAtAlias="dDateLastModified"
            modifiedByAlias="lUserKey">
          </app-modified-metadata>
        </section>
      </app-grid-form>
    </div>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <!-- <app-button v-if="canReadReport" type="default" @click="openReport">Driver Assignment Report &rarr;</app-button> -->
        <!-- <app-button type="default" @click="openEmployeeRecord">Full Employee Record &rarr;</app-button> -->
        <app-button type="primary" @click="saveDriver" :disabled="!canEditDriver">Save</app-button>
        <app-button v-if="!isNewRecord" type="default" @click="duplicate" :disabled="!canDuplicateDriver">Duplicate</app-button>
        <app-button type="default" @click="cancelDriver">Close</app-button>
      </template>

      <app-button v-if="isDeletable" type="danger" @click="deleteDriver" :disabled="!canDeleteDriver">Delete</app-button>
      <app-button v-if="isUndeletable" type="default" @click="undeleteRecord" :disabled="!canDeleteDriver">Undelete</app-button>
    </app-footerbar>
  </div> <!-- /driver-view -->
</template>

<script>
import Access from '@/utils/access.js';
import { mapGetters, mapActions } from 'vuex';
import RecordView from '@/components/ancestors/RecordView.vue';

// @TODO
// Perhaps we have a link on the Driver and Truck screen that will go to the Reports screen and select the
// "Driver Assignment" report by default.

export default {
  name: 'driver-view',

  extends: RecordView,

  data () {
    return {
      viewConfig: {
        noun: 'Driver',
        recordKeyName: 'lDriverKey',
        returnRouteName: 'Drivers',
        addRouteName: 'AddDriver'
      },

      trucks: [],
      statuses: [],
      employees: [],
      subterminals: [],
      licenseClasses: [],

      record: {
        lEmployeeKey: '',
        ch5DriverCode: '',
        lTruckKey: '',
        lDriverStatusKey: '',
        fCommission1: 0,
        fCommission2: 0,
        dDOTPhysical: '',
        bPoliceCertification: '',
        dPoliceCertificationExpiration: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        lSubterminalKey: '',
        vc255Notes: '',
        vc20LicenseNum1: '',
        ch2StateKey_License1: '',
        lLicenseClassTypeKey1: '',
        dLicenseExpiration1: '',
        vc20LicenseNum2: '',
        ch2StateKey_License2: '',
        lLicenseClassTypeKey2: '',
        dLicenseExpiration2: '',
        sPagerEmail: '',
        sPhone1: '',
        sPhone2: '',
        bActive: true
      }
    };
  },

  computed: {
    ...mapGetters(['DRIVER__addProcess']),

    canSeeCommission () {
      return Access.has('drivers.commission');
    },

    canDeleteDriver () {
      if (Access.has('drivers.editStatusOnly')) return false;

      return Access.has('drivers.delete');
    },

    canEditDriver () {
      return Access.has('drivers.edit') || Access.has('drivers.editStatusOnly');
    },

    canDuplicateDriver () {
      return Access.has('drivers.edit');
    },

    isRestrictedToStatus () {
      return Access.has('drivers.editStatusOnly');
    },

    canReadReport () {
      return Access.has('reports.read');
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getTrucks',
      'TOPSCOMPANY__getEmployees',
      'TOPSCOMPANY__getDriverStatuses',
      'DRIVER__getLicenseClasses',
      'DRIVER__toggleAddProcess',
      'RECORD__getSubterminals',
      'DRIVER__cacheEmployeeKey'
    ]),

    deleteDriver () {
      this.$confirm('Deleting a driver DOES NOT remove their access to the program. Please use the User Management application to revoke access.', 'Important', {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.deleteRecord();
      }).catch(() => {
        // Handle cancel
      });
    },

    addEmployee () {
      this.DRIVER__toggleAddProcess(true);

      this.$router.push({ name: 'AddEmployee' });
    },

    cancelDriver () {
      this.DRIVER__toggleAddProcess(false);
      this.DRIVER__cacheEmployeeKey('');

      this.cancel();
    },

    saveDriver () {
      this.DRIVER__toggleAddProcess(false);
      this.DRIVER__cacheEmployeeKey('');

      this.save();
    },

    preselectEmployee () {
      if (!this.DRIVER__addProcess.active) return;

      this.$set(this.record, 'lEmployeeKey', this.DRIVER__addProcess.employeeKey);
    },

    openReport () {
      this.$router.push({ name: 'Reports', query: { 'preselectreport': '' } });
    },

    openEmployeeRecord () {
      this.$router.push({ name: 'Employee', params: { 'key': this.$route.params.key } });
    }
  },

  mounted () {
    this.TOPSCOMPANY__getDriverStatuses({
      callback: response => {
        this.statuses = response;
      }
    });

    this.DRIVER__getLicenseClasses({
      callback: response => {
        this.licenseClasses = response;
      }
    });

    this.TOPSCOMPANY__getTrucks({
      callback: response => {
        this.trucks = response;
      }
    });

    this.RECORD__getSubterminals({
      noun: this.viewConfig.noun,
      callback: response => {
        this.subterminals = response;
      }
    });

    this.TOPSCOMPANY__getEmployees({
      callback: response => {
        this.employees = response;
        this.preselectEmployee();
      }
    });
  }
};
</script>
