<template>
  <div id="prices-view">
    <section class="tabs-section">
      <tab-group v-model="$store.state.price.activeTab">
        <tab-item value="services">Services</tab-item>
        <tab-item value="customers">Customers</tab-item>
      </tab-group>
    </section>

    <ServiceList
      v-show="$store.state.price.activeTab === 'services'"
      @on-mount="getServices"
      @on-select="selectService" />

    <CustomerList
      v-show="$store.state.price.activeTab === 'customers'"
      @on-select="selectCustomer" />

    <section class="data-placeholder" v-if="!$store.getters['price.activeService'] && !activeCustomer">
      <h4>Please select a service or customer to view&nbsp;prices.</h4>
    </section>

    <template v-else>
      <app-titlebar :title="title">
        <app-button
          @click="$store.state.price.serviceSketchIsVisible = !$store.state.price.serviceSketchIsVisible"
          v-if="$store.state.price.activeTab === 'services'">
          <i class="far fa-chevron-down" v-show="!$store.state.price.serviceSketchIsVisible"></i>
          <i class="far fa-chevron-up" v-show="$store.state.price.serviceSketchIsVisible"></i>
        </app-button>
      </app-titlebar>

      <ServiceSketch v-if="$store.state.price.serviceSketchIsVisible" />

      <grid
        class="data-section"
        title=""
        key="grid"
        :grid="gridSettings"
        :data="gridData"
        :refreshedAt="refreshedAt"
        :config="viewConfig"
        :show-loader="showLoader"
        @refresh="refresh"
        @openRecord="onOpenPriceDialog($event)"
        @exportData="exportData"
        @save="save">
        <template slot="context-tools">
          <app-button type="white" @click="toggleDefaultPrices" title="Toggle Default Prices" data-flip-key="toggle-defaults">
            <i :class="defaultsButtonModifiers"></i>&nbsp;Defaults
          </app-button>
          <app-button type="white" @click="toggleInactiveRecords" title="Toggle Deleted Records" data-flip-key="toggle-deleted">
            <i :class="inactiveToggleClasses"></i>
          </app-button>
        </template>

        <template slot="floating-tools">
          <app-button type="success" size="normal" @click="add" v-if="canAdd">
            <i class="far fa-plus"></i>&nbsp;Add
          </app-button>
        </template>
      </grid>
    </template>

    <dialog :title="title" id="edit-price-popover" popover style="padding: 0;">
      <price
        :record-key="priceModal.key"
        :service-key="$store.getters['price.activeService'].Key"
        :presets="priceModal.presets"
        :isNested="true"
        :key="priceModal.key"
        @close="onClosePricePopover"
        @duplicate="onDuplicate">
      </price>
    </dialog>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>
import Price from '../Price.vue';
import Access from '@/utils/access';
import Grid from '../../features/Grid.vue';
import RecordsView from '../../ancestors/RecordsView.vue';
import ServiceList from './services/ServiceList.vue';
import CustomerList from './customers/CustomerList.vue';
import ServiceSketch from './ServiceSketch.vue';

export default {
  name: 'prices-view',

  extends: RecordsView,

  components: {
    Grid,
    Price,
    ServiceList,
    CustomerList,
    ServiceSketch
  },

  data () {
    return {
      viewConfig: {
        uuid: 'prices-view',
        noun: 'Price',
        requireFilters: false,
        shouldImmediatelyLoadData: false,
        recordKeyName: 'lPriceKey',
        readRouteName: 'Price',

        dataAdditional: {
          ShowInactive: false,
          Services: [],
          Customers: [],
          ShowDefaults: false
        }
      },

      priceModal: {
        key: null,
        presets: null
      }
    };
  },

  computed: {
    canAdd () {
      return Access.has('price.edit');
    },

    title () {
      if (this.$store.state.price.activeTab === 'services') {
        let segments = [this.$store.getters['price.activeService'].Name];

        if (this.$store.getters['price.activeService'].bCalculated) {
          segments.push('Calculated');
        };

        return segments.join(' · ') || '';
      }

      if (this.$store.state.price.activeTab === 'customers') {
        return this.$store.getters['price.activeCustomer'].Value || '';
      }

      return '';
    },

    defaultsButtonModifiers () {
      return {
        'fal': true,
        'fa-square-check': this.viewConfig.dataAdditional.ShowDefaults,
        'fa-square': !this.viewConfig.dataAdditional.ShowDefaults
      };
    }
  },

  methods: {
    add () {
      this.onOpenPriceDialog({ lPriceKey: null });
    },

    onDuplicate (record) {
      this.priceModal.presets = record;
      this.add();

      setTimeout(() => {
        this.priceModal.presets = null;
      }, 5000);
    },

    onOpenPriceDialog ({ lPriceKey }) {
      document.querySelector('#edit-price-popover').showPopover();

      this.priceModal.key = lPriceKey;
    },

    onClosePricePopover () {
      document.querySelector('#edit-price-popover').hidePopover();

      this.priceModal.key = null;
      this.priceModal.presets = null;

      this.loadData();
    },

    getUnitTypes () {
      this.$store.dispatch('PRICE__getUnitTypes', {
        success: response => {
          this.$store.state.price.unitTypes = response;
        }
      });
    },

    async getServices () {
      this.$store.dispatch('PRICE__getFilteredServices', {
        success: response => {
          this.$store.state.price.services = response;
          return true;
        }
      });
    },

    selectService (key) {
      this.$store.state.price.activeServiceKey = key;
      this.$store.state.price.activeCustomerKey = null;

      this.viewConfig.dataAdditional.Services = [key];
      this.viewConfig.dataAdditional.Customers = [];

      this.loadData();
    },

    selectCustomer (key) {
      this.$store.state.price.activeCustomerKey = key;
      this.$store.state.price.activeServiceKey = null;
      this.$store.state.price.serviceSketchIsVisible = false;

      this.viewConfig.dataAdditional.Services = [];
      this.viewConfig.dataAdditional.Customers = [key];

      this.loadData();
    },

    async beforeLoadData () {
      return new Promise(resolve => {
        if (this.viewConfig.dataAdditional.Services) {
          this.gridSettings.Filters = this.$_.remove(this.gridSettings.Filters, filter => {
            return ['sService'].includes(filter.FieldId);
          });
        }

        if (this.viewConfig.dataAdditional.Customers) {
          this.gridSettings.Filters = this.$_.remove(this.gridSettings.Filters, filter => {
            return ['sCustomer'].includes(filter.FieldId);
          });
        }

        resolve();
      });
    },

    toggleDefaultPrices () {
      this.viewConfig.dataAdditional.ShowDefaults = !this.viewConfig.dataAdditional.ShowDefaults;

      this.loadData();
    }
  },

  mounted () {
    this.getUnitTypes();

    setTimeout(() => {
      if (this.$store.state.price.activeServiceKey) {
        this.selectService(this.$store.state.price.activeServiceKey);
      }

      if (this.$store.state.price.activeCustomerKey) {
        this.selectCustomer(this.$store.state.price.activeCustomerKey);
      }
    }, 500);
  },

  provide () {
    return {
      getServices: this.getServices
    };
  }
};
</script>
