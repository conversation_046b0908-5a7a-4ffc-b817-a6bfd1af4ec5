<template>
  <li class="filter" data-customer
    :data-active="$store.getters['price.activeCustomer'].Key === customer.Key"
    @click="$emit('on-select', customer.Key)">

    <div class="name" v-html="customer.HighlightedValue || customer.Value"></div>
  </li>
</template>

<script>
export default {
  name: 'customer-item',

  props: {
    customer: {
      type: Object,
      required: true
    }
  }
};
</script>
