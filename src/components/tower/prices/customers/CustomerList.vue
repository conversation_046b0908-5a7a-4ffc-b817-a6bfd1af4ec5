<template>
  <ul class="filters-section">
    <li class="filter" data-filter>
      <input type="search" v-model="$store.state.price.customerFilter" placeholder="Filter" />
    </li>
    <CustomerItem
      v-for="customer in filteredCustomers"
      :customer="customer"
      :key="customer.Key"
      @on-select="$emit('on-select', $event)" />
  </ul>
</template>

<script>
import Fuse from 'fuse.js';
import CustomerItem from './CustomerItem.vue';

let customerFuse = null;

export default {
  name: 'customer-list',

  components: {
    CustomerItem
  },

  computed: {
    filteredCustomers () {
      if (!!customerFuse && !!this.$store.state.price.customerFilter) {
        const customers = customerFuse.search(this.$store.state.price.customerFilter).map(customer => customer.item);

        return customers.map(({ HighlightedValue, ...customer }) => {
          customer.HighlightedValue = customer.Value.replace(new RegExp(this.$store.state.price.customerFilter, 'gi'), '<mark>$&</mark>');
          return customer;
        });
      }

      return this.$store.state.price.customers.map(({ HighlightedValue, ...customer }) => customer);
    }
  },

  watch: {
    '$store.state.price.customers' () {
      customerFuse = new Fuse(this.$store.state.price.customers, {
        keys: ['Value'],
        threshold: 0.3
      });
    }
  },

  methods: {
    getCustomers () {
      this.$store.dispatch('PRICE__getCustomers', {
        success: response => {
          this.$store.state.price.customers = response;
        }
      });
    }
  },

  mounted () {
    this.getCustomers();
  }
};
</script>
