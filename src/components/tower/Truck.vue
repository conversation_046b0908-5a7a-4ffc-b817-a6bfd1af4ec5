<template>
  <div id="record-view" data-page="truck" :data-search-mode="searchMode">
    <app-titlebar title="Truck" v-if="!searchMode"></app-titlebar>

    <div class="content-section">
      <app-grid-form>
        <section class="_section-group">
          <form-section title="Truck" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-3">
                <app-text v-model="record.ch10TruckNumber" id="truck.ch10TruckNumber" :disabled="isRestrictedToStatus" :required="true">
                  Number
                </app-text>
              </div>
              <div class="column is-3">
                <app-shortcode v-if="statuses" id="truck.lTruckStatusTypeKey" v-model="record.lTruckStatusTypeKey" :options="statuses" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :required="true">
                  Status
                </app-shortcode>
              </div>
              <div class="column is-3">
                <app-shortcode id="truck.lRevenueTypeKey" v-model="record.lRevenueTypeKey" :options="revenueTypes" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :required="true">
                  Revenue Type
                </app-shortcode>
              </div>
              <div class="column is-3">
                <app-shortcode id="truck.lTruckTypeKey" v-model="record.lTruckTypeKey" :options="truckTypes" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :required="true">
                  Type
                </app-shortcode>
              </div>
              <div class="column is-3 is-left">
                <app-shortcode id="truck.lMakeKey" v-model="record.lMakeKey" :options="makes" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" @change="onVehicleMakeChange">
                  Make
                </app-shortcode>
              </div>
              <div class="column is-3">
                <app-shortcode v-if="models" id="truck.lModelKey" v-model="record.lModelKey" :options="models" :disabled="isRestrictedToStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
                  Model
                </app-shortcode>
              </div>
              <div class="column is-3">
                <app-text v-model="record.iYear" id="truck.iYear" maxlength="4" :disabled="isRestrictedToStatus">
                  Year
                </app-text>
              </div>
              <div class="column is-3">
                <app-text v-model="record.vc20AssetNumber" id="truck.vc20AssetNumber" :disabled="isRestrictedToStatus">
                  Asset Number
                </app-text>
              </div>
              <div class="column is-3 is-left is-bottom">
                <app-text v-model="record.lOdometer" id="truck.lOdometer" :disabled="isRestrictedToStatus">
                  Odometer
                </app-text>
              </div>
              <div class="column is-3 is-bottom">
                <app-date-time v-model="record.dOdometerDate" id="truck.dOdometerDate" :formatter="!searchMode" :disabled="isRestrictedToStatus">
                  Odometer Date
                </app-date-time>
              </div>
              <div class="column is-6 is-bottom">
                <label>VIN</label>
                <div class="field has-addons">
                  <p class="control">
                    <input v-model="record.vc25VIN" type="text" class="input" id="truck.vc25VIN" maxlength="17" :disabled="isRestrictedToStatus" />
                  </p>
                  <p class="control width-auto">
                    <button @click.prevent="decodeVin" class="button" :disabled="!canDecodeVin"><i class="far fa-wand-magic-sparkles"></i></button>
                  </p>
                </div>
              </div>
            </div> <!-- /columns -->
          </form-section>
        </section>

        <section class="_section-group">
          <form-section title="Miscellaneous" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-12 is-left">
                <app-text v-model="record.vc100CurrentLocation" id="truck.vc100CurrentLocation" maxlength="100" :disabled="isRestrictedToStatus">
                  Location
                </app-text>
              </div>
              <div class="column is-12 is-left">
                <app-text v-model="record.ch10Assignment" id="truck.ch10Assignment" :disabled="isRestrictedToStatus">
                  Assignment
                </app-text>
              </div>
              <div class="column is-12 is-left">
                <app-text v-model="record.vc100CurrentDestination" id="truck.vc100CurrentDestination" maxlength="100" :disabled="isRestrictedToStatus">
                  Destination
                </app-text>
              </div>
              <div class="column is-6 is-left">
                <app-text v-model="record.vc50UserDefined1" id="truck.vc50UserDefined1" :disabled="isRestrictedToStatus">
                  {{ TOPSCOMPANY__settings.vc15Label_Truck_UserDef1 }}
                </app-text>
              </div>
              <div class="column is-6">
                <app-text v-model="record.vc50UserDefined2" id="truck.vc50UserDefined2" :disabled="isRestrictedToStatus">
                  {{ TOPSCOMPANY__settings.vc15Label_Truck_UserDef2 }}
                </app-text>
              </div>
              <div class="column is-12 is-left">
                <app-textarea v-model="record.vc255Notes" :maxlength="255" id="truck.vc255Notes" :disabled="isRestrictedToStatus">
                  Notes
                </app-textarea>
              </div>
              <div class="column is-6 is-left is-bottom">
                <app-text v-model="record.vc100TrackingUnitData" id="truck.vc100TrackingUnitData" maxlength="100" :disabled="isRestrictedToStatus">
                  Tracking Unit Data
                </app-text>
              </div>
              <div class="column is-6 is-bottom">
                <div v-if="subterminals.length > 1">
                  <app-select v-model="record.lSubterminalKey" id="truck.lSubterminalKey" :options="subterminals" keyAlias="0" valueAlias="1">
                    Company
                  </app-select>
                </div>
              </div>
            </div> <!-- /columns -->
          </form-section>

          <app-modified-metadata
            class="modified-metadata"
            v-if="!isNewRecord"
            :record="record"
            :config="viewConfig"
            modifiedAtAlias="dDateLastModified"
            modifiedByAlias="lUserKey">
          </app-modified-metadata>
        </section>
      </app-grid-form>
    </div>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <!-- <app-button v-if="canReadReport" type="default" @click="openReport">Driver Assignment Report &rarr;</app-button> -->
        <app-button type="primary" @click="save" :disabled="!canEditTruck">Save</app-button>
        <app-button v-if="!isNewRecord" type="default" @click="duplicate" :disabled="!canDuplicateTruck">Duplicate</app-button>
        <app-button type="default" @click="cancel">Close</app-button>
      </template>

      <app-button v-if="isDeletable" type="danger" @click="deleteRecord" :disabled="!canDeleteTruck">Delete</app-button>
      <app-button v-if="isUndeletable" type="default" @click="undeleteRecord" :disabled="!canDeleteTruck">Undelete</app-button>
    </app-footerbar>
  </div> <!-- /truck-view -->
</template>

<script>
import Access from '@/access.js';
import { mapActions } from 'vuex';
import RecordView from '../ancestors/RecordView.vue';

/**
 * TODO: Perhaps we have a link on the Driver and Truck screen
 * that will go to the Reports screen and select the
 * "Driver Assignment" report by default.
 */

export default {
  name: 'truck-view',

  extends: RecordView,

  data () {
    return {
      makes: [],
      models: [],
      statuses: [],
      truckTypes: [],
      revenueTypes: [],
      subterminals: [],
      viewConfig: {
        noun: 'Truck',
        recordKeyName: 'lTruckKey',
        returnRouteName: 'Trucks',
        addRouteName: 'AddTruck'
      },
      record: {
        ch10TruckNumber: '',
        lTruckStatusTypeKey: '',
        lRevenueTypeKey: '',
        lTruckTypeKey: '',
        lMakeKey: '',
        lModelKey: '',
        iYear: '',
        vc20AssetNumber: '',
        lOdometer: '',
        dOdometerDate: '',
        vc25VIN: '',
        vc100CurrentLocation: '',
        ch10Assignment: '',
        vc100CurrentDestination: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        vc255Notes: '',
        vc100TrackingUnitData: '',
        lSubterminalKey: '',
        bActive: true
      }
    };
  },

  computed: {
    canDeleteTruck () {
      if (Access.has('trucks.editStatusOnly')) return false;

      return Access.has('trucks.delete');
    },

    canEditTruck () {
      return Access.has('trucks.edit') || Access.has('trucks.editStatusOnly');
    },

    canDuplicateTruck () {
      return Access.has('trucks.edit');
    },

    canDecodeVin () {
      return Access.has('trucks.decodeVin');
    },

    isRestrictedToStatus () {
      return Access.has('trucks.editStatusOnly');
    },

    canReadReport () {
      return Access.has('reports.read');
    }
  },

  watch: {
    'record.lMakeKey': {
      immediate: true,
      handler () {
        this.getModels();
      }
    }
  },

  methods: {
    ...mapActions([
      'CALL__getMakes',
      'CALL__getModels',
      'TOPSCALL__decodeVIN',
      'CALL__getTruckTypes',
      'TRUCK__getRevenueTypes',
      'TOPSCOMPANY__getTruckStatuses',
      'RECORD__getSubterminals'
    ]),

    onVehicleMakeChange () {
      this.$set(this.$data.record, 'lModelKey', '');
    },

    getModels () {
      if (!this.$data.record.lMakeKey) return;

      this.CALL__getModels({
        makeKey: this.$data.record.lMakeKey,
        callback: response => {
          this.$data.models = response;
        }
      });
    },

    decodeVin () {
      this.TOPSCALL__decodeVIN({
        vin: this.$data.record.vc25VIN,
        callback: response => {
          this.$set(this.record, 'iYear', response.Year);
          this.$set(this.record, 'lMakeKey', response.MakeKey);
          this.$set(this.record, 'lModelKey', response.ModelKey);

          this.getModels();
        }
      });
    },

    afterFillDefaultValues () {
      this.getModels();
    },

    openReport () {
      this.$router.push({ name: 'Reports', query: { 'preselectreport': '' } });
    }
  },

  mounted () {
    this.CALL__getMakes({
      callback: response => {
        this.$data.makes = response;
      }
    });

    this.TRUCK__getRevenueTypes({
      callback: response => {
        this.$data.revenueTypes = response;
      }
    });

    this.CALL__getTruckTypes({
      callback: response => {
        this.$data.truckTypes = response;
      }
    });

    this.TOPSCOMPANY__getTruckStatuses({
      callback: response => {
        this.$data.statuses = response;
      }
    });

    this.RECORD__getSubterminals({
      noun: this.$data.viewConfig.noun,
      callback: response => {
        this.$data.subterminals = response;
      }
    });
  }
};
</script>
