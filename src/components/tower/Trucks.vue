<template>
  <div id="records-view" data-page="trucks">
    <transition name="fade" mode="out-in">
      <grid
        title="Trucks"
        key="grid"
        :grid="gridSettings"
        :data="gridData"
        :refreshedAt="refreshedAt"
        :noun="viewConfig.noun"
        :config="viewConfig"
        :multiselect="true"
        :show-loader="showLoader"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @save="save">
        <template slot="context-tools">
          <spotlight-control
            :noun="viewConfig.noun"
            @setFilters="setFilters"
            @resetFilters="getSettings">
          </spotlight-control>

          <app-button type="white" v-if="canDelete" @click="deleteRecord" title="Delete"><i class="far fa-trash-alt"></i></app-button>
          <app-button type="white" v-if="canRestore" @click="undeleteRecord" title="Restore"><i class="fal fa-undo"></i></app-button>
          <app-button type="white" v-if="canDuplicate" @click="duplicate" title="Duplicate"><i class="fal fa-clone"></i></app-button>
          <app-button type="white" @click="toggleInactiveRecords" title="Toggle Deleted Records"><i :class="inactiveToggleClasses"></i></app-button>
        </template>

        <template slot="floating-tools">
          <app-button type="success" size="normal" @click="add" v-if="canAdd">
            <i class="far fa-plus"></i>&nbsp;Add
          </app-button>
        </template>
      </grid>
    </transition>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>
import Access from '@/utils/access.js';
import Grid from '../features/Grid.vue';
import RecordsView from '../ancestors/RecordsView.vue';

export default {
  name: 'trucks-view',

  extends: RecordsView,

  components: {
    Grid
  },

  data () {
    return {
      actionableRecord: {},
      actionsVisible: false,

      viewConfig: {
        key: null,
        uuid: 'trucks-screen',
        noun: 'Truck',
        requireData: false,
        requireFilters: false,
        recordKeyName: 'lTruckKey',
        readRouteName: 'Truck',
        addRouteName: 'AddTruck'
      }
    };
  },

  computed: {
    canDelete () {
      if (!this.isDeletable) return false;
      if (Access.has('trucks.editStatusOnly')) return false;

      return this.oneRecordIsSelected && Access.has('trucks.delete');
    },

    canRestore () {
      if (this.isDeletable) return false;
      if (Access.has('trucks.editStatusOnly')) return false;

      return this.oneRecordIsSelected && Access.has('trucks.delete');
    },

    canAdd () {
      if (Access.has('trucks.editStatusOnly')) return false;

      return Access.has('trucks.edit');
    },

    canDuplicate () {
      return this.oneRecordIsSelected && this.canAdd;
    }
  }
};
</script>
