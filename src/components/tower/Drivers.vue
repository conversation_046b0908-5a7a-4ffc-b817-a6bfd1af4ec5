<template>
  <div id="records-view" data-page="drivers">
    <transition name="fade" mode="out-in">
      <grid
        title="Drivers"
        key="grid"
        :grid="gridSettings"
        :data="gridData"
        :refreshedAt="refreshedAt"
        :noun="viewConfig.noun"
        :config="viewConfig"
        :multiselect="true"
        :show-loader="showLoader"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @save="save">
        <template slot="context-tools">
          <spotlight-control
            :noun="viewConfig.noun"
            @setFilters="setFilters"
            @resetFilters="getSettings">
          </spotlight-control>

          <app-button type="white" v-if="canDelete" @click="deleteDriver" title="Delete" data-flip-key="delete-driver"><i class="far fa-trash-alt"></i></app-button>
          <app-button type="white" v-if="canRestore" @click="undeleteRecord" title="Restore" data-flip-key="restore-driver"><i class="fal fa-undo"></i></app-button>
          <app-button type="white" v-if="canDuplicate" @click="duplicate" title="Duplicate" data-flip-key="duplicate-driver"><i class="fal fa-clone"></i></app-button>
          <app-button type="white" @click="toggleInactiveRecords" title="Toggle Deleted Records" data-flip-key="toggle-deleted"><i :class="inactiveToggleClasses"></i></app-button>
        </template>

        <template slot="floating-tools">
          <app-button type="success" size="normal" @click="add" v-if="canAdd">
            <i class="far fa-plus"></i>&nbsp;Add
          </app-button>
        </template>
      </grid>
    </transition>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>
import Access from '@/access.js';
import Grid from '../features/Grid.vue';
import RecordsView from '../ancestors/RecordsView.vue';

export default {
  name: 'drivers-view',

  extends: RecordsView,

  components: {
    Grid
  },

  data () {
    return {
      viewConfig: {
        key: null,
        uuid: 'drivers-screen',
        noun: 'Driver',
        requireData: false,
        requireFilters: false,
        recordKeyName: 'lDriverKey',
        readRouteName: 'Driver',
        addRouteName: 'AddDriver'
      }
    };
  },

  computed: {
    canDelete () {
      if (!this.isDeletable) return false;
      if (Access.has('drivers.editStatusOnly')) return false;

      return this.oneRecordIsSelected && Access.has('drivers.delete');
    },

    canRestore () {
      if (this.isDeletable) return false;
      if (Access.has('drivers.editStatusOnly')) return false;

      return this.oneRecordIsSelected && Access.has('drivers.delete');
    },

    canAdd () {
      if (Access.has('drivers.editStatusOnly')) return false;

      return Access.has('drivers.edit');
    },

    canDuplicate () {
      return this.oneRecordIsSelected && this.canAdd;
    }
  },

  methods: {
    deleteDriver () {
      this.$confirm('Deleting a driver DOES NOT remove their access to the program. Please use the User Management application to revoke access.', 'Important', {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.deleteRecord();
      }).catch(() => {
        // Handle cancel
      });
    }
  }
};
</script>
