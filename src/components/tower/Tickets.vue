<template>
  <div id="records-view" data-page="tow-tickets">
    <transition name="fade" mode="out-in">
      <grid
        title="Tow Tickets"
        key="grid"
        :grid="gridSettings"
        :data="gridData"
        :refreshedAt="refreshedAt"
        :noun="viewConfig.noun"
        :config="viewConfig"
        :multiselect="false"
        :show-loader="showLoader"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @save="save">
        <template slot="context-tools">
          <app-button type="white" @click="goToTools" v-if="canViewTools">Tools</app-button>
          <spotlight-control
            :noun="viewConfig.noun"
            @setFilters="setFilters"
            @resetFilters="getSettings">
          </spotlight-control>
        </template>
      </grid>
    </transition>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>
import Access from '@/utils/access';
import Grid from '../features/Grid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';

export default {
  name: 'tickets-view',

  extends: RecordsView,

  components: {
    Grid
  },

  data () {
    return {
      actionableRecord: {},
      actionsVisible: false,

      viewConfig: {
        key: null,
        uuid: 'tickets-screen',
        noun: 'TowTicket',
        requireData: false,
        requireFilters: true,
        recordKeyName: 'lTowTicketKey',
        readRouteName: 'Ticket',
        addRouteName: 'AddTicket'
      }
    };
  },

  computed: {
    canDelete () {
      if (!this.isDeletable) return false;
      if (Access.has('drivers.editStatusOnly')) return false;

      return this.oneRecordIsSelected && Access.has('drivers.delete');
    },

    canRestore () {
      if (this.isDeletable) return false;
      if (Access.has('drivers.editStatusOnly')) return false;

      return this.oneRecordIsSelected && Access.has('drivers.delete');
    },

    canAdd () {
      if (Access.has('drivers.editStatusOnly')) return false;

      return Access.has('drivers.edit');
    },

    canDuplicate () {
      return this.oneRecordIsSelected && this.canAdd;
    },

    canViewTools () {
      return Access.has('tickets.edit');
    }
  },

  methods: {
    goToTools () {
      this.$router.push({ name: 'TicketTools' });
    }
  }
};
</script>
