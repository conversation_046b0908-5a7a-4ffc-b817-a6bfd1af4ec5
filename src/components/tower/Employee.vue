<template>
  <div id="record-view" data-page="employee" :data-search-mode="searchMode">
    <app-titlebar title="Employee" v-if="!searchMode"></app-titlebar>

    <div class="content-section">
      <app-grid-form>
        <section class="_section-group">
          <form-section title="Employee" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-4 is-left">
                <app-text v-model="record.vc30Name" id="employee.vc30Name" maxlength="30" :required="true">
                  Name
                </app-text>
              </div>
              <div class="column is-4">
                <app-shortcode id="employee.lEmployeeTypeKey" v-model="record.lEmployeeTypeKey" :options="employeeTypes" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" activeAlias="Active" :required="true">
                  Type
                </app-shortcode>
              </div>
              <div class="column is-4">
                <app-text v-model="record.ch10EmployeeNum" id="employee.ch10EmployeeNum" maxlength="10">
                  Employee Number
                </app-text>
              </div>
              <div class="column is-4 is-left is-bottom">
                <app-date-time v-model="record.dDateOfBirth" id="employee.dDateOfBirth" :formatter="!searchMode">
                  Date of Birth
                </app-date-time>
              </div>
              <div class="column is-4 is-bottom">
                <template v-if="'sUser' in record">
                  <app-text v-model="record.sUser" @blur="onUserBlur">
                    User ID
                  </app-text>
                </template>
              </div>
              <div class="column is-4 is-bottom">
                <app-data-point label="Driver">
                  {{ record.sDriver }}
                </app-data-point>
              </div>
            </div> <!-- /columns -->
          </form-section>

          <form-section title="Address" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-12 is-left">
                <app-text v-model="record.vc30Address1" id="employee.vc30Address1" maxlength="30">
                  Address 1
                </app-text>
              </div>
              <div class="column is-12 is-left">
                <app-text v-model="record.vc30Address2" id="employee.vc30Address2" maxlength="30">
                  Address 2
                </app-text>
              </div>
              <div class="column is-6 is-left is-bottom">
                <app-text v-model="record.vc30City" id="employee.vc30City" maxlength="30">
                  City
                </app-text>
              </div>
              <div class="column is-3 is-bottom">
                <app-select-state v-model="record.ch2StateKey" id="employee.ch2StateKey">
                  State
                </app-select-state>
              </div>
              <div class="column is-3 is-bottom">
                <app-text v-model="record.vc10Zip" id="employee.vc10Zip" maxlength="10">
                  Zip
                </app-text>
              </div>
            </div> <!-- /columns -->
          </form-section>
        </section>

        <section class="_section-group">
          <form-section title="Contact" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-4 is-left">
                <app-phone v-model="record.vc20Phone1" id="employee.vc20Phone1" maxlength="20">
                  Phone 1
                </app-phone>
              </div>
              <div class="column is-4">
                <app-phone v-model="record.vc20Phone2" id="employee.vc20Phone2" maxlength="20">
                  Phone 2
                </app-phone>
              </div>
              <div class="column is-4">
                <app-text v-model="record.vc50Email" id="employee.vc50Email" maxlength="50">
                  Email
                </app-text>
              </div>
              <div class="column is-4 is-left is-bottom">
                <app-text v-model="record.vc50PagerEmail" id="employee.vc50PagerEmail" maxlength="50">
                  Pager Email
                </app-text>
              </div>
              <div class="column is-4 is-bottom">
                <app-text v-model="record.vc20PagerPIN" id="employee.vc20PagerPIN" maxlength="20">
                  Pager PIN
                </app-text>
              </div>
              <div class="column is-4 is-bottom">
                <app-number v-model="record.lPagerMaxCharacters" id="employee.lPagerMaxCharacters" min="0">
                  Pager Max Length
                </app-number>
              </div>
            </div> <!-- /columns -->
          </form-section>

          <form-section title="Miscellaneous" :fixed="true">
            <div class="columns is-multiline">
              <div class="column is-4 is-left">
                <app-date-time v-model="record.dDrugTestDate" id="employee.dDrugTestDate" :formatter="!searchMode">
                  Drug Test Date
                </app-date-time>
              </div>
              <div class="column is-4">
                <app-text v-model="record.vc50UserDefined1" id="employee.vc50UserDefined1" maxlength="50">
                  {{ TOPSCOMPANY__settings.vc15Label_Employee_UserDef1 }}
                </app-text>
              </div>
              <div class="column is-4">
                <app-text v-model="record.vc50UserDefined2" id="employee.vc50UserDefined2" maxlength="50">
                  {{ TOPSCOMPANY__settings.vc15Label_Employee_UserDef2 }}
                </app-text>
              </div>
              <div class="column is-12 is-left">
                <app-textarea v-model="record.vc255Notes" :maxlength="255" id="employee.vc255Notes">
                  Notes
                </app-textarea>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="record.sAdditionalNotes" :maxlength="1000" id="employee.sAdditionalNotes">
                  Additional Notes
                </app-textarea>
              </div>
            </div> <!-- /columns -->
          </form-section>

          <app-modified-metadata
            v-if="!isNewRecord"
            class="modified-metadata"
            :record="record"
            :config="viewConfig"
            modifiedAtAlias="dDateLastModified"
            modifiedByAlias="lUserKey">
          </app-modified-metadata>
        </section>
      </app-grid-form>
    </div>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button type="primary" @click="save" :disabled="!canEditEmployee">Save</app-button>
        <app-button v-if="!isNewRecord" type="default" @click="duplicate" :disabled="!canEditEmployee">Duplicate</app-button>
        <app-button type="default" @click="cancelEmployee">Close</app-button>
      </template>

      <app-button v-if="isDeletable" type="danger" @click="deleteEmployee" :disabled="!canDeleteEmployee">Delete</app-button>
      <app-button v-if="isUndeletable" type="default" @click="undeleteRecord" :disabled="!canDeleteEmployee">Undelete</app-button>
    </app-footerbar>
  </div>
</template>

<script>
import Access from '@/utils/access.js';
import { mapGetters, mapActions } from 'vuex';
import RecordView from '@/components/ancestors/RecordView.vue';

export default {
  name: 'employee-view',

  extends: RecordView,

  data () {
    return {
      viewConfig: {
        noun: 'Employee',
        recordKeyName: 'lEmployeeKey',
        returnRouteName: 'Employees',
        addRouteName: 'AddEmployee'
      },

      record: {
        lEmployeeKey: null,
        vc30Name: '',
        lEmployeeTypeKey: '',
        c11SocialSecurityNum: '',
        dDateOfBirth: '',
        ch10EmployeeNum: '',
        dDrugTestDate: '',
        vc50PagerEmail: '',
        vc20PagerPIN: '',
        lPagerMaxCharacters: '',
        vc30Address1: '',
        vc30Address2: '',
        vc30City: '',
        ch2StateKey: '',
        vc10Zip: '',
        vc20Phone1: '',
        vc20Phone2: '',
        vc50Email: '',
        vc255Notes: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        lUserKey_LastModifiedBy: null,
        dDateLastModified: '',
        bActive: true,
        lUserKey: '',
        sUser: '',
        sDriver: ''
      },

      truck: {},
      employeeTypes: []
    };
  },

  computed: {
    ...mapGetters(['DRIVER__addProcess']),

    canDeleteEmployee () {
      return Access.has('employees.delete');
    },

    canEditEmployee () {
      return Access.has('employees.edit');
    }
  },

  methods: {
    ...mapActions([
      '__getNow',
      'USER__getKey',
      'TOPSCOMPANY__getEmployeeTypes',
      'DRIVER__cacheEmployeeKey'
    ]),

    async beforeSave () {
      this.record.lUserKey = await this.getUserKey();
    },

    async onUserBlur () {
      this.record.lUserKey = await this.getUserKey();
    },

    async getUserKey (userKey = this.record.sUser) {
      if (!userKey) { return ''; }

      return new Promise(resolve => {
        this.USER__getKey({
          id: userKey,
          success: response => {
            resolve(response.Key);
          }
        });
      });
    },

    deleteEmployee () {
      this.$confirm('Deleting an employee DOES NOT remove their access to the program. Please use the User Management application to revoke access.', 'Important', {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.deleteRecord();
      }).catch(() => {
        // Handle cancel
      });
    },

    cancelEmployee () {
      if (this.DRIVER__addProcess.active) {
        this.$router.push({ name: 'AddDriver' });
      } else {
        this.cancel();
      }
    },

    create () {
      this.__getNow({
        callback: response => {
          this.$set(this.record, 'dDateLastModified', response.Now);
          this.$set(this.record, 'lUserKey_LastModifiedBy', this.__state.user.Key);
          this.$set(this.record, 'bActive', true);

          this.RECORD__create({
            noun: this.viewConfig.noun,
            data: this.record,
            callback: response => {
              if (this.DRIVER__addProcess.active) {
                this.DRIVER__cacheEmployeeKey(response.lEmployeeKey);
                this.$router.push({ name: 'AddDriver' });
              } else {
                this.$router.push({ name: this.viewConfig.returnRouteName });
              }
            }
          });
        }
      });
    }
  },

  mounted () {
    this.TOPSCOMPANY__getEmployeeTypes({
      callback: response => {
        this.employeeTypes = response;
      }
    });
  }
};
</script>
