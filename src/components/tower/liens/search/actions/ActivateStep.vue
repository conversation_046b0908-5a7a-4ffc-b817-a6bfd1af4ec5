<template>
  <Expandable>
    <template slot="summary">Activate Step</template>

    <ActionSelect
      v-model="selectedStepKey"
      :options="$store.getters['lien.activeSteps']"
      valueAlias="vc50Name"
      keyAlias="lLienStepKey">
      Step
    </ActionSelect>

    <ActionButton @click="onClick" :disabled="disabled">
      Activate
    </ActionButton>
  </Expandable>
</template>

<script>
import Expandable from '../../inputs/Expandable.vue';
import ActionButton from '../../inputs/Button.vue';
import ActionSelect from '../../inputs/Select.vue';

export default {
  name: 'activate-step',

  components: {
    Expandable,
    ActionButton,
    ActionSelect
  },

  props: {
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  data () {
    return {
      selectedStepKey: null
    };
  },

  methods: {
    onClick () {
      this.$store.dispatch('LIENBATCH__activateStep', {
        stepKey: this.selectedStepKey,
        callKeys: this.selectedRecordKeys,
        callback: () => {
          this.$emit('on-batch-step-activate');
        }
      });
    }
  },

  mounted () {
    this.selectedStepKey = this.$store.state.lien.selectedStepKey;
  }
};
</script>
