<template>
  <Expandable>
    <template slot="summary">Start Lien</template>

    <ActionSelect
      v-model="startProcessKey"
      :options="$store.getters['lien.activeProcesses']"
      :disabled="$store.getters['lien.activeProcesses'].length === 0"
      keyAlias="Key"
      valueAlias="Value">
      Process
    </ActionSelect>

    <ActionButton @click="onClick" :disabled="!canClick">
      Start
    </ActionButton>

  </Expandable>
</template>

<script>
import Expandable from '../../inputs/Expandable.vue';
import ActionSelect from '../../inputs/Select.vue';
import ActionButton from '../../inputs/Button.vue';

export default {
  name: 'start-lien',

  components: {
    Expandable,
    ActionSelect,
    ActionButton
  },

  props: {
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  data () {
    return {
      startProcessKey: null
    };
  },

  computed: {
    canClick () {
      return !this.disabled && this.startProcessKey;
    }
  },

  methods: {
    onClick () {
      this.$store.dispatch('LIENBATCH__start', {
        processKey: this.startProcessKey,
        callKeys: this.selectedRecordKeys,
        callback: () => {
          this.$emit('on-batch-lien-start');
        }
      });
    }
  },

  mounted () {
    if (this.$store.getters['lien.activeProcesses'].length) {
      this.startProcessKey = this.$store.getters['lien.activeProcesses'][0].Key;
    }
  }
};
</script>
