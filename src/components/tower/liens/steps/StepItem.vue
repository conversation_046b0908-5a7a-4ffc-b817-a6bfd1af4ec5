<template>
  <Expandable
    :open="step.lLienStepKey === $store.state.lien.selectedStepKey"
    :observe-external-open-state="true"
    @click="$emit('on-select')">

    <template slot="summary">
      <div class="name">{{ step.vc50Name }}</div>
      <div class="optional is-small" v-if="!step.bRequired">Optional</div>
    </template>

    <AppTip v-if="step.activationDescription">
      <p class="small">{{ step.activationDescription }}</p>
    </AppTip>

    <!-- Individual tasks -->
    <ActionButton
      :popovertarget="taskListId"
      :disabled="!step.Tasks.length || !canProcess"
      variant="blue">
      <span>Process calls&hellip;</span>
    </ActionButton>

    <TaskList
      :id="taskListId"
      :step="$store.getters['lien.selectedStep']"
      :security-access="securityAccess"
      :selected-record-keys="selectedRecordKeys"
      :did-process-all-tasks="didProcessAllTasks && processedCalls.length > 0"
      @on-process-task-fail="onProcessTaskFail($event)"
      @on-process-task-success="onProcessTaskSuccess($event)"
      @on-did-process-all-tasks="onDidProcessAllTasks($event)"
      @on-process-all-tasks-reset="onProcessAllTasksReset($event)"
      @on-click-step-complete="onClickStepComplete()" />

    <div class="v-space"></div>

    <AppTip v-if="failedCalls.length">
      <i class="fas fa-exclamation-triangle pure-red" slot="icon"></i>
      <p class="small">{{ failedCalls.length }} call<span v-if="failedCalls.length > 1">s</span> failed to process. <span v-if="failedCalls.length === 1">It</span><span v-else>They</span> will be excluded from step&nbsp;actions.</p>
    </AppTip>

    <!-- Step actions -->
    <AppTip v-if="step.Tasks.length && !processedCalls.length">
      <i class="far fa-exclamation-triangle pure-orange" slot="icon"></i>
      <p class="small">No tasks processed yet. Be mindful with step&nbsp;actions.</p>
    </AppTip>

    <CompleteAction
      :step="step"
      :selected-record-keys="processedCallKeys.length ? processedCallKeys : selectedRecordKeys"
      :disabled="!securityAccess.canCompleteStep"
      @on-step-complete="$emit('on-step-complete')"
      ref="completeAction" />

    <SkipAction
      :step="step"
      :selected-record-keys="selectedRecordKeys"
      :disabled="!securityAccess.canSkipStep"
      @on-step-skip="$emit('on-step-skip')" />

    <HoldAction
      :step="step"
      :selected-record-keys="selectedRecordKeys"
      :disabled="!securityAccess.canHoldStep"
      @on-step-hold="$emit('on-step-hold')" />

  </Expandable>
</template>

<script>
import TaskList from '../tasks/TaskList.vue';
import CompleteAction from '../steps/actions/Complete.vue';
import HoldAction from '../steps/actions/Hold.vue';
import SkipAction from '../steps/actions/Skip.vue';
import ActionButton from '../inputs/Button.vue';
import Expandable from '../inputs/Expandable.vue';

export default {
  name: 'step-item',

  components: {
    TaskList,
    CompleteAction,
    HoldAction,
    SkipAction,
    ActionButton,
    Expandable
  },

  props: {
    step: {
      type: Object,
      required: true,
      default: () => ({})
    },
    securityAccess: {
      type: Object,
      required: true,
      default: () => {}
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  data () {
    return {
      failedCalls: [],
      processedCalls: [],
      processedDocuments: [],
      didProcessAllTasks: false
    };
  },

  computed: {
    taskListId () {
      return `task-list-${this.step.lLienStepKey}`;
    },

    itemClasses () {
      return {
        'step': true,
        'is-highlighted': this.step.lLienStepKey === this.$store.state.lien.selectedStepKey
      };
    },

    canProcess () {
      return this.step.lLienStepKey === this.$store.state.lien.selectedStepKey &&
        this.selectedRecordKeys.length > 0;
    },

    processedCallKeys () {
      return this.processedCalls.map(call => call.Key);
    }
  },

  methods: {
    onProcessTaskFail (result) {
      this.failedCalls.push(...result);
      this.$emit('on-process-task-fail', result);
    },

    onProcessTaskSuccess (result) {
      if ('calls' in result) {
        this.processedCalls.push(...result.calls);
      }

      if ('documents' in result) {
        this.processedDocuments.push(...result.documents);
      }
    },

    onProcessAllTasksReset () {
      this.failedCalls = [];
      this.processedCalls = [];
      this.processedDocuments = [];
      this.didProcessAllTasks = false;
    },

    async onDidProcessAllTasks () {
      this.$emit('on-process-all-tasks');
      this.didProcessAllTasks = true;

      // Wait for onProcessTaskSuccess() to respond to events
      setTimeout(async () => {
        if (this.processedDocuments.length > 0) {
          await this.addLetters();
        }
      }, 1000);
    },

    onClickStepComplete () {
      document.getElementById(this.taskListId).hidePopover();
      this.$refs.completeAction.$el.click();
    },

    addLetters () {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('LIENBATCH__addLetters', {
          calls: this.processedCalls,
          callback: () => {
            resolve(true);
          }
        });
      });
    }
  }
};
</script>

<style scoped>
.optional {
  color: var(--pure-blue);
  background-color: hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.1);
  border-radius: 1rem;
  padding: 0rem 0.75rem;
}
</style>
