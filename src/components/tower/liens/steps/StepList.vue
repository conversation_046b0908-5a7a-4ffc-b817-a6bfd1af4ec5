<template>
  <ul class="steps" v-if="$store.getters['lien.activeSteps'].length > 0">
    <StepItem
      v-for="step in $store.getters['lien.activeSteps']"
      :step="step"
      :security-access="securityAccess"
      :selected-record-keys="selectedRecordKeys"
      :key="step.lLienStepKey"
      @on-select="onSelect(step)"
      @on-step-complete="$emit('on-step-complete')"
      @on-step-hold="$emit('on-step-hold')"
      @on-process-task-fail="$emit('on-process-task-fail', $event)" />
  </ul>
</template>

<script>
import StepItem from './StepItem.vue';

export default {
  name: 'step-list',

  components: {
    StepItem
  },

  props: {
    securityAccess: {
      type: Object,
      required: true,
      default: () => {}
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  watch: {
    '$store.state.lien.selectedProcessKey': {
      handler: function () {
        this.getSteps(this.$store.state.lien.selectedProcessKey);
        this.onSelect(this.$store.state.lien.selectedStep);
      },
      immediate: true
    }
  },

  methods: {
    fetchSteps (processKey) {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('LIENPROCESS__getSteps', {
          processKey: processKey,
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    async getSteps (processKey) {
      if (!processKey) return [];

      let steps = await this.fetchSteps(processKey);

      steps.forEach(step => {
        if (step.iActivationQty && step.sActivationUnits && step.sActivationBasis) {
          let description = `Activated ${step.iActivationQty} ${step.sActivationUnits} after ${step.sActivationBasis}`;
          description = description.charAt(0).toUpperCase() + description.slice(1).toLowerCase();

          step.activationDescription = description;
        } else {
          step.activationDescription = '';
        }
      });

      this.$store.state.lien.steps = steps;
    },

    onSelect (step) {
      if (!step) return;
      if (!step.lLienStepKey) return;

      this.$store.state.lien.selectedStepKey = step.lLienStepKey;

      this.$emit('on-select', step);
    }
  }
};
</script>

<style scoped>
.steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
</style>
