<template>
  <li class="label-item">

    <section class="card" :data-selected="isSelected">
      <template v-for="lineNumber in $store.state.lien.selectedLabelProfile.tMaxLinesPerLabel">
        <input class="line"
          v-model="localLabel[lineNumber]"
          @keyup="updateLabel"
          :maxlength="$store.state.lien.selectedLabelProfile.tMaxCharsPerLabel"
          :key="lineNumber"
          :placeholder="'Line ' + lineNumber" />
      </template>
    </section>
    </section>

    <footer class="footer">
      <button class="select-button" @click="toggleIsSelected" title="Select/deselect label">
        <i class="far fa-circle-check" v-show="isSelected"></i>
        <i class="far fa-circle-dashed" v-show="!isSelected"></i>
      </button>
      <button class="delete-button" @click="deleteLabel" title="Remove label">
        <i class="far fa-xmark"></i>
      </button>
    </footer>

  </li>
</template>

<script>
export default {
  name: 'label-item',

  props: {
    label: {
      type: Object,
      required: true,
      default: () => ({})
    },

    index: {
      type: Number,
      required: true
    }
  },

  data () {
    return {
      localLabel: { ...this.label }
    };
  },

  computed: {
    isSelected () {
      return this.$store.state.lien.selectedLabelKeys.includes(this.label.key);
    }
  },

  watch: {
    label: {
      handler (value) {
        this.localLabel = { ...value };
      },
      deep: true
    }
  },

  methods: {
    updateLabel () {
      this.$set(this.$store.state.lien.labels, this.index, this.localLabel);
    },

    toggleIsSelected () {
      if (this.$store.state.lien.selectedLabelKeys.includes(this.label.key)) {
        this.$store.state.lien.selectedLabelKeys = this.$store.state.lien.selectedLabelKeys.filter(key => key !== this.label.key);
      } else {
        this.$store.state.lien.selectedLabelKeys.push(this.label.key);
      }
    },

    deleteLabel () {
      this.$store.state.lien.labels.splice(this.index, 1);
    }
  }
};
</script>

<style scoped>
.label-item {
  .card {
    display: grid;

    padding: 0.5rem;
    margin-bottom: 0.25rem;
    border: 1px solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 0 0 0 hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.5);
    transition: box-shadow 0.2s;

    &[data-selected="true"] {
      box-shadow:
        0 0 0 0.25rem hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.5),
        0 0.25rem 2rem 0.25rem hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.1);
    }

    .line {
      border: none;
    }
  }

  .footer {
    padding: 0;
    text-align: right;
    background: transparent;

    .select-button,
    .delete-button {
      appearance: none;
      background-color: transparent;
      border: none;
      color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.5);
    }
  }
}
</style>
