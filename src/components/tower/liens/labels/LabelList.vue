<template>
  <ul id="label-list">
    <LabelItem
      v-for="(label, index) in $store.state.lien.labels"
      :label="label"
      :index="index"
      :key="index" />
  </ul>
</template>

<script>
import LabelItem from './LabelItem.vue';

export default {
  name: 'label-list',

  components: {
    LabelItem
  },

  watch: {
    '$store.state.lien.labels': {
      handler (value) {
        this.$store.state.lien.selectedLabelKeys = this.$store.state.lien.selectedLabelKeys.filter(key => {
          return this.$store.state.lien.labels.some(label => label.key === key);
        });
      },
      deep: true
    }
  }
};
</script>

<style scoped>
#label-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(30ch, 1fr));
  gap: 1.618rem 1rem;
}
</style>
