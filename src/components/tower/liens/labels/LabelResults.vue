<template>
  <section id="label-results">
    <app-placeholder v-if="$store.state.lien.labels.length === 0">
      No labels added yet.
    </app-placeholder>

    <LabelList />
  </section>
</template>

<script>
import LabelList from './LabelList.vue';

export default {
  name: 'label-results',

  components: {
    LabelList
  }
};
</script>

<style scoped>
#label-results {
  padding: 2rem;
  height: 100%;
  overflow-y: auto;

  background:
    url("data:image/svg+xml,%3Csvg width='6' height='6' viewBox='0 0 6 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='1.4' fill-rule='evenodd'%3E%3Cpath d='M5 0h1L0 6V5zM6 5v1H5z'/%3E%3C/g%3E%3C/svg%3E"),
    radial-gradient(
      circle at center top,
      hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.05),
      hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.15));
}
</style>
