<template>
  <button
    type="button"
    :disabled="disabled"
    :data-variant="variant"
    @click="$emit('click')">
    <slot></slot>
  </button>
</template>

<script>
export default {
  name: 'button-input',

  props: {
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    variant: {
      type: String,
      required: false,
      default: 'default'
    }
  }
};
</script>

<style scoped>
button {
  padding: 0.25rem 0.5rem;
  background-color: white;
  font-size: 1rem;
  border: 0.1rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.25rem;
  box-shadow: 0 0.25rem 0.25rem hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.05);
  opacity: 1;
  transform: scale(1);
  transition: all 0.2s ease-out;
  will-change: opacity, transform;

  &:disabled {
    box-shadow: none;
    opacity: 0.5;
    transform: scale(0.95);
  }

  &[data-variant="blue"] {
    color: white;
    background-color: var(--pure-blue);
  }
}
</style>
