<template>
  <label>
    <input
      type="checkbox"
      v-model="$value"
      @change="$emit('change', $event)"
      :disabled="disabled">
    <div><slot></slot></div>
  </label>
</template>

<script>
export default {
  name: 'checkbox-input',

  props: {
    value: {},
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  }
};
</script>

<style scoped>
label {
  display: grid;
  grid-template-columns: 1em 1fr;
  gap: 0.5rem;
  align-items: center;

  padding: 0.25rem 0.5rem;
  width: 100%;
  border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.5rem;
}

div {
  white-space: nowrap;
}
</style>
