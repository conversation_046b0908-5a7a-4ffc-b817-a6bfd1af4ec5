<template>
  <label>
    <div><slot></slot></div>
    <input
      type="number"
      v-model="$value"
      :disabled="disabled"
      :min="min"
      :max="max"
      :step="step"
      @change="$emit('change', $event)">
  </label>
</template>

<script>
export default {
  name: 'number-input',

  props: {
    value: {},
    min: {
      type: [Number, String],
      required: false,
      default: 0,
      coerce (value) {
        return typeof value === 'string' ? Number(value) : value;
      }
    },
    max: {
      type: [Number, String],
      required: false,
      default: Infinity,
      coerce (value) {
        return typeof value === 'string' ? Number(value) : value;
      }
    },
    step: {
      type: [Number, String],
      required: false,
      default: 1,
      coerce (value) {
        return typeof value === 'string' ? Number(value) : value;
      }
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  }
};
</script>

<style scoped>
label {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.5rem;

  padding: 0.25rem 0.5rem;
  width: 100%;
  border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.5rem;
}

div {
  white-space: nowrap;
}

input {
  font-size: 1rem;
  color: var(--pure-blue) !important;
  background: transparent !important;
  border: 0;
}
</style>
