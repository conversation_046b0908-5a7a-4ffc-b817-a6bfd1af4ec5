<template>
  <dialog
    :id="id"
    class="task-list"
    popover
    title="Task Processor"
    @toggle="onListToggle">

    <section class="bulk-actions">
      <app-date-time v-model="lettersSentDate" v-if="anyHasLettersToSend" :date-picker="true">
        Letters Sent Date
      </app-date-time>
      <app-select v-model="labelFormat" :options="$store.getters['lien.activeLabelLayouts']" v-if="anyHasLabelsToPrint" keyAlias="Key" valueAlias="Value" :empty-option="true">
        Label Format
      </app-select>
      <app-number v-model="startLabel" v-if="anyHasLabelsToPrint" min="1">
        Start Label
      </app-number>
      <app-date-time v-model="setDate" v-if="anyHasDateToSet" :date-picker="true">
        Date to Set
      </app-date-time>
      <app-text v-model="certificationNumber" v-if="anyUsesCertificationNumber">
        Certification Number
      </app-text>

      <div class="buttons">
        <template v-if="!didProcessAllTasks">
          <ActionButton @click="processAllTasks" variant="blue" :disabled="!canProcessAllTasks">
            Process tasks
          </ActionButton>
        </template>

        <ActionButton @click="$emit('on-click-step-complete')" variant="blue" v-if="didProcessAllTasks">
          Complete step&hellip;
        </ActionButton>
      </div>
    </section>

    <div class="v-space"></div>

    <ul class="tasks" v-if="step">
      <TaskItem
        v-for="(task, index) in tasksProxy"
        :ref="'task-' + index"
        :task="task"
        :letters-sent-date="lettersSentDate"
        :label-format="labelFormat"
        :start-label="startLabel"
        :set-date="setDate"
        :certification-number="certificationNumber"
        :selected-record-keys="selectedRecordKeys"
        :key="index"
        @on-process-task-fail="$emit('on-process-task-fail', $event)"
        @on-process-task-success="$emit('on-process-task-success', $event)" />
    </ul>

  </dialog>
</template>

<script>
import { VALUE_ID } from '@/config.js';
import TaskItem from './TaskItem.vue';
import ActionButton from '@/components/tower/liens/inputs/Button.vue';

export default {
  name: 'task-list',

  components: {
    TaskItem,
    ActionButton
  },

  props: {
    id: {
      type: String,
      required: true
    },
    step: {
      type: Object,
      required: false,
      default: () => ({})
    },
    securityAccess: {
      type: Object,
      required: true,
      default: () => {}
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    },
    didProcessAllTasks: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  data () {
    return {
      lettersSentDate: null,
      labelFormat: null,
      startLabel: null,
      setDate: null,
      certificationNumber: null
    };
  },

  computed: {
    tasksProxy () {
      const tasks = this.$_.get(this.step, 'Tasks', []);

      return tasks.filter(task => !task.bSkipBatch);
    },

    anyHasLettersToSend () {
      return this.tasksProxy.some(task => task.lLienTaskTypeKey === VALUE_ID.lienTaskType.letter);
    },

    anyHasLabelsToPrint () {
      // Disabled in case we need it back
      // return this.tasksProxy.some(task => task.lLienTaskTypeKey === VALUE_ID.lienTaskType.label);
      return false;
    },

    anyHasDateToSet () {
      return this.tasksProxy.some(task => task.lLienTaskTypeKey === VALUE_ID.lienTaskType.setDate);
    },

    anyUsesCertificationNumber () {
      return this.tasksProxy.some(task => task.bUsesCertificationNum);
    },

    canProcessAllTasks() {
      if (this.tasksProxy.length === 0) return false;
      if (this.didProcessAllTasks) return false;
      if (this.anyHasLettersToSend && !this.lettersSentDate) return false;
      if (this.anyHasLabelsToPrint && !this.labelFormat) return false;
      if (this.anyHasDateToSet && !this.setDate) return false;
      if (this.anyUsesCertificationNumber && !this.certificationNumber) return false;

      return true;
    }
  },

  methods: {
    onListToggle ({ newState }) {
      if (newState === 'open') {
        this.lettersSentDate = null;
        this.labelFormat = null;
        this.startLabel = null;
        this.setDate = null;
        this.certificationNumber = null;

        this.resetAllTaskStates();
      }
    },

    async resetAllTaskStates () {
      const promisedTasks = Object.keys(this.$refs).map(async (ref) => {
        if (ref.startsWith('task-')) {
          return this.$refs[ref][0].resetState();
        }
      });

      try {
        this.$emit('on-process-all-tasks-reset');
        await Promise.all(promisedTasks.map(promise => promise.catch(response => response)));
      } catch (error) {
        console.error('Error resetting tasks:', error);
      }
    },

    async processAllTasks () {
      this.$emit('on-process-all-tasks-reset');

      const promisedTasks = Object.keys(this.$refs).map(async (ref) => {
        if (ref.startsWith('task-')) {
          return this.$refs[ref][0].processTask();
        }
      });

      try {
        await Promise.all(promisedTasks.map(promise => promise.catch(response => response)));
        this.$emit('on-did-process-all-tasks');
      } catch (error) {
        console.error('Error processing tasks:', error);
      }
    }
  }
};
</script>

<style scoped>
.task-list {
  padding: 1rem;
  inline-size: clamp(45ch, calc(100% - 2rem), 60ch) !important;
  block-size: clamp(30rem, 30rem, calc(100dvh - 2rem));
  overflow-y: auto;
  box-shadow:
    inset 0 0 0 0.1rem hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1),
    var(--box-shadow-100);
}

.bulk-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  padding: 1rem;
  background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.75rem;

  .buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
  }

  .control {
    padding: 0.2rem 0.5rem;
    background: white;
    border-radius: 0.25rem;

    .button,
    .-input {
      border: none;
    }
  }
}

.tasks {
  display: flex;
  flex-direction: column;
  gap: 1em;
}
</style>
