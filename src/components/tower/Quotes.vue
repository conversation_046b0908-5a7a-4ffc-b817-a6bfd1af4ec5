<template>
  <div id="records-view" data-page="quotes">
    <grid
      title="Quotes"
      key="grid"
      :grid="gridSettings"
      :data="gridData"
      :refreshedAt="refreshedAt"
      :config="viewConfig"
      :multiselect="true"
      :actions="false"
      :show-loader="showLoader"
      @refresh="refresh"
      @openRecord="openRecord"
      @exportData="exportData"
      @save="save">
      <template slot="context-tools">
        <spotlight-control
          :noun="viewConfig.noun"
          @setFilters="setFilters"
          @resetFilters="getSettings">
        </spotlight-control>
        <app-button type="white" v-if="canDuplicate" @click="duplicate" title="Duplicate"><i class="fal fa-clone"></i></app-button>
      </template>

      <template slot="floating-tools">
        <app-button type="success" size="normal" @click="addQuote" v-if="canAdd">
          <i class="far fa-plus"></i>&nbsp;Add
        </app-button>
      </template>
    </grid>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>
import Access from '@/access.js';
import Grid from '../features/Grid.vue';
import { mapGetters, mapActions } from 'vuex';
import RecordsView from '../ancestors/RecordsView.vue';

export default {
  name: 'quotes-view',

  extends: RecordsView,

  components: { Grid },

  data () {
    return {
      viewConfig: {
        uuid: 'quotes-view',
        noun: 'Quote',
        requireFilters: false,
        recordKeyName: 'lKey',
        readRouteName: 'Quote',
        addRouteName: 'AddQuote'
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'RECORDS__settings'
    ]),

    canDuplicate () {
      return Access.has('quote.write') && this.oneRecordIsSelected;
    },

    canAdd () {
      return Access.has('quote.write');
    }
  },

  methods: {
    ...mapActions(['__selectRecords']),

    addQuote () {
      this.$router.push({ name: 'AddQuote' });
    }
  }
};
</script>
