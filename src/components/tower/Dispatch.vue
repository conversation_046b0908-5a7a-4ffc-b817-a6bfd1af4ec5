<template>
  <div class="dispatch">
    <div class="grid-container">
      <transition name="fade" mode="out-in">
        <grid
          title="Unassigned Calls"
          key="grid"
          class="grid unassigned"
          :grid="unassignedGridSettings"
          :data="unassignedGridData"
          :refreshedAt="refreshedAt"
          :config="viewConfig"
          :quickAssign="true"
          :actions="true"
          :show-loader="showLoader"
          :findButtonVisible="false"
          :page-results="false"
          @refresh="refresh"
          @openRecord="openRecord"
          @exportData="exportData"
          @save="save"
          @getActions="getActions">
          <template slot="context-tools">
            <app-button type="white" @click="toggleAllAppointments()"><i :class="appointmentsToggleClasses"></i>&nbsp;&nbsp;All Appointments</app-button>
          </template>
        </grid>
      </transition>

      <div class="resizer"></div>

      <transition name="fade" mode="out-in">
        <grid
          title="Assigned Calls"
          key="grid"
          class="grid assigned"
          :grid="assignedGridSettings"
          :data="assignedGridData"
          :refreshedAt="refreshedAt"
          :config="viewConfig"
          :actions="true"
          :show-loader="showLoader"
          :findButtonVisible="false"
          :page-results="false"
          @refresh="refresh"
          @openRecord="openRecord"
          @exportData="exportData"
          @save="save"
          @getActions="getActions">
          <template slot="context-tools">
            <app-button type="white" @click="toggleDispatchUnits">Dispatch Units</app-button> &nbsp;
            <app-button type="white" @click="notify">Notify</app-button>
          </template>

          <template slot="floating-tools">
            <app-button type="success" size="normal" @click="addCall" v-if="canAddCall">
              <i class="far fa-plus"></i>&nbsp;Add
            </app-button>
          </template>
        </grid>
      </transition>
    </div> <!-- /grid-container -->

    <actions
      :show="actionsVisible"
      :record="actionableRecord"
      :call-key="actionableRecord.lCallKey"
      :subterminal-key="actionableRecord.lSubterminalKey"
      :dispatch-key="actionableRecord.lDispatchKey"
      @close="toggleActions"
      @notify="notify">
    </actions>

    <app-modal title="Payments" @close="$_PaymentMixin_togglePayments" :show="paymentsVisible">
      <payment-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="paymentsVisible"
        @close="$_PaymentMixin_togglePayments(false)">
      </payment-section>
    </app-modal>

    <app-modal title="Holds" @close="$_HoldsMixin_toggleHolds" :show="holdsVisible">
      <holds-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="holdsVisible"
        @close="$_HoldsMixin_toggleHolds(false)">
      </holds-section>
    </app-modal>

    <app-modal title="Dispatch Units" @close="toggleDispatchUnits" :show="dispatchUnitsVisible" :pad="false">
      <DispatchUnitsController />
    </app-modal>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div> <!-- /dispatch -->
</template>

<script>
import is from 'is_js';
import Access from '@/access.js';
import { mapGetters } from 'vuex';
import keyCodes from '@/keycodes.js';
import Actions from '../features/Actions.vue';
import Grid from '../features/DispatchGrid.vue';
import HoldsSection from '../call/HoldsSection.vue';
import HoldsMixin from '@/mixins/holds_mixin.js';
import { GRID_KEY, COMPANY_ID } from '@/config.js';
import PaymentSection from '../call/PaymentSection.vue';
import PaymentMixin from '@/mixins/payment_mixin.js';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import DispatchUnitsController from '../dispatchunits/Controller.vue';

import {
  get,
  find,
  isEmpty,
  debounce
} from 'lodash';

export default {
  name: 'dispatch',

  extends: RecordsView,

  mixins: [
    HoldsMixin,
    PaymentMixin
  ],

  components: {
    Grid,
    Actions,
    HoldsSection,
    PaymentSection,
    DispatchUnitsController
  },

  /* TODO: Handle these terminal settings
  TRMlDispatchRefresh_Default;
  TRMbAdvancedDriverTruckFilter;
  TRMbAllowCallNumAsInvoiceNum;

  UPRvc50LastLocationName
  UPRvc100DispatchDumpDir
  UPRlMaxFindRecords
  UPRlHighlightETA
  UPRlDispatchRefresh
  UPRLOClSubterminalKey
  */

  data () {
    return {
      viewConfig: {
        key: null,
        uuid: 'dispatch-screen',
        noun: 'Dispatches',
        recordKeyName: 'lCallKey',
        readRouteName: 'Call',
        requireFilters: false,
        dataAdditional: {
          SubcompanyKey: '', // Set in mounted()
          ShowApptCallMinutes: ''
        }
      },

      refreshInterval: 0,
      refreshTimer: null,
      actionableRecord: {},
      actionsVisible: false,
      dispatchUnitsVisible: false,
      showApptCallMinutesCache: '',
      allAppointmentsVisible: false,
      assignedCallGridKey: GRID_KEY.assigned,
      unassignedCallGridKey: GRID_KEY.unassigned,

      gridContainer: {
        endY: 0,
        startY: 0,
        maxHeight: 100,
        minHeight: 100,
        resizeTarget: {},
        isResizing: false
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'USER__state',
      'TOPSCOMPANY__settings'
    ]),

    unassignedGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.$data.unassignedCallGridKey]);

      return settings || {};
    },

    unassignedGridData () {
      return get(this.$data.viewData, `Grids[${this.$data.unassignedCallGridKey}]`, []);
    },

    assignedGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.$data.assignedCallGridKey]);

      return settings || {};
    },

    assignedGridData () {
      return get(this.$data.viewData, `Grids[${this.$data.assignedCallGridKey}]`, []);
    },

    appointmentsToggleClasses () {
      return {
        'fal': true,
        'fa-check-square': this.$data.allAppointmentsVisible,
        'fa-square': !this.$data.allAppointmentsVisible
      };
    },

    canAddCall () {
      if (Number(this.__state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT) {
        return !Access.has('calls.duplicate');
      }

      return true;
    }
  },

  methods: {
    resetTimer () {
      this.clearRefreshTimer();

      if (this.$data.refreshInterval > 0) {
        this.$data.refreshTimer = window.setInterval(() => {
          this.refresh();
        }, this.$data.refreshInterval * 1000);
      }
    },

    clearRefreshTimer () {
      if (this.$data.refreshTimer !== null) {
        window.clearInterval(this.$data.refreshTimer);
        this.$data.refreshTimer = null;
      }
    },

    beforeLoadData () {
      return new Promise((resolve, reject) => {
        this.$data.allAppointmentsVisible = window.sessionStorage.getItem('all-appointments-visible') === 'true';

        this.$data.viewConfig.dataAdditional.ShowApptCallMinutes = this.$data.allAppointmentsVisible
          ? ''
          : this.$data.showApptCallMinutesCache;

        resolve();
      });
    },

    afterLoadData () {
      this.resetTimer();

      setTimeout(() => {
        this.initializeGridResizing();
      }, 1000);
    },

    afterGetTOPSCompanySettings () {
      this.setUserCompanySettings();
    },

    setUserCompanySettings () {
      let intervalProfile = get(this.USER__state.Profile, 'lDispatchRefresh', null);
      let intervalDefault = get(this.TOPSCOMPANY__settings, 'lDispatchRefresh_Default', null);
      let interval = intervalProfile || intervalDefault;

      this.$data.refreshInterval = is.falsy(interval) ? 0 : interval;

      this.resetTimer();

      this.$data.viewConfig.dataAdditional.ShowApptCallMinutes = get(this.USER__state.Profile, 'lShowApptCall', '');

      if (isEmpty(this.$data.viewConfig.dataAdditional.ShowApptCallMinutes)) {
        this.$data.viewConfig.dataAdditional.ShowApptCallMinutes = get(this.TOPSCOMPANY__settings, 'lShowApptCall_Default', '');
      }

      this.$data.showApptCallMinutesCache = this.$data.viewConfig.dataAdditional.ShowApptCallMinutes;
    },

    getActions (record) {
      this.$data.actionableRecord = record;
      this.$data.actionsVisible = true;
    },

    toggleActions () {
      if (is.truthy(this.$data.actionsVisible)) this.refresh();

      this.$data.actionsVisible = !this.$data.actionsVisible;
    },

    addCall () {
      this.$router.push({ name: 'AddCall' });
    },

    notify (payload) {
      this.$router.replace({
        name: 'Notify',
        query: {
          callKey: this.$_.get(payload, 'callKey', ''),
          dispatchKey: this.$_.get(payload, 'dispatchKey', ''),
          dispatchDriverKey: this.$_.get(payload, 'dispatchDriverKey', ''),
          dispatchTruckKey: this.$_.get(payload, 'dispatchTruckKey', ''),
          dispatchEmployeeKey: this.$_.get(payload, 'dispatchEmployeeKey', ''),
          reason: this.$_.get(payload, 'reason', ''),
          returnTo: 'Dispatch'
        }
      });
    },

    toggleDispatchUnits () {
      this.$data.dispatchUnitsVisible = !this.$data.dispatchUnitsVisible;
    },

    handleKeyboardEvents (event) {
      if (event.altKey && event.keyCode === keyCodes.a) {  // Alt + A
        this.addCall();
      }
    },

    mouseDownHandler (event) {
      if (event.target.className === 'resizer') {
        this.$data.gridContainer.isResizing = true;
        this.$data.gridContainer.startY = event.y;
      }
    },

    mouseUpHandler (event) {
      this.$data.gridContainer.isResizing = false;
    },

    mouseMoveHandler (event) {
      if (!this.$data.gridContainer.isResizing) return;

      event.preventDefault();
      this.$data.gridContainer.endY = event.y;
      this.$data.gridContainer.resizeTarget.style['flex-basis'] = event.y + 'px';

      this.saveGridSize();
    },

    initializeGridResizing () {
      if (!isEmpty(this.$data.gridContainer.resizeTarget)) return;

      this.$set(this.gridContainer, 'resizeTarget', document.querySelector('.unassigned'));

      if (is.truthy(window.sessionStorage.getItem('Dispatch-grid-size'))) {
        this.$data.gridContainer.resizeTarget.style['flex-basis'] = window.sessionStorage.getItem('Dispatch-grid-size') + 'px';
      }
    },

    saveGridSize: debounce(function () {
      window.sessionStorage.setItem('Dispatch-grid-size', this.$data.gridContainer.endY);
    }, 2 * 1000),

    toggleAllAppointments (value = !this.$data.allAppointmentsVisible) {
      this.$data.allAppointmentsVisible = value;
      this.$data.viewConfig.dataAdditional.ShowApptCallMinutes = value ? '' : this.$data.showApptCallMinutesCache;

      window.sessionStorage.setItem('all-appointments-visible', this.$data.allAppointmentsVisible);

      this.loadData();
    }
  },

  created () {
    document.addEventListener('keydown', this.handleKeyboardEvents);
    document.addEventListener('mousedown', this.mouseDownHandler);
    document.addEventListener('mouseup', this.mouseUpHandler);
    document.addEventListener('mousemove', this.mouseMoveHandler);
  },

  mounted () {
    this.$data.viewConfig.dataAdditional.SubcompanyKey = this.__state.user.Profile.lSubterminalKey || '';
    this.$store.state.addCall.shouldStayOnSave = false;
  },

  destroyed () {
    this.clearRefreshTimer();

    document.removeEventListener('keydown', this.handleKeyboardEvents);
    document.removeEventListener('mousedown', this.mouseDownHandler);
    document.removeEventListener('mouseup', this.mouseUpHandler);
    document.removeEventListener('mousemove', this.mouseMoveHandler);
  }
};
</script>

<style scoped>
.grid-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  @include hide-scrollbars();

  .grid {
    flex: 1;
    overflow: hidden;

    @include hide-scrollbars();

    &.unassigned {
      flex-basis: 50vh;
    }

    &.assigned {
      flex-basis: 50vh;
    }
  }

  .resizer {
    flex-basis: 5px;
    background: var(--divider-bg);
    cursor: ns-resize;
  }
}
</style>
