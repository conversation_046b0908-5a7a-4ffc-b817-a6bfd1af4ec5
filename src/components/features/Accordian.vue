<template>
  <div class="accordian" :data-expanded="expanded">
    <div class="_title">
      <slot></slot>
      <div class="_controls">
        <slot name="controls"></slot>
        <app-button class="_toggler" @click="expanded = !expanded">
          <i :class="iconClasses"></i>
        </app-button>
      </div>
    </div>
    <transition
      @enter="enter"
      @leave="leave"
      :css="false">
      <div class="_body" v-show="expanded">
        <slot name="body"></slot>
      </div>
    </transition>
  </div>
</template>

<script>
import { EVENT_TOGGLE_ACCORDIAN_GROUP } from '@/config.js';
import { TweenLite } from 'gsap';

export default {
  name: 'accordian-control',

  props: {
    group: { default: '' },
    expandOnMount: { default: false }
  },

  data () {
    return {
      expanded: false
    };
  },

  computed: {
    iconClasses () {
      return {
        'far': true,
        'fa-chevron-up': this.$data.expanded,
        'fa-chevron-down': !this.$data.expanded
      };
    }
  },

  methods: {
    enter (el, done) {
      // Save the initial height for smooth animation
      const height = el.scrollHeight;

      // Start from collapsed state
      el.style.height = '0px';
      el.style.opacity = '0';
      el.style.overflow = 'hidden';

      // Animate to expanded state
      TweenLite.to(el, 0.4, {
        height: height,
        ease: 'Power3.easeOut',
        onComplete: () => {
          TweenLite.to(el, 0.2, {
            opacity: 1,
            onComplete: done
          });
        }
      });
    },

    leave (el, done) {
      // Save the current height for smooth animation
      const height = el.scrollHeight;
      el.style.height = `${height}px`;
      el.style.overflow = 'hidden';

      // Animate to collapsed state
      TweenLite.to(el, 0.2, {
        opacity: 0,
        onComplete: () => {
          TweenLite.to(el, 0.4, {
            height: 0,
            ease: 'Power3.easeOut',
            onComplete: done
          });
        }
      });
    }
  },

  mounted () {
    this.$data.expanded = this.expandOnMount;

    this.$hub.$on(EVENT_TOGGLE_ACCORDIAN_GROUP, payload => {
      if (payload.group === this.group) {
        this.$data.expanded = payload.expanded;
      }
    });
  }
};
</script>

<style scoped>
.accordian {
  &[data-expanded] {
    padding-bottom: 0.5rem;
    background-color: color-mix(in oklch, var(--pure-blue) 5%, transparent);
  }
}

._title {
  position: relative;

  display: grid;
  grid-template-columns: 1fr min-content;

  padding: 1rem;

  &:hover {
    ._controls {
      opacity: 1;
    }
  }

}

._controls {
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;

  display: flex;
  gap: 0.5rem;

  padding: 0.5rem !important;
  background-color: var(--dark-blue);
  border-radius: 0.25rem;
  box-shadow: var(--box-shadow-50);
  opacity: 0;
  transition: opacity 0.2s ease-in-out;

  button,
  input {
    color: white;
    background: hsla(0, 0%, 100%, 0.1);
    border: 0;

    &[data-style="danger"] {
      background: var(--danger);
    }

    &[data-style="success"] {
      background: var(--success);
    }

    &:hover {
      background: hsla(0, 0%, 100%, 0.2);
      transition: background 0.2s ease-in-out;

      &[data-style="danger"] {
        background: var(--danger-lighten);
      }

      &[data-style="success"] {
        background: var(--success-lighten);
      }
    }
  }
}

._body {
  margin: 0 0.5rem;
  background-color: white;
  border-radius: 0.25rem;
}
</style>
