<template>
  <span class="invoice-number">
    {{ call.vc20InvoiceNum || '' }}
  </span>
</template>

<script>
export default {
  name: 'invoice-number',

  props: {
    callKey: { required: true }
  },

  data () {
    return {
      call: {}
    };
  },

  methods: {
    getCall () {
      return new Promise(resolve => {
        this.$store.dispatch('CALL__read', {
          callKey: this.callKey,
          callback: response => { resolve(response); }
        });
      });
    }
  },

  async mounted () {
    this.$data.call = await this.getCall();
  }
};
</script>
