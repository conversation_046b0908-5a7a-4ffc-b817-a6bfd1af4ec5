<template>
  <ul class="payments" ref="list">
    <li class="payment" data-controls>
      <div class="_add" type="success">
        <app-button type="success">
          <i class="fas fa-plus"></i> Add
        </app-button>
        <select v-model="addMethod" @change="onAddMethodChange">
          <option value="">Select a method</option>
          <option value="manual">Manual&hellip;</option>
          <option value="towpay" :disabled="!$store.state.payment.isTowPayAvailable">TowPay&hellip;</option>
        </select>
      </div>
      <div class="_filter"
        :data-active="!!filters.length"
        @click="editFilters('filters')">
        <span v-if="filters.length">{{ filters.length }}</span>
        <i class="far fa-bars-filter"></i>
      </div>
    </li>
    <li class="payment"
      v-for="payment in data" :key="payment.lPaymentKey"
      :data-selected="Number(payment.lPaymentKey) === Number(activePaymentKey)"
      :data-deleted="!payment.bActive"
      :data-positive-balance="Number(payment.realTimeBalance) > 0"
      @click="$emit('select', payment)">
      <div class="_balance">
        {{ payment.realTimeBalance | usd }}
        <span class="_amount" v-show="payment.realTimeBalance != payment.tcAmount">of {{ payment.tcAmount | usd }}</span>
      </div>
      <div class="_type is-small">{{ payment.sPaymentType }}&thinsp;&middot;&thinsp;{{ toSimpleDate(payment.dReceived) }}</div>
      <div class="_actions">
        <app-button @click="$emit('edit', payment)" title="View and edit payment.">
          <i class="fas fa-pencil"></i>
        </app-button>
        <app-button @click="$emit('overpayment', payment)" title="Create overpayment invoice.">
          <i class="fas fa-overline"></i>
        </app-button>
      </div>
    </li>
    <li class="payment" data-gradient></li>
  </ul>
</template>

<script>
import Grid from './Grid.vue';
import format from 'date-fns/format';

export default {
  name: 'payments-grid',

  extends: Grid,

  props: {
    activePaymentKey: { default: null }
  },

  data () {
    return {
      addMethod: ''
    };
  },

  methods: {
    toSimpleDate (value) {
      if (!value) return value;

      return format(value, 'M/D/YY');
    },

    onAddMethodChange () {
      this.$emit('addWithMethod', this.$data.addMethod);

      this.$data.addMethod = '';
    }
  }
};
</script>
