<template>
<div class="data-grid" :id="gridId">
  <app-titlebar :title="title">
    <template slot="center">
      <div class="is-hidden-mobile">
        <app-button type="text" :disabled="true" style="opacity: 1">
          {{ data.length }} Records
        </app-button>
        <app-button
          type="text"
          @click="autoSizeColumns"
          :disabled="!grid.Key || data.length === 0 || isAutoSizing"
          title="Auto-size columns to fit content">
          {{ isAutoSizing ? 'Sizing...' : 'Fit' }}
        </app-button>
        <app-button type="text" @click="refresh" :title="'Refreshed at ' + refreshedAt | verbalTime">
          Refresh
        </app-button>
        <app-button
          class="-filters"
          :class="{ '-active': filters.length }"
          type="text"
          v-if="filtersButtonVisible"
          @click="editFilters('filters')"
          :disabled="!grid.Key">
          Filters <span v-if="filters.length">&nbsp;(&thinsp;{{ filters.length }}&thinsp;)</span>
        </app-button>
        <app-button
          type="text"
          v-if="columnsButtonVisible"
          @click="editSettings"
          :disabled="!grid.Key">
          Columns
        </app-button>
        <app-dropdown v-if="canExport">
          <app-button type="text" :disabled="!grid.Key">
            Export
          </app-button>
          <template slot="menu">
            <a href="#" class="dropdown-item" @click.prevent="handleExport('CSVFile')">CSV</a>
            <a href="#" class="dropdown-item" @click.prevent="handleExport('PDF')">PDF</a>
          </template>
        </app-dropdown>
      </div>
    </template>
    <slot name="context-tools"></slot>
  </app-titlebar>

  <transition name="fade" mode="out-in">
    <section class="viewport" v-if="!showLoader">
      <div class="_liner">
        <div class="-column -action">
          <div class="-row -header">
            <p class="control">
              <app-button class="_queue-trigger" type="default" size="small" v-if="canBatchQueue" @click="toggleReconcile('*')">
                <i class="_icon fas fa-check"></i> <div class="_text">Reconcile</div>
              </app-button>
            </p>
            <p class="control">
              <app-button class="_queue-trigger" type="default" size="small" v-if="canBatchQueue" @click="toggleConfirm('*')">
                <i class="_icon fas fa-check"></i> <div class="_text">Confirm</div>
              </app-button>
            </p>
          </div>
          <!-- <div :class="rowClasses(record)" v-for="record in book.page.records" :key="record[config.recordKeyName]"> -->
          <div :class="rowClasses(record)" v-for="(record, recordIndex) in data" :key="recordIndex">
            <app-group>
              <p class="control">
                <app-button class="_queue-trigger" type="default" size="small" :data-indicator-visible="reconcileIndicatorVisible(record)" @click="toggleReconcile(record)" :disabled="!record.bReconcilable">
                  <i :class="reconcileButtonIndicators(record)"></i> <div class="_text">Reconcile</div>
                </app-button>
              </p>
              <p class="control">
                <app-button class="_queue-trigger" type="default" size="small" :data-indicator-visible="confirmIndicatorVisible(record)" @click="toggleConfirm(record)" :disabled="!record.bConfirmable">
                  <i :class="confirmButtonIndicators(record)"></i> <div class="_text">Confirm</div>
                </app-button>
              </p>
            </app-group>
          </div> <!-- /-row -->
        </div> <!-- /-column -->

        <div class="-column" v-for="(column, columnIndex) in columns" :style="'--width: ' + column.Width + 'px'" :key="columnIndex">
          <div class="-row -header">
            <span class="-sort" v-if="sortIcon(column)">
              <span class="-priority" v-show="sortableColumns.length > 1">{{ humanizeSortPriority(column) }}</span>
              <i class="-orientation fas" :class="sortIcon(column)"></i>
            </span>
            <span class="-name" @click="sortBy(column)" :title="column.Name">{{ column.Name }}</span>
            <div class="-resizer" :data-id="column.ID" :data-grid-id="gridId"></div>
          </div>
          <!-- <div :class="rowClasses(record)" v-for="(record, recordIndex) in book.page.records" @click.capture="openRecord(record)" :title="cellValue(record, column)" :key="recordIndex"> -->
          <div :class="rowClasses(record)" v-for="(record, recordIndex) in data" @click.capture="openRecord(record)" :title="cellValue(record, column)" :key="recordIndex">
            <span :class="cellClasses(record, column)">{{ cellValue(record, column) }}</span>
          </div> <!-- /-row -->
        </div> <!-- /-column -->

        <div class="-column -flexer">
          <div class="-row -header"></div>
          <!-- <div class="-row" v-for="(record, recordIndex) in book.page.records" :key="recordIndex"></div> -->
          <div class="-row" v-for="(record, recordIndex) in data" :key="recordIndex"></div>
        </div>
      </div>

      <div class="_floating-tools">
        <div class="pill">
          <slot name="floating-tools"></slot>
          <!-- <template v-if="pageResults && book.length">
            <div class="divider"></div>
            <app-button class="button is-white" @click="pagePrevious">
              <i class="far fa-arrow-left"></i>
            </app-button>
            <app-button class="button is-white" @click="pageNext">
              <i class="far fa-arrow-right"></i>
            </app-button>
          </template> -->
        </div>
      </div>
    </section> <!-- /viewport -->
    <div class="loadport" v-else key="loader">
      <app-loader class="_indicator"></app-loader>
    </div>
  </transition>
</div>
</template>

<script>
import Grid from './Grid.vue';
import Access from '@/utils/access.js';

export default {
  name: 'checkout-grid',

  extends: Grid,

  props: {
    queue: { type: Array, default: [] }
  },

  computed: {
    canBatchQueue () {
      return Access.has('checkout.batchQueue');
    }
  },

  methods: {
    recordThumbnail (record, task) {
      let callKeyAlias = this.config.readRouteKey || this.config.recordKeyName;

      if (record === '*') {
        let records = [];

        this.data.forEach(record => {
          if ((task === 'reconcile' && record.bReconcilable) || (task === 'confirm' && record.bConfirmable)) {
            records.push({
              key: Number(this.$_.get(record, callKeyAlias, null)),
              paymentKey: Number(this.$_.get(record, this.config.recordKeyName, null)),
              retow: Number(this.$_.get(record, 'bRetow', null)),
              task: task
            });
          }
        });

        return records;
      }

      return {
        key: Number(this.$_.get(record, callKeyAlias, null)),
        paymentKey: Number(this.$_.get(record, this.config.recordKeyName, null)),
        retow: Number(this.$_.get(record, 'bRetow', null)),
        task: task
      };
    },

    toggleReconcile (record) {
      this.$emit('toggle-reconcile', this.recordThumbnail(record, 'reconcile'));
    },

    toggleConfirm (record) {
      this.$emit('toggle-confirm', this.recordThumbnail(record, 'confirm'));
    },

    isPendingReconcile (record) {
      return !!this.$_.find(this.queue, { paymentKey: Number(record[this.config.recordKeyName]), task: 'reconcile' });
    },

    isPendingConfirm (record) {
      return !!this.$_.find(this.queue, { paymentKey: Number(record[this.config.recordKeyName]), task: 'confirm' });
    },

    reconcileIndicatorVisible (record) {
      return this.isPendingReconcile(record);
    },

    confirmIndicatorVisible (record) {
      return this.isPendingConfirm(record);
    },

    reconcileButtonIndicators (record) {
      return {
        '_icon': true,
        'fas': true,
        'fa-check': true
      };
    },

    confirmButtonIndicators (record) {
      return {
        '_icon': true,
        'fas': true,
        'fa-check': true
      };
    }
  }
};
</script>
