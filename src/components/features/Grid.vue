<template>
<div class="data-grid" :id="gridId">
  <app-titlebar :title="title">
    <template slot="center">
      <div class="is-hidden-mobile">
        <app-button type="text" :disabled="true" style="opacity: 1">
          {{ data.length }} Records
        </app-button>
        <app-button
          type="text"
          @click="autoSizeColumns"
          :disabled="!grid.Key || data.length === 0 || isAutoSizing"
          title="Auto-size columns to fit content">
          {{ isAutoSizing ? 'Sizing...' : 'Fit' }}
        </app-button>
        <app-button type="text" @click="refresh" :title="'Refreshed at ' + refreshedAt | verbalTime">
          Refresh
        </app-button>
        <app-button
          class="-filters"
          :class="{ '-active': filters.length }"
          type="text"
          v-if="filtersButtonVisible"
          @click="editFilters('filters')">
          Filters <span v-if="filters.length">&nbsp;(&thinsp;{{ filters.length }}&thinsp;)</span>
        </app-button>
        <app-button
          @click="editFilters('form')"
          type="text"
          v-if="findButtonVisible">
          Find
        </app-button>
        <!-- <filters-glance
          :filters="filters"
          @editFilters="editFilters">
        </filters-glance> -->
        <app-button
          type="text"
          v-if="columnsButtonVisible"
          @click="editSettings"
          :disabled="!grid.Key">
          Columns
        </app-button>
        <app-dropdown v-if="canExport">
          <app-button type="text" :disabled="!grid.Key">
            Export
          </app-button>
          <template slot="menu">
            <a href="#" class="dropdown-item" @click.prevent="handleExport('CSVFile')">CSV</a>
            <a href="#" class="dropdown-item" @click.prevent="handleExport('PDF')">PDF</a>
          </template>
        </app-dropdown>
      </div>
    </template>
    <slot name="context-tools"></slot>
  </app-titlebar>

  <transition name="fade" mode="out-in">
    <section class="viewport" v-if="!showLoader" @scroll="handleScroll" ref="viewport">
      <div class="_liner" :style="{height: totalHeight + 'px'}">
        <div class="-column -action">
          <div class="-row -header">
            <p class="control" @click.capture="toggleSelection" v-if="multiselect">
              <a class="-button button is-small"><i class="far fa-check-square"></i></a>
            </p>
          </div>

          <div :style="{paddingTop: paddingTop + 'px', paddingBottom: paddingBottom + 'px'}">
            <div :class="rowClasses(record, { allow_overflow: true })"
              v-for="(record, index) in visibleData"
              :key="getRecordKey(record, index, 'action')"
              :style="{height: rowHeight + 'px'}">

              <app-group>
                <p class="control" @click.capture="manageSelection($event, record)" v-if="multiselect">
                  <a class="-button button is-small"><i :class="selectorClasses(record)"></i></a>
                </p>

                <p class="control" v-if="quickAssign" @click.stop="assign(record)">
                  <a class="-button button is-small"><i class="far fa-map"></i></a>
                </p>

                <p class="control" v-if="actionsVisible">
                  <a @click.stop="$emit('getActions', record)" class="-button button is-small"><i class="fas fa-ellipsis-h"></i></a>
                </p>

                <a class="-button -preview button is-small"
                  v-if="invoicePreviewControl"
                  @click.capture="viewInvoice(record)"
                  title="Preview invoice request">
                  Preview
                </a>

                <a class="-button -preview button is-small"
                  v-if="invoiceReviewControl"
                  @click.capture="viewInvoice(record)"
                  title="Show invoice">
                  Show
                </a>

                <a class="-button -requested button is-small" v-if="isRequested(record)" title="Invoice is requested">
                  <i class="-icon fas fa-share"></i>
                </a>

                <a class="-button -error button is-small" v-if="hasErrors(record)">
                  <i class="-icon fas fa-exclamation-circle"></i>
                  <ul class="-detail">
                    <li v-for="(error, errorIndex) in record.Errors" :key="`error-${getRecordKey(record, errorIndex, 'error')}-${errorIndex}`">{{ error.Message }}</li>
                  </ul>
                </a>
              </app-group>
            </div> <!-- /-row -->
          </div>
        </div> <!-- /-column -->

        <div class="-column"
          v-for="(column, colIndex) in columns"
          :style="getColumnStyle(column)"
          :key="column.ID || colIndex">

          <div class="-row -header">
            <span class="-sort" v-if="sortIcon(column)">
              <span class="-priority" v-show="sortableColumns.length > 1">{{ humanizeSortPriority(column) }}</span>
              <i class="-orientation fas" :class="sortIcon(column)"></i>
            </span>
            <span class="-name" @click="sortBy(column)" :title="column.Name">{{ column.Name }}</span>
            <div class="-resizer" :data-id="column.ID" :data-grid-id="gridId"></div>
          </div>

          <div :style="{paddingTop: paddingTop + 'px', paddingBottom: paddingBottom + 'px'}">
            <div :class="rowClasses(record)"
              v-for="(record, index) in visibleData"
              :title="cellValue(record, column)"
              :key="getRecordKey(record, index, `col-${column.ID}`)"
              :style="{height: rowHeight + 'px'}"
              @click.capture="openRecord(record)">
              <span :class="cellClasses(record, column)">{{ cellValue(record, column) }}</span>
            </div>
          </div>
        </div>

        <div class="-column -flexer">
          <div class="-row -header"></div>
          <div :style="{paddingTop: paddingTop + 'px', paddingBottom: paddingBottom + 'px'}">
            <div class="-row"
              v-for="(record, index) in visibleData"
              :key="getRecordKey(record, index, 'flexer')"
              :style="{height: rowHeight + 'px'}">
            </div>
          </div>
        </div>
      </div>

      <div class="_floating-tools">
        <div class="pill">
          <slot name="floating-tools"></slot>
        </div>
      </div>
    </section> <!-- /viewport -->
    <div class="loadport" v-else key="loader">
      <app-loader class="_indicator"></app-loader>
    </div>
  </transition>
</div>
</template>

<script>
import is from 'is_js';
import Access from '@/utils/access.js';
import { affirmative } from '@/utils/filters.js';
import { mapGetters, mapActions } from 'vuex';
import FiltersGlance from '../features/FiltersGlance.vue';

// Import only the specific lodash functions we need to minimize bundle size
import get from 'lodash/get';
import has from 'lodash/has';
import max from 'lodash/max';
import find from 'lodash/find';
import isNil from 'lodash/isNil';
import range from 'lodash/range';
import filter from 'lodash/filter';
import orderBy from 'lodash/orderBy';
import isEmpty from 'lodash/isEmpty';
import forEach from 'lodash/forEach';
import uniqueId from 'lodash/uniqueId';
import toNumber from 'lodash/toNumber';
import debounce from 'lodash/debounce';
import toString from 'lodash/toString';
import findIndex from 'lodash/findIndex';

let rafId = null;

export default {
  name: 'data-grid',

  props: {
    data: { type: Array },
    grid: { type: Object },
    title: { type: String, required: false },
    config: { type: Object },
    access: { type: Object, required: false },

    showLoader: { type: Boolean, default: false },
    refreshedAt: { type: String, required: false },
    findButtonVisible: { type: Boolean, default: true },
    quickAssign: { type: Boolean, required: false, default: false },
    actions: { type: [String, Boolean], required: false, default: false },
    filtersButtonVisible: { type: Boolean, required: false, default: true },
    columnsButtonVisible: { type: Boolean, required: false, default: true },
    multiselect: { type: [String, Boolean], required: false, default: false },
    invoicePreviewControl: { type: Boolean, default: false },
    invoiceReviewControl: { type: Boolean, default: false }
  },

  components: {
    FiltersGlance
  },

  data () {
    return {
      gridId: uniqueId('grid-'),
      clickedRow: {},

      // Virtual scrolling data
      visibleStartIndex: 0,
      visibleEndIndex: 0,
      rowHeight: 33.33, // Approximate row height in pixels
      viewportHeight: 0,
      scrollPosition: 0,

      // Cache for column styles to ensure consistency
      columnStyleCache: new Map(),

      // Flag to indicate when auto-sizing is in progress
      isAutoSizing: false,

      workingColumn: {
        record: {},
        endX: null,
        startX: null,
        minWidth: 75,
        startWidth: 0,
        isResizing: false
      }
    };
  },

  computed: {
    ...mapGetters([
      'USER__state',
      '__selectedRecords',
      'TOPSCOMPANY__settings'
    ]),

    // Ensure we have a record key name to use for keys
    recordKeyName() {
      return this.config && this.config.recordKeyName ? this.config.recordKeyName : 'id';
    },

    // Get the key to use for v-for loops, allowing child components to override
    keyForRecords() {
      // Use uniqueRecordKey from child component if available
      return this.uniqueRecordKey || this.recordKeyName;
    },

    // Virtual scrolling computed properties
    visibleData() {
      if (!this.data || this.data.length === 0) return [];

      // If we have a small dataset, just return all data
      if (this.data.length <= 100) {
        return this.data;
      }

      // Calculate visible range based on scroll position
      const buffer = 50; // Increased buffer size to ensure more rows are rendered
      const start = Math.max(0, this.visibleStartIndex - buffer);
      const end = Math.min(this.data.length, this.visibleEndIndex + buffer);

      return this.data.slice(start, end);
    },

    totalHeight() {
      return this.data.length * this.rowHeight;
    },

    paddingTop() {
      if (this.data.length <= 100) return 0;
      return Math.max(0, this.visibleStartIndex - 50) * this.rowHeight;
    },

    paddingBottom() {
      if (this.data.length <= 100) return 0;
      const visibleEnd = Math.min(this.data.length, this.visibleEndIndex + 50);
      const renderedEnd = Math.min(this.data.length, this.visibleEndIndex + 50);
      const unrenderedCount = Math.max(0, this.data.length - renderedEnd);
      return unrenderedCount * this.rowHeight;
    },

    canExport () {
      return get(this.access, 'export', true);
    },

    restrictedColumns () {
      // Note: Restricted by default
      const columns = [
        'fCommission1',
        'fCommission2',
        'vc255AccountingNotes'
      ];

      const filtered = [];

      if (!Access.has('drivers.commission')) {
        filtered.push('fCommission1', 'fCommission2');
      }

      if (!Access.has('calls.accountingNotes')) {
        filtered.push('vc255AccountingNotes');
      }

      return filtered;
    },

    computedWidth () {
      // Ensure we have valid numbers for all values
      const startWidth = parseInt(this.workingColumn.startWidth) || 100;
      const startX = parseInt(this.workingColumn.startX) || 0;
      const endX = parseInt(this.workingColumn.endX) || 0;
      const delta = endX && startX ? (endX - startX) : 0;
      const value = startWidth + delta;
      const minWidth = this.workingColumn.minWidth || 75;

      return Math.max(value, minWidth);
    },

    columns: {
      get () {
        if (!has(this.grid, 'Columns')) return [];

        return filter(this.grid.Columns, column => {
          return !is.inArray(column.ID, this.restrictedColumns);
        });
      },
      set (columns) {
        if (has(this.grid, 'Columns')) {
          this.grid.Columns = columns;
        }
      }
    },

    sortableColumns () {
      return orderBy(
        filter(this.columns, column => toString(column.Ordered) === 'true'),
        'OrderPriority',
        'asc'
      );
    },

    filters () {
      return get(this.grid, 'Filters', []);
    },

    actionsVisible () {
      return toString(this.actions) === 'true';
    }
  },

  watch: {
    // Clear the column style cache when the grid changes
    grid: {
      handler() {
        if (this.columnStyleCache) {
          this.columnStyleCache.clear();
        }
      },
      deep: true
    }
  },

  methods: {
    ...mapActions(['__selectRecords']),

    handleExport (format) {
      this.exportData(format);
    },

    // Generate a key for a record, using the child component's getUniqueRecordKey method if available
    getRecordKey(record, index, prefix) {
      // If record doesn't have the key property, use index
      if (!record || !record[this.recordKeyName]) {
        return `${prefix}-index-${index}`;
      }

      // If child component has a getUniqueRecordKey method, use it
      if (typeof this.getUniqueRecordKey === 'function') {
        return `${prefix}-${this.keyForRecords}-${this.getUniqueRecordKey(record)}`;
      }

      // Otherwise, use the default key generation
      return `${prefix}-${this.keyForRecords}-${record[this.recordKeyName]}`;
    },

    // Get consistent column style with fixed width
    getColumnStyle(column) {
      // If column is being resized, don't use cache
      if (this.workingColumn.isResizing && this.workingColumn.record && this.workingColumn.record.ID === column.ID) {
        const width = column.Width || 100;
        return {
          width: `${width}px`,
          minWidth: `${width}px`,
          maxWidth: `${width}px`,
          '--width': `${width}px`
        };
      }

      // Use cached style if available to ensure consistency during scrolling
      const columnId = column.ID || '';
      if (!this.columnStyleCache.has(columnId) || this.columnStyleCache.get(columnId).width !== column.Width) {
        const width = column.Width || 100;
        const style = {
          width: `${width}px`,
          minWidth: `${width}px`,
          maxWidth: `${width}px`,
          '--width': `${width}px`
        };

        // Cache the style object
        this.columnStyleCache.set(columnId, {
          style,
          width: column.Width
        });
      }

      // Return the cached style
      return this.columnStyleCache.get(columnId).style;
    },

    toggleSelection () {
      // If nothing is selected, select all visible records
      // Otherwise, clear the selection
      const newSelection = this.__selectedRecords.length === 0 ? [...this.data] : [];
      this.__selectRecords(newSelection);
    },

    manageSelection (event, record) {
      // Create a new array instead of mutating the original
      const selectedRecords = [...this.__selectedRecords];

      if (event.shiftKey) {
        // Selection range of rows
        if (isEmpty(this.clickedRow)) return;

        const start = findIndex(this.data, this.clickedRow);
        const end = findIndex(this.data, record);

        if (start === -1 || end === -1) return;

        const rangeStart = Math.min(start, end) + 1;
        const rangeEnd = Math.max(start, end);

        // Get records in the range that aren't already selected
        const recordsToAdd = [];
        for (let i = rangeStart; i <= rangeEnd; i++) {
          const dataRecord = this.data[i];
          if (dataRecord && !this.isSelected(dataRecord)) {
            recordsToAdd.push(dataRecord);
          }
        }

        // Add all new records at once
        if (recordsToAdd.length > 0) {
          selectedRecords.push(...recordsToAdd);
        }
      } else {
        // Select single row
        if (this.isSelected(record)) {
          const keyName = this.recordKeyName;
          const recordKey = record[keyName];

          // Find the index using the key for more reliable matching
          const pruneIndex = selectedRecords.findIndex(r => r[keyName] === recordKey);
          if (pruneIndex !== -1) {
            selectedRecords.splice(pruneIndex, 1);
          }
        } else {
          selectedRecords.push(record);
        }
      }

      this.clickedRow = record;
      this.__selectRecords(selectedRecords);
    },

    openRecord (record) {
      this.$emit('openRecord', record);
    },

    assign ({ lCallKey }) {
      this.$router.push({ name: 'AssignCall', params: { key: lCallKey } });
    },

    isSelected (record) {
      const keyName = this.recordKeyName;
      const target = find(this.__selectedRecords, [keyName, record[keyName]]);

      if (isNil(target)) return false;

      // Clarify multiple dispatches on a single call
      const dispatchKeyName = has(target, 'CAL_lDispatchKey') ? 'CAL_lDispatchKey' : 'lDispatchKey';

      return is.all.truthy([
        target[keyName] === record[keyName],
        get(target, dispatchKeyName, '') === get(record, dispatchKeyName, '')
      ]);
    },

    refresh () {
      this.$emit('refresh');
    },

    editFilters (mode = 'form') {
      this.$router.push({
        name: 'GridSearch',
        params: { key: this.grid.Key },
        query: {
          noun: this.config.noun,
          mode: mode
        }
      });
    },

    editSettings () {
      this.$router.push({
        name: 'GridColumns',
        params: { key: this.grid.Key },
        query: { noun: this.config.noun }
      });
    },

    exportData (format) {
      this.$emit('exportData', { format: format, gridKey: this.grid.Key });
    },

    formatData (record, column) {
      // if (column.ID === 'dLastModified') {
      //   return relativeDate(record[column.ID]);
      // }

      if (is.startWith(column.ID, 'b')) {
        return affirmative(record[column.ID]);
      }

      return record[column.ID];
    },

    sortBy (column) {
      if (column.ID === 'lAppointmentETAMinutes') return;

      let sortBoolean = this.getSortBoolean(column);
      let ascendingBoolean = this.getAscendingBoolean(column);
      let sortPriority = this.getSortPriority(column);

      column.Ordered = toString(sortBoolean);
      column.Ascending = toString(ascendingBoolean);
      column.OrderPriority = toString(sortPriority);

      this.optimizeSortPriority();

      this.$emit('save', this.grid);
    },

    getSortBoolean (column) {
      return this.getSortOrientation(column) !== 'desc';
    },

    getAscendingBoolean (column) {
      return this.getSortOrientation(column) === '';
    },

    getSortPriority (column) {
      if (this.getSortOrientation(column) === 'asc') return column.OrderPriority;

      if (this.getSortOrientation(column) === 'desc') return 0;

      let highestPriority = max(this.sortableColumns, 'OrderPriority') || { OrderPriority: 0 };
      let priority = toNumber(highestPriority.OrderPriority);

      return priority + 1;
    },

    getSortOrientation ({ Ordered, Ascending }) {
      if (toString(Ordered) === 'false') return '';

      return toString(Ascending) === 'false' ? 'desc' : 'asc';
    },

    humanizeSortPriority ({ Ordered, OrderPriority }) {
      if (Ordered !== 'true') return '';

      return toString(OrderPriority) !== '0' ? OrderPriority : '';
    },

    sortIcon (column) {
      let icons = {
        '': '',
        asc: 'fa-sort-down',
        desc: 'fa-sort-up'
      };

      return icons[this.getSortOrientation(column)];
    },

    optimizeSortPriority () {
      forEach(this.sortableColumns, (column, index) => {
        column.OrderPriority = index + 1;
      });
    },

    beginResize (event) {
      if (event.target.dataset.gridId !== this.gridId) return;
      if (event.target.className !== '-resizer') return;

      this.workingColumn.record = find(this.columns, ['ID', event.target.dataset.id]);

      if (!this.workingColumn.record) return;

      this.workingColumn.startWidth = this.workingColumn.record.Width || 100;
      this.workingColumn.isResizing = true;
      this.workingColumn.startX = event.x;
    },

    isResizing (event) {
      if (!this.workingColumn.isResizing || !this.workingColumn.record) return;

      event.preventDefault();
      this.workingColumn.endX = event.x;

      if (this.workingColumn.record) {
        this.workingColumn.record.Width = this.computedWidth;
      }
    },

    endResize(event) {
      if (!this.workingColumn.isResizing) return;

      const isGridElement = event.target.closest(`#${this.gridId}`);
      if (!isGridElement && event.target.className !== '-resizer') {
        // Still end the resize operation even if clicked elsewhere
      }

      this.workingColumn.isResizing = false;

      if (this.workingColumn.record && this.workingColumn.record.ID) {
        this.columnStyleCache.delete(this.workingColumn.record.ID);
        this.saveColumnSize();
      }
    },

    saveColumnSize: debounce(function () {
      this.$emit('save', this.grid);
    }, 1000),

    autoSizeColumns() {
      if (!this.data || this.data.length === 0 || !this.columns || this.columns.length === 0 || this.isAutoSizing) {
        return;
      }

      // Set loading state
      this.isAutoSizing = true;

      // Use setTimeout to allow the UI to update before starting the calculation
      setTimeout(() => {
        try {
          // Create a temporary canvas to measure text width
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');

          // Get the computed style of the grid cells to use the same font
          const gridElement = document.getElementById(this.gridId);
          if (!gridElement) {
            this.isAutoSizing = false;
            return;
          }

          const cellStyle = window.getComputedStyle(gridElement.querySelector('.-row') || document.body);
          context.font = `${cellStyle.fontWeight} ${cellStyle.fontSize} ${cellStyle.fontFamily}`;

          // For large datasets, sample a subset of the data to improve performance
          const sampleSize = Math.min(this.data.length, 1000);
          const sampleStep = Math.max(1, Math.floor(this.data.length / sampleSize));
          const sampledData = [];

          for (let i = 0; i < this.data.length; i += sampleStep) {
            sampledData.push(this.data[i]);
          }

          // Calculate optimal width for each column
          const updatedColumns = this.columns.map(column => {
            // Start with the column header width
            let maxWidth = context.measureText(column.Name || '').width + 40; // Add padding

            // Check sampled data for this column
            sampledData.forEach(record => {
              const cellContent = this.cellValue(record, column) || '';
              const cellWidth = context.measureText(String(cellContent)).width + 40; // Add padding
              maxWidth = Math.max(maxWidth, cellWidth);
            });

            // Respect minimum width
            const minWidth = this.workingColumn.minWidth;
            const optimalWidth = Math.max(Math.ceil(maxWidth), minWidth);

            // Create a new column object to avoid mutating the original
            return {
              ...column,
              Width: optimalWidth
            };
          });

          // Update the columns
          if (has(this.grid, 'Columns')) {
            // Find the columns in the grid that match our visible columns
            const gridColumns = this.grid.Columns.map(gridCol => {
              const matchingCol = updatedColumns.find(col => col.ID === gridCol.ID);
              if (matchingCol) {
                return {
                  ...gridCol,
                  Width: matchingCol.Width
                };
              }
              return gridCol;
            });

            // Update the grid columns
            this.grid.Columns = gridColumns;

            // Clear the column style cache
            this.columnStyleCache.clear();

            // Save the changes
            this.saveColumnSize();
          }
        } catch (error) {
          console.error('Error auto-sizing columns:', error);
        } finally {
          // Reset loading state
          this.isAutoSizing = false;
        }
      }, 50); // Small delay to allow UI update
    },

    cellValue (record, column) {
      if (column.ID.startsWith('b') && column.ID !== 'bOwnerWithVehicle') {
        return affirmative(record[column.ID]);
      }

      return record[column.ID];
    },

    selectorClasses (record) {
      return {
        'far': true,
        'fa-square': !this.isSelected(record),
        'fa-check-square': this.isSelected(record)
      };
    },

    rowClasses (record, options = {}) {
      return {
        '-row': true,
        '-highlighted': this.isSelected(record),
        '-allow-overflow': options.allow_overflow || false,
        '-ghost': is.inArray(toString(get(record, 'bActive', '1')), ['0', 'false']),
        '-hold': this.isOnHold(record)
      };
    },

    isOnHold (record) {
      let isOnHold1 = Number(get(record, 'HLD_bHoldOn', 0));
      let isOnHold2 = get(record, 'HLD_sHoldOn', 0);
      let isOnHold3 = Number(get(record, 'bHold', ''));

      return isOnHold1 === 1 || isOnHold2 === 'On' || isOnHold3 === 1;
    },

    cellClasses (record, column) {
      // ...
    },

    isRequested (record) {
      return get(record, 'isRequested', false);
    },

    hasErrors (record) {
      return has(record, 'Errors');
    },

    viewInvoice (record) {
      this.$emit('viewInvoice', record);
    },

    handleScroll() {
      if (rafId) return;
      rafId = requestAnimationFrame(() => {
        rafId = null;

        // Check if viewport ref exists before accessing its properties
        if (!this.$refs.viewport) return;

        const scrollTop = this.$refs.viewport.scrollTop;
        this.scrollPosition = scrollTop;
        const startIndex = Math.floor(scrollTop / this.rowHeight);

        // Calculate visible rows with a larger buffer
        const visibleRowCount = Math.ceil(this.viewportHeight / this.rowHeight);
        const bufferMultiplier = 2; // Show twice as many rows as visible
        const endIndex = Math.min(startIndex + (visibleRowCount * bufferMultiplier), this.data.length);

        this.visibleStartIndex = startIndex;
        this.visibleEndIndex = endIndex;
      });
    }
  },

  created () {
    // Use passive event listeners where possible for better performance
    document.addEventListener('mousedown', this.beginResize, { passive: true });
    document.addEventListener('mouseup', this.endResize, { passive: true });
    // mousemove can't be passive because we call preventDefault in the handler
    document.addEventListener('mousemove', this.isResizing);
  },

  mounted() {
    // Set a reasonable default for visible rows to ensure initial rendering
    this.visibleEndIndex = 100; // Show more rows initially

    // Use nextTick to ensure the DOM is updated
    this.$nextTick(() => {
      if (this.$refs.viewport) {
        this.viewportHeight = this.$refs.viewport.clientHeight;

        // Calculate visible rows based on actual viewport height
        const visibleRowCount = Math.ceil(this.viewportHeight / this.rowHeight);
        this.visibleEndIndex = Math.max(100, visibleRowCount * 2); // Double the visible rows to ensure coverage

        // Only call handleScroll if viewport exists
        this.handleScroll();
      }
    });

    // Add resize observer to update visible rows when viewport size changes
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          if (entry.target === this.$refs.viewport) {
            this.viewportHeight = entry.contentRect.height;

            // Recalculate visible rows when viewport size changes
            const visibleRowCount = Math.ceil(this.viewportHeight / this.rowHeight);
            this.visibleEndIndex = Math.max(this.visibleEndIndex, visibleRowCount * 2);

            // Only call handleScroll if viewport exists
            if (this.$refs.viewport) {
              this.handleScroll();
            }
          }
        }
      });

      // Only observe if viewport exists
      if (this.$refs.viewport) {
        resizeObserver.observe(this.$refs.viewport);
        this.resizeObserver = resizeObserver;
      }
    }
  },

  destroyed() {
    if (rafId) cancelAnimationFrame(rafId);

    // Clean up event listeners with the same options they were added with
    document.removeEventListener('mousedown', this.beginResize, { passive: true });
    document.removeEventListener('mouseup', this.endResize, { passive: true });
    document.removeEventListener('mousemove', this.isResizing);

    // Clean up resize observer
    if (this.resizeObserver && this.$refs.viewport) {
      this.resizeObserver.unobserve(this.$refs.viewport);
      this.resizeObserver.disconnect();
    }
  }
};
</script>
