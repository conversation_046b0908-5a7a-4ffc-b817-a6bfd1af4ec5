<template>
<div class="data-grid" :id="gridId">
  <app-titlebar :title="title">
    <template slot="center">
      <div class="is-hidden-mobile">
        <app-button type="text" :disabled="true" style="opacity: 1">
          {{ data.length }} Records
        </app-button>
        <app-button type="text" @click="refresh" :title="'Refreshed at ' + refreshedAt | verbalTime">
          Refresh
        </app-button>
        <app-button
          class="-filters"
          :class="{ '-active': filters.length }"
          type="text"
          v-if="filtersButtonVisible"
          @click="editFilters('filters')">
          Filters <span v-if="filters.length">&nbsp;(&thinsp;{{ filters.length }}&thinsp;)</span>
        </app-button>
        <app-button
          @click="editFilters('form')"
          type="text"
          v-if="findButtonVisible">
          Find
        </app-button>
        <!-- <filters-glance
          :filters="filters"
          @editFilters="editFilters">
        </filters-glance> -->
        <app-button
          type="text"
          v-if="columnsButtonVisible"
          @click="editSettings"
          :disabled="!grid.Key">
          Columns
        </app-button>
        <app-dropdown v-if="canExport">
          <app-button type="text" :disabled="!grid.Key">
            Export
          </app-button>
          <template slot="menu">
            <a href="#" class="dropdown-item" @click.prevent="handleExport('CSVFile')">CSV</a>
            <a href="#" class="dropdown-item" @click.prevent="handleExport('PDF')">PDF</a>
          </template>
        </app-dropdown>
      </div>
    </template>
    <slot name="context-tools"></slot>
  </app-titlebar>

  <transition name="fade" mode="out-in">
    <section class="viewport" v-if="!showLoader">
      <div class="_liner">
        <div class="-column -action">
          <div class="-row -header">
            <p class="control" @click.capture="toggleSelection" v-if="multiselect">
              <a class="-button button is-small"><i class="far fa-check-square"></i></a>
            </p>
          </div>
          <!-- 2021-6-30 -->
          <!-- Disabling paging for now. Unsure if it will come back or take a different form. -->
          <!-- <div :class="rowClasses(record)" v-for="record in book.page.records" :key="record[config.recordKeyName]"> -->
          <div :class="rowClasses(record, { allow_overflow: true })"
            v-for="(record, index) in data"
            :key="index">

            <app-group>
              <p class="control" @click.capture="manageSelection($event, record)" v-if="multiselect">
                <a class="-button button is-small"><i :class="selectorClasses(record)"></i></a>
              </p>

              <p class="control" v-if="quickAssign" @click.stop="assign(record)">
                <a class="-button button is-small"><i class="far fa-map"></i></a>
              </p>

              <p class="control" v-if="actionsVisible">
                <a @click.stop="$emit('getActions', record)" class="-button button is-small"><i class="fas fa-ellipsis-h"></i></a>
              </p>

              <a class="-button -preview button is-small"
                v-if="invoicePreviewControl"
                @click.capture="viewInvoice(record)"
                title="Preview invoice request">
                Preview
              </a>

              <a class="-button -preview button is-small"
                v-if="invoiceReviewControl"
                @click.capture="viewInvoice(record)"
                title="Show invoice">
                Show
              </a>

              <a class="-button -requested button is-small" v-if="isRequested(record)" title="Invoice is requested">
                <i class="-icon fas fa-share"></i>
              </a>

              <a class="-button -error button is-small" v-if="hasErrors(record)">
                <i class="-icon fas fa-exclamation-circle"></i>
                <ul class="-detail">
                  <li v-for="(error, index) in record.Errors" :key="index">{{ error.Message }}</li>
                </ul>
              </a>
            </app-group>
          </div> <!-- /-row -->
        </div> <!-- /-column -->

        <div class="-column"
          v-for="(column, index) in columns"
          :style="`--width: ${column.Width}px`"
          :key="index">

          <div class="-row -header">
            <span class="-sort" v-if="sortIcon(column)">
              <span class="-priority" v-show="sortableColumns.length > 1">{{ humanizeSortPriority(column) }}</span>
              <i class="-orientation fas" :class="sortIcon(column)"></i>
            </span>
            <span class="-name" @click="sortBy(column)" :title="column.Name">{{ column.Name }}</span>
            <div class="-resizer" :data-id="column.ID" :data-grid-id="gridId"></div>
          </div>

          <!-- <div :class="rowClasses(record)" v-for="record in book.page.records" @click.capture="openRecord(record)" :title="cellValue(record, column)" :key="record[config.recordKeyName]"> -->
          <div :class="rowClasses(record)"
            v-for="(record, index) in data"
            :title="cellValue(record, column)"
            :key="index"
            @click.capture="openRecord(record)">
            <span :class="cellClasses(record, column)">{{ cellValue(record, column) }}</span>
          </div>
        </div>

        <div class="-column -flexer">
          <div class="-row -header"></div>
          <!-- <div class="-row" v-for="(record, index) in book.page.records" :key="index"></div> -->
          <div class="-row" v-for="(record, index) in data" :key="index"></div>
        </div>
      </div>

      <div class="_floating-tools">
        <div class="pill">
          <slot name="floating-tools"></slot>
          <!-- <template v-if="pageResults && book.length">
            <div class="divider"></div>
            <app-button class="button is-white" @click="pagePrevious">
              <i class="far fa-arrow-left"></i>
            </app-button>
            <app-button class="button is-white" @click="pageNext">
              <i class="far fa-arrow-right"></i>
            </app-button>
          </template> -->
        </div>
      </div>
    </section> <!-- /viewport -->
    <div class="loadport" v-else key="loader">
      <app-loader class="_indicator"></app-loader>
    </div>
  </transition>
</div>
</template>

<script>
import is from 'is_js';
import Access from '@/access.js';
import { affirmative } from '@/filters.js';
import { mapGetters, mapActions } from 'vuex';
import FiltersGlance from '../features/FiltersGlance.vue';

import {
  get,
  has,
  max,
  find,
  isNil,
  range,
  filter,
  orderBy,
  isEmpty,
  forEach,
  uniqueId,
  toNumber,
  debounce,
  toString,
  findIndex
} from 'lodash';

export default {
  name: 'data-grid',

  props: {
    data: { type: Array },
    grid: { type: Object },
    title: { type: String, required: false },
    config: { type: Object },
    access: { type: Object, required: false },
    pageResults: { type: Boolean, default: true },
    showLoader: { type: Boolean, default: false },
    refreshedAt: { type: String, required: false },
    findButtonVisible: { type: Boolean, default: true },
    quickAssign: { type: Boolean, required: false, default: false },
    actions: { type: [String, Boolean], required: false, default: false },
    filtersButtonVisible: { type: Boolean, required: false, default: true },
    columnsButtonVisible: { type: Boolean, required: false, default: true },
    multiselect: { type: [String, Boolean], required: false, default: false },
    invoicePreviewControl: { type: Boolean, default: false },
    invoiceReviewControl: { type: Boolean, default: false }
  },

  components: {
    FiltersGlance
  },

  data () {
    return {
      gridId: uniqueId('grid-'),
      clickedRow: {},

      book: {
        length: 0,
        page: {
          records: [],
          number: 0,
          size: 50
        }
      },

      workingColumn: {
        record: {},
        endX: null,
        startX: null,
        minWidth: 75,
        startWidth: 0,
        isResizing: false
      }
    };
  },

  computed: {
    ...mapGetters([
      'USER__state',
      '__selectedRecords',
      'TOPSCOMPANY__settings'
    ]),

    canExport () {
      return get(this.access, 'export', true);
    },

    restrictedColumns () {
      // Note: Restricted by default
      let columns = [
        'fCommission1',
        'fCommission2',
        'vc255AccountingNotes'
      ];

      if (Access.has('drivers.commission')) {
        columns = filter(columns, ['fCommission1', 'fCommission2']);
      }

      if (Access.has('calls.accountingNotes')) {
        columns = filter(columns, 'vc255AccountingNotes');
      }

      return columns;
    },

    computedWidth () {
      let value = parseInt(this.$data.workingColumn.startWidth) + parseInt(this.$data.workingColumn.endX - this.$data.workingColumn.startX);

      if (value < this.$data.workingColumn.minWidth) {
        return this.$data.workingColumn.minWidth;
      }

      return value;
    },

    columns: {
      get () {
        if (!has(this.grid, 'Columns')) return [];

        return filter(this.grid.Columns, column => {
          return !is.inArray(column.ID, this.restrictedColumns);
        });
      },
      set (columns) {
        if (has(this.grid, 'Columns')) {
          this.grid.Columns = columns;
        }
      }
    },

    sortableColumns () {
      let columns = [];

      columns = filter(this.columns, column => toString(column.Ordered) === 'true');
      columns = orderBy(columns, 'OrderPriority', 'asc');

      return columns;
    },

    filters () {
      return get(this.grid, 'Filters', []);
    },

    actionsVisible () {
      return toString(this.actions) === 'true';
    }
  },

  watch: {
    'data' () {
      if (this.data.length > 0) {
        this.setBookLength();
        this.setPageData();
      }
    }
  },

  methods: {
    ...mapActions(['__selectRecords']),

    pagePrevious () {
      if (this.$data.book.page.number > 0) {
        --this.$data.book.page.number;
      }

      this.setPageData();
    },

    pageNext () {
      if (this.$data.book.page.number >= this.$data.book.length) return;

      ++this.$data.book.page.number;

      this.setPageData();
    },

    setPageData () {
      if (!this.pageResults) {
        this.$data.book.page.records = this.data;
        return;
      }

      let start = this.$data.book.page.number * this.$data.book.page.size;
      let end = start + this.$data.book.page.size;

      this.$data.book.page.records = this.data.slice(start, end);
    },

    setBookLength () {
      let fullPages = Math.floor(this.data.length / this.$data.book.page.size) - 1;
      let overflowPages = this.data.length % this.$data.book.page.size > 0 ? 1 : 0;

      this.$data.book.length = fullPages + overflowPages;
    },

    handleExport (format) {
      this.exportData(format);
    },

    toggleSelection () {
      let selectedRecords = JSON.parse(JSON.stringify(this.__selectedRecords));

      if (selectedRecords.length === 0) {
        forEach(this.data, record => {
          selectedRecords.push(record);
        });
      } else {
        selectedRecords = [];
      }

      this.__selectRecords(selectedRecords);
    },

    manageSelection (event, record) {
      // Clone pure object while dropping the hooks that Vuex has in place.
      // This allows me to mutate the object without violating the
      // direct state mutation restriction. Not generally advised.
      let selectedRecords = JSON.parse(JSON.stringify(this.__selectedRecords));

      if (event.shiftKey) {
        // Selection range of rows
        if (isEmpty(this.$data.clickedRow)) return;

        let start = findIndex(this.data, this.$data.clickedRow);
        let end = findIndex(this.data, record);

        start += (start < end) ? 1 : -1;
        end += (start < end) ? 1 : -1;

        forEach(range(start, end), index => {
          selectedRecords.push(this.data[index]);
        });
      } else {
        // Select single row
        if (this.isSelected(record)) {
          let pruneRecord = findIndex(selectedRecords, record);
          selectedRecords.splice(pruneRecord, 1);
        } else {
          selectedRecords.push(record);
        }
      }

      this.$data.clickedRow = record;
      this.__selectRecords(selectedRecords);
    },

    openRecord (record) {
      this.$emit('openRecord', record);
    },

    assign ({ lCallKey }) {
      this.$router.push({ name: 'AssignCall', params: { key: lCallKey } });
    },

    isSelected (record) {
      let keyName = this.config.recordKeyName;
      let target = find(this.__selectedRecords, [keyName, record[keyName]]);

      if (isNil(target)) return;

      // Clarify multiple dispatches on a single call
      let dispatchKeyName = has(target, 'CAL_lDispatchKey') ? 'CAL_lDispatchKey' : 'lDispatchKey';

      return is.all.truthy([
        target[keyName] === record[keyName],
        get(target, dispatchKeyName, '') === get(record, dispatchKeyName, '')
      ]);
    },

    refresh () {
      this.$emit('refresh');
    },

    editFilters (mode = 'form') {
      this.$router.push({
        name: 'GridSearch',
        params: { key: this.grid.Key },
        query: {
          noun: this.config.noun,
          mode: mode
        }
      });
    },

    editSettings () {
      this.$router.push({
        name: 'GridColumns',
        params: { key: this.grid.Key },
        query: { noun: this.config.noun }
      });
    },

    exportData (format) {
      this.$emit('exportData', { format: format, gridKey: this.grid.Key });
    },

    formatData (record, column) {
      // if (column.ID === 'dLastModified') {
      //   return relativeDate(record[column.ID]);
      // }

      if (is.startWith(column.ID, 'b')) {
        return affirmative(record[column.ID]);
      }

      return record[column.ID];
    },

    sortBy (column) {
      if (column.ID === 'lAppointmentETAMinutes') return;

      let sortBoolean = this.getSortBoolean(column);
      let ascendingBoolean = this.getAscendingBoolean(column);
      let sortPriority = this.getSortPriority(column);

      column.Ordered = toString(sortBoolean);
      column.Ascending = toString(ascendingBoolean);
      column.OrderPriority = toString(sortPriority);

      this.optimizeSortPriority();

      this.$emit('save', this.grid);
    },

    getSortBoolean (column) {
      return this.getSortOrientation(column) !== 'desc';
    },

    getAscendingBoolean (column) {
      return this.getSortOrientation(column) === '';
    },

    getSortPriority (column) {
      if (this.getSortOrientation(column) === 'asc') return column.OrderPriority;

      if (this.getSortOrientation(column) === 'desc') return 0;

      let highestPriority = max(this.sortableColumns, 'OrderPriority') || { OrderPriority: 0 };
      let priority = toNumber(highestPriority.OrderPriority);

      return priority + 1;
    },

    getSortOrientation ({ Ordered, Ascending }) {
      if (toString(Ordered) === 'false') return '';

      return toString(Ascending) === 'false' ? 'desc' : 'asc';
    },

    humanizeSortPriority ({ Ordered, OrderPriority }) {
      if (Ordered !== 'true') return '';

      return toString(OrderPriority) !== '0' ? OrderPriority : '';
    },

    sortIcon (column) {
      let icons = {
        '': '',
        asc: 'fa-sort-down',
        desc: 'fa-sort-up'
      };

      return icons[this.getSortOrientation(column)];
    },

    optimizeSortPriority () {
      forEach(this.sortableColumns, (column, index) => {
        column.OrderPriority = index + 1;
      });
    },

    beginResize (event) {
      if (event.target.dataset.gridId !== this.$data.gridId) return;
      if (event.target.className !== '-resizer') return;

      this.$data.workingColumn.record = find(this.columns, ['ID', event.target.dataset.id]);
      this.$data.workingColumn.startWidth = this.$data.workingColumn.record.Width;
      this.$data.workingColumn.isResizing = true;
      this.$data.workingColumn.startX = event.x;
    },

    isResizing (event) {
      if (!this.$data.workingColumn.isResizing) return;

      event.preventDefault();
      this.$data.workingColumn.endX = event.x;
      this.$data.workingColumn.record.Width = this.computedWidth;

      this.saveColumnSize();
    },

    endResize (event) {
      this.$data.workingColumn.isResizing = false;
    },

    saveColumnSize: debounce(function () {
      this.$emit('save', this.grid);
    }, 3000),

    cellValue (record, column) {
      if (column.ID.startsWith('b') && column.ID !== 'bOwnerWithVehicle') {
        return affirmative(record[column.ID]);
      }

      return record[column.ID];
    },

    selectorClasses (record) {
      return {
        'far': true,
        'fa-square': !this.isSelected(record),
        'fa-check-square': this.isSelected(record)
      };
    },

    rowClasses (record, options = {}) {
      return {
        '-row': true,
        '-highlighted': this.isSelected(record),
        '-allow-overflow': options.allow_overflow || false,
        '-ghost': is.inArray(toString(get(record, 'bActive', '1')), ['0', 'false']),
        '-hold': this.isOnHold(record)
      };
    },

    isOnHold (record) {
      let isOnHold1 = Number(this.$_.get(record, 'HLD_bHoldOn', 0));
      let isOnHold2 = this.$_.get(record, 'HLD_sHoldOn', 0);
      let isOnHold3 = Number(this.$_.get(record, 'bHold', ''));

      return isOnHold1 === 1 || isOnHold2 === 'On' || isOnHold3 === 1;
    },

    cellClasses (record, column) {
      // ...
    },

    isRequested (record) {
      return this.$_.get(record, 'isRequested', false);
    },

    hasErrors (record) {
      return this.$_.has(record, 'Errors');
    },

    viewInvoice (record) {
      this.$emit('viewInvoice', record);
    }
  },

  created () {
    document.addEventListener('mousedown', this.beginResize);
    document.addEventListener('mouseup', this.endResize);
    document.addEventListener('mousemove', this.isResizing);
  },

  destroyed () {
    document.removeEventListener('mousedown', this.beginResize);
    document.removeEventListener('mouseup', this.endResize);
    document.removeEventListener('mousemove', this.isResizing);
  }
};
</script>
