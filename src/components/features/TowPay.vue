<template>
  <app-modal
    title="Add Payment"
    @close="$emit('close')"
    :pad="false"
    :show="true">

    <div id="towpay">
      <section id="loader-section" v-show="!state.isReady">
        <app-loader></app-loader>
      </section>

      <ul class="towpay-tabs" v-show="state.isReady && state.tab.barIsEnabled">
        <li class="tab" v-show="state.tab.swipeIsEnabled" @click="state.tab.active = 'swipe'" :data-active="state.tab.active === 'swipe'">
          Reader
        </li>
        <li class="tab" @click="state.tab.active = 'entry'" :data-active="state.tab.active === 'entry'">
          Entry
        </li>
        <li class="tab" v-show="state.tab.linkIsEnabled" @click="state.tab.active = 'link'" :data-active="state.tab.active === 'link'">
          Link
        </li>
      </ul>

      <section id="swipe-section" v-show="state.isReady && state.tab.active === 'swipe'">
        <div class="input-panel" :data-show="!state.payWithCardReader.isPending">
          <app-grid-form context="inline">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-number v-model="amount" :required="true" :min="0" :max="maxAmountProxy">
                  Amount
                </app-number>
              </div>
              <div class="column is-6">
                <app-select v-model="receiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value" :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-select v-model="selectedReaderKey" :options="readers" keyAlias="Key" valueAlias="Name" :required="true">
                  Reader
                </app-select>
              </div>
              <div class="column is-6">
                <app-select v-model="receivedByKey" :options="employees" keyAlias="Key" valueAlias="Value" :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="note" maxlength="255">
                  Note
                </app-textarea>
              </div>
            </div>
          </app-grid-form>

          <app-button type="primary" @click="payWithCardReader" :disabled="!canPayWithCardReader">
            Send to reader
          </app-button>
        </div>

        <div class="feedback-panel" :data-show="state.payWithCardReader.isPending">
          <div class="_card">
            <i class="_icon fal fa-credit-card"></i>

            <div class="_instruction">
              Insert the card.
            </div>

            <div class="_actions">
              <app-button type="primary" @click="$emit('close')">
                Done
              </app-button>
              <app-button @click="cancelPayWithCardReader">
                Cancel
              </app-button>
            </div>
          </div>
        </div>
      </section>

      <section id="entry-section" v-show="state.isReady && state.tab.active === 'entry'">
        <div class="input-panel" :data-show="!state.payWithCardNotPresent.isComplete">
          <app-grid-form context="inline">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-number v-model="amount" :required="true" :min="0" :max="maxAmountProxy">
                  Amount
                </app-number>
              </div>
              <div class="column is-6">
                <app-select v-model="receiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value" :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-text v-model="cardholderName" :required="true">
                  Cardholder Name
                </app-text>
              </div>
              <div class="column is-6">
                <app-select v-model="receivedByKey" :options="employees" keyAlias="Key" valueAlias="Value" :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="note" maxlength="255">
                  Note
                </app-textarea>
              </div>
            </div>
          </app-grid-form>

          <div id="card-input"></div>

          <app-button type="primary" @click="payWithCardNotPresent" :disabled="!canPayWithCardNotPresent">
            Pay now
          </app-button>
        </div>

        <div class="feedback-panel" :data-show="state.payWithCardNotPresent.isComplete">
          <div class="_card">
            <i class="_icon fal fa-badge-check"></i>

            <div class="_instruction">
              Payment complete.
              <div class="_payment-number">Confirmation: {{ state.payWithCardNotPresent.payment.key }}</div>
            </div>

            <div class="_actions">
              <app-button type="primary" @click="$emit('close')">
                Done
              </app-button>
            </div>
          </div>
        </div>
      </section>

      <section id="link-section" v-if="state.isReady && state.tab.active === 'link'">
        <div class="input-panel" :data-show="!state.payWithLink2Pay.isSent">
          <app-grid-form context="inline">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-number v-model="amount" :required="true" :min="0" :max="maxAmountProxy">
                  Amount
                </app-number>
              </div>
              <div class="column is-6">
                <app-select v-model="receiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value" :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-text v-model="emailPhone" :required="true">
                  Email or Phone
                </app-text>
              </div>
              <div class="column is-6">
                <app-select v-model="receivedByKey" :options="employees" keyAlias="Key" valueAlias="Value" :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="note" maxlength="255">
                  Note
                </app-textarea>
              </div>
            </div>
          </app-grid-form>

          <app-button type="primary" @click="payWithLink2Pay" :disabled="!canPayWithLink2Pay">
            Send link
          </app-button>
        </div>

        <div class="feedback-panel" :data-show="state.payWithLink2Pay.isSent">
          <div class="_card">
            <i class="_icon fal fa-paper-plane"></i>

            <div class="_instruction">Link is sent.</div>

            <div class="_actions">
              <app-button type="primary" @click="$emit('close')">
                Done
              </app-button>
            </div>
          </div>
        </div>
      </section>

      <img class="powered-by" src="/static/towpay-logo.webp" alt="Powered by TowPay">
    </div>
  </app-modal>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'towpay',

  props: {
    callKey: { type: [Number, String], required: false },
    customerKey: { type: [Number, String], default: null },
    initialAmount: { type: [Number, String], default: null },
    isTowPayment: { type: Boolean, default: true }
  },

  data () {
    return {
      state: {
        isReady: false,
        isBusy: false,

        tab: {
          active: 'entry',
          barIsEnabled: false,
          swipeIsEnabled: false,
          entryIsEnabled: true,
          linkIsEnabled: false
        },

        payWithCardReader: {
          isPending: false,
          readerId: '',
          intentId: ''
        },

        payWithCardNotPresent: {
          isComplete: false,
          payment: {
            key: '',
            intentId: ''
          }
        },

        payWithLink2Pay: {
          isSent: false
        }
      },

      selectedReaderKey: '',
      cardholderName: '',
      receiptTypeKey: '',
      receivedByKey: '',
      emailPhone: '',
      note: '',
      amount: '',
      maxAmount: '',
      receiptTypes: [],
      employees: [],
      readers: [],

      stripe: null,
      elements: null,
      cardElement: null
    };
  },

  computed: {
    ...mapGetters([
      '__state'
    ]),

    canPayWithCardReader () {
      return !this.$data.state.isBusy &&
        this.$data.amount &&
        this.$data.selectedReaderKey &&
        this.$data.receiptTypeKey &&
        this.$data.receivedByKey;
    },

    canPayWithCardNotPresent () {
      return !this.$data.state.isBusy &&
        this.$data.amount &&
        this.$data.cardholderName &&
        this.$data.receiptTypeKey &&
        this.$data.receivedByKey;
    },

    canPayWithLink2Pay () {
      return !this.$data.state.isBusy &&
        this.$data.amount &&
        this.$data.emailPhone &&
        this.$data.receiptTypeKey &&
        this.$data.receivedByKey;
    },

    reader () {
      return this.$_.find(this.$data.readers, ['Key', this.$data.selectedReaderKey]);
    },

    defaultSubterminalKey () {
      return this.$data.subterminals[0].Key;
    },

    maxAmountProxy () {
      return this.$data.maxAmount.length > 0 ? Number(this.$data.maxAmount) : null;
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getEmployees',

      'PAYMENT__sendLinkToPay',
      'PAYMENT__getReceiptTypes',
      'PAYMENT__getSubterminals',
      'PAYMENT__getCCPaymentInfo',
      'PAYMENT__sendTowPayIntentToReader',
      'PAYMENT__cancelTowPayReaderIntent',
      'PAYMENT__createTowPayNotPresentPayment',

      'TOPSCALL__sendLinkToPay',
      'TOPSCALL__getCCPaymentInfo',
      'TOPSCALL__sendTowPayIntentToReader',
      'TOPSCALL__cancelTowPayReaderIntent',
      'TOPSCALL__createTowPayNotPresentPayment'
    ]),

    getPaymentInfo () {
      if (this.callKey) {
        return new Promise(resolve => {
          this.TOPSCALL__getCCPaymentInfo({
            callKey: this.callKey,
            isTowPayment: this.isTowPayment,
            success: response => {
              resolve(response);
            }
          });
        });
      } else {
        return new Promise(resolve => {
          this.PAYMENT__getCCPaymentInfo({
            subterminalKey: this.defaultSubterminalKey,
            success: response => {
              resolve(response);
            }
          });
        });
      }
    },

    getSubterminals () {
      return new Promise(resolve => {
        this.PAYMENT__getSubterminals({
          success: response => {
            resolve(response);
          }
        });
      });
    },

    populateReceiptTypes () {
      this.PAYMENT__getReceiptTypes({
        callback: response => {
          this.$data.receiptTypes = response;
        }
      });
    },

    populateEmployees () {
      this.TOPSCOMPANY__getEmployees({
        callback: response => {
          this.$data.employees = response;
        }
      });
    },

    populateAmount () {
      if (!this.callKey) { return; }
      if (this.$data.amount) { return; }

      this.$store.dispatch('TOPSCALL__read', {
        callKey: this.callKey,
        success: response => {
          this.$data.maxAmount = Number(response.tcBalance).toFixed(2);
          this.$data.amount = this.$data.maxAmount;
        }
      });
    },

    payWithCardReader () {
      this.$data.isBusy = true;

      if (this.callKey) {
        this.TOPSCALL__sendTowPayIntentToReader({
          callKey: this.callKey,
          readerID: this.reader.Key,
          readerType: this.reader.Type,
          isTowPayment: this.isTowPayment,
          amount: this.$data.amount,
          note: this.$data.note,
          receiptType: this.$data.receiptTypeKey,
          employeeKey: this.$data.receivedByKey,
          success: response => {
            this.$data.state.payWithCardReader.intentId = response.IntentID;
            this.$data.state.payWithCardReader.readerId = this.reader.Key;
            this.$data.state.payWithCardReader.isPending = true;
            this.$data.isBusy = false;
          }
        });
      } else {
        this.PAYMENT__sendTowPayIntentToReader({
          subterminalKey: this.defaultSubterminalKey,
          customerKey: this.customerKey,
          readerID: this.reader.Key,
          readerType: this.reader.Type,
          isTowPayment: this.isTowPayment,
          amount: this.$data.amount,
          note: this.$data.note,
          receiptType: this.$data.receiptTypeKey,
          employeeKey: this.$data.receivedByKey,
          success: response => {
            this.$data.state.payWithCardReader.intentId = response.IntentID;
            this.$data.state.payWithCardReader.readerId = this.reader.Key;
            this.$data.state.payWithCardReader.isPending = true;
            this.$data.isBusy = false;
          }
        });
      }
    },

    cancelPayWithCardReader () {
      this.$data.isBusy = true;

      if (this.callKey) {
        this.TOPSCALL__cancelTowPayReaderIntent({
          callKey: this.callKey,
          intentId: this.$data.state.payWithCardReader.intentId,
          readerId: this.$data.state.payWithCardReader.readerId,
          success: () => {
            this.$data.state.payWithCardReader.intentId = '';
            this.$data.state.payWithCardReader.readerId = '';
          },
          always: () => {
            this.$data.state.payWithCardReader.isPending = false;
            this.$data.isBusy = false;
          }
        });
      } else {
        this.PAYMENT__cancelTowPayReaderIntent({
          intentId: this.$data.state.payWithCardReader.intentId,
          readerId: this.$data.state.payWithCardReader.readerId,
          success: () => {
            this.$data.state.payWithCardReader.intentId = '';
            this.$data.state.payWithCardReader.readerId = '';
          },
          always: () => {
            this.$data.state.payWithCardReader.isPending = false;
            this.$data.isBusy = false;
          }
        });
      }
    },

    payWithLink2Pay () {
      this.$data.isBusy = true;

      if (this.callKey) {
        this.TOPSCALL__sendLinkToPay({
          callKey: this.callKey,
          emailPhone: this.$data.emailPhone,
          isTowPayment: this.isTowPayment,
          amount: this.$data.amount,
          note: this.$data.note,
          receiptType: this.$data.receiptTypeKey,
          employeeKey: this.$data.receivedByKey,
          success: response => {
            this.$data.state.payWithLink2Pay.isSent = true;
            this.$data.isBusy = false;

            this.$emit('created', response);
          }
        });
      } else {
        this.PAYMENT__sendLinkToPay({
          subterminalKey: this.defaultSubterminalKey,
          customerKey: this.customerKey,
          emailPhone: this.$data.emailPhone,
          amount: this.$data.amount,
          note: this.$data.note,
          receiptType: this.$data.receiptTypeKey,
          employeeKey: this.$data.receivedByKey,
          success: response => {
            this.$data.state.payWithLink2Pay.isSent = true;
            this.$data.isBusy = false;

            this.$emit('created', response);
          }
        });
      }
    },

    async payWithCardNotPresent () {
      this.$data.state.isBusy = true;

      const paymentMethod = await this.createStripePaymentMethod();

      const payment = await this.createTopsPayment({
        methodId: paymentMethod.id,
        name: paymentMethod.name,
        brand: paymentMethod.brand,
        expiry: paymentMethod.expiry,
        lastFour: paymentMethod.lastFour
      });

      this.$emit('created', payment);

      this.$data.state.payWithCardNotPresent.payment.key = payment.key;
      this.$data.state.payWithCardNotPresent.payment.intentId = payment.intentId;
      this.$data.state.payWithCardNotPresent.isComplete = true;
      this.$data.state.isBusy = false;
    },

    async createStripePaymentMethod () {
      return new Promise(async (resolve, reject) => {
        const response = await this.$data.stripe.createPaymentMethod({
          type: 'card',
          card: this.$data.cardElement,
          billing_details: {
            name: this.$data.cardholderName
          }
        });

        if (!('paymentMethod' in response)) {
          reject('Unable to create payment method.');
        }

        const expiryMonth = response.paymentMethod.card.exp_month || '';
        const expiryYear = response.paymentMethod.card.exp_year.toString().substring(2, 4) || '';
        const expiry = `${expiryMonth}${expiryYear}`;

        resolve({
          id: response.paymentMethod.id || '',
          name: response.paymentMethod.billing_details.name || '',
          brand: response.paymentMethod.card.brand || '',
          expiry: expiry,
          lastFour: response.paymentMethod.card.last4 || ''
        });
      });
    },

    async createTopsPayment ({ methodId, name, brand, expiry, lastFour }) {
      if (this.callKey) {
        return new Promise(async (resolve, reject) => {
          this.TOPSCALL__createTowPayNotPresentPayment({
            callKey: this.callKey,
            isTowPayment: this.isTowPayment,
            amount: this.$data.amount,
            paymentMethodId: methodId,
            name: name,
            brand: brand,
            expiry: expiry,
            lastFour: lastFour,
            note: this.$data.note,
            receiptType: this.$data.receiptTypeKey,
            employeeKey: this.$data.receivedByKey,
            success: response => {
              resolve({
                intentId: response.IntentID,
                key: response.PaymentKey
              });
            }
          });
        });
      } else {
        return new Promise(async (resolve, reject) => {
          this.PAYMENT__createTowPayNotPresentPayment({
            subterminalKey: this.defaultSubterminalKey,
            customerKey: this.customerKey,
            amount: this.$data.amount,
            paymentMethodId: methodId,
            name: name,
            brand: brand,
            expiry: expiry,
            lastFour: lastFour,
            note: this.$data.note,
            receiptType: this.$data.receiptTypeKey,
            employeeKey: this.$data.receivedByKey,
            success: response => {
              resolve({
                intentId: response.IntentID,
                key: response.PaymentKey
              });
            }
          });
        });
      }
    }
  },

  async mounted () {
    this.$data.amount = this.initialAmount;

    this.populateReceiptTypes();
    this.populateEmployees();
    this.$data.subterminals = await this.getSubterminals();

    const paymentInfo = await this.getPaymentInfo();

    this.$data.readers = paymentInfo.Readers;

    this.$data.selectedReaderKey = paymentInfo.LastReaderKey || this.$_.get(this.$data.readers, '[0].Key', '');

    // Initialize tabs
    this.$data.state.tab.swipeIsEnabled = this.$data.readers.length;
    this.$data.state.tab.linkIsEnabled = this.__state.topsCompany.settings.bAllowLinkToPay;

    if (this.$data.state.tab.swipeIsEnabled) {
      this.$data.state.tab.active = 'swipe';
    }

    if (this.$data.state.tab.swipeIsEnabled || this.$data.state.tab.linkIsEnabled) {
      this.$data.state.tab.barIsEnabled = true;
    }

    this.$data.state.isReady = true;

    // Initialize Stripe Elements
    this.$data.stripe = window.Stripe(paymentInfo.PublicKey, {
      stripeAccount: paymentInfo.AccountID
    });
    this.$data.elements = this.$data.stripe.elements();

    const style = {
      base: {
        fontFamily: '"Ubuntu", sans-serif',
        fontSize: '16px',
        color: '#32325d',
        '::placeholder': {
          color: '#5081ED'
        }
      },
      invalid: {
        fontFamily: '"Ubuntu", sans-serif',
        color: '#fa755a',
        iconColor: '#fa755a'
      }
    };

    this.$data.cardElement = this.$data.elements.create('card', { style: style });
    this.$data.cardElement.mount('#card-input');
  }
};
</script>
