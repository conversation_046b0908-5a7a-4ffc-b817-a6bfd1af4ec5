<template>
  <div v-if="clipboardAvailable">
    <app-button class="btn btn-secondary" @click="pasteLatLong" tooltip="Paste Lat/Lon">Paste Lat/Lon</app-button>
  </div>
</template>

<script>
export default {
  name: 'LatLonPasteButton',
  data () {
    return {
      clipboardAvailable: false
    };
  },
  methods: {
    async checkClipboardPermission () {
      this.clipboardAvailable = !!navigator.clipboard.readText;
      console.log('Clipboard access available: ' + this.clipboardAvailable);
    },
    async pasteLatLong () {
      try {
        const text = await navigator.clipboard.readText();
        const coords = text.split(',');

        if (coords.length === 2) {
          const latitude = parseFloat(coords[0].trim());
          const longitude = parseFloat(coords[1].trim());

          if (!isNaN(latitude) && !isNaN(longitude)) {
            // Emitting the latitude and longitude to the parent component
            this.$emit('update-coords', { latitude, longitude });
          } else {
            // eslint-disable-next-line no-undef
            alert('Clipboard does not contain valid latitude and longitude values.');
          }
        } else {
          // eslint-disable-next-line no-undef
          alert('Clipboard does not contain two values separated by a comma.');
        }
      } catch (err) {
        // eslint-disable-next-line no-undef
        console.log('Failed to read clipboard contents: ' + err);
      }
    }
  },
  mounted () {
    this.checkClipboardPermission();
  }
};
</script>
