<template>
  <div>
    <app-modal class="quick-views" :show="isVisible" title="Quick views" @close="close">
      <div class="container">
        <template v-if="views.length > 0">
          <label>Saved Views</label>

          <template v-for="view in views">
            <div class="field has-addons" :key="view.Key">
              <p class="control">
                <input v-model="view.Name" class="input is-full" type="text" placeholder="Name">
              </p>
              <p class="control">
                <input v-model="view.Description" class="input is-full" type="text" placeholder="Description">
              </p>
              <p class="control">
                <button @click.prevent="load(view)" class="button" title="Load View"><i class="fal fa-cloud-download"></i></button>
              </p>
              <p class="control">
                <button @click.prevent="update(view)" class="button" title="Save Changes" :disabled="!canUpdate(view)"><i class="fal fa-cloud-upload"></i></button>
              </p>
              <p class="control">
                <button @click.prevent="remove(view)" class="button"><i class="far fa-trash-alt"></i></button>
              </p>
            </div>
          </template>
          <hr>
        </template>

        <label>Add View</label>
        <div class="field has-addons">
          <p class="control">
            <input v-model="viewShape.Name" class="input is-full" type="text" placeholder="Name">
          </p>
          <p class="control">
            <input v-model="viewShape.Description" class="input is-full" type="text" placeholder="Description">
          </p>
          <p class="control width-auto">
            <button @click.prevent="addUserView" class="button" title="Add User View" :disabled="!canAdd"><i class="fal fa-user-plus"></i></button>
          </p>
          <p class="control">
            <button @click.prevent="addGroupView" class="button" title="Add Group View" :disabled="!canAdd"><i class="fal fa-users"></i></button>
          </p>
        </div>

        <hr>

        <label>Advanced</label>
        <app-button @click="clearTemporaryData">Clear Temporary Data</app-button>
        <!-- <app-button @click="clearFilters">Clear View Filters</app-button> -->
        <app-button @click="resetView">Reset Columns &amp; Filters</app-button>
      </div>
    </app-modal>

    <portal to="quick-views">
      <a class="link" @click="toggleVisibility" title="Quick views" key="quick-views">
        Quick Views
      </a>
    </portal>
  </div>
</template>

<script>
import { EVENT_INFO } from '@/config.js';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'view-settings',

  props: {
    noun: { type: String, required: true },
    viewKey: { type: [Number, String], default: null },
    viewUuid: { type: String, default: null }
  },

  data () {
    return {
      views: [],
      columnCap: 50,
      loadedView: {},
      isVisible: false,
      viewShape: {
        Name: '',
        Description: ''
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'RECORDS__settings'
    ]),

    canAdd () {
      return !this.$_.isEmpty(this.$data.viewShape.Name);
    }
  },

  watch: {
    isVisible () {
      this.getViewList();
    }
  },

  methods: {
    ...mapActions([
      '__uncacheFilter',
      '__setCurrentView',
      'USERVIEW__delete',
      'USERVIEW__getList',
      'RECORDS__getSettings',
      'RECORDS__saveSettings',
      'RECORDS__cacheSettings',
      'USERVIEW__deleteDefaultView'
    ]),

    getViewList () {
      if (!this.isVisible) return;

      this.USERVIEW__getList({
        viewKey: this.RECORDS__settings.Key,
        orgUnit: this.__state.orgUnitKey,
        callback: response => {
          this.$data.views = response;
        }
      });
    },

    load (view) {
      this.$data.loadedView = view;

      this.RECORDS__getSettings({
        noun: this.noun,
        userViewKey: view.Key,
        callback: response => {
          let prunedColumns = this.pruneColumns(response);

          this.RECORDS__cacheSettings(prunedColumns);

          this.RECORDS__saveSettings({
            noun: this.noun,
            data: prunedColumns,
            callback: () => {
              this.$emit('viewLoaded');
              this.close();
            }
          });
        }
      });
    },

    pruneColumns (settings) {
      this.$_.forEach(settings.Grids, grid => {
        if (grid.Columns.length > this.$data.columnCap) {
          grid.Columns = this.$_.take(grid.Columns, this.$data.columnCap);
        }
      });

      return settings;
    },

    canUpdate (view) {
      return view.Key === this.$data.loadedView.Key;
    },

    update (view) {
      this.USERVIEW__delete({
        key: view.Key,
        callback: () => {
          this.save({
            userViewKey: -1,
            orgUnit: '',
            name: view.Name,
            description: view.Description,
            getViewListOnSuccess: true
          });
        }
      });
    },

    remove (view, callback) {
      this.$data.views = this.$_.reject(this.$data.views, ['Key', view.Key]);

      this.USERVIEW__delete({ key: view.Key });
    },

    save (primer) {
      this.RECORDS__saveSettings({
        noun: this.noun,
        data: {
          UserViewKey: primer.userViewKey,
          Key: this.RECORDS__settings.Key,
          OrgUnit: primer.orgUnit,
          Name: primer.name,
          Description: primer.description,
          Grids: this.RECORDS__settings.Grids
        },
        callback: response => {
          if (primer.closeOnSuccess) this.close();
          if (primer.getViewListOnSuccess) this.getViewList();
        }
      });
    },

    addUserView () {
      this.save({
        userViewKey: -1,
        orgUnit: '',
        name: this.$data.viewShape.Name,
        description: this.$data.viewShape.Description,
        getViewListOnSuccess: true
      });

      this.$data.viewShape.Name = '';
      this.$data.viewShape.Description = '';
    },

    addGroupView () {
      this.save({
        userViewKey: -1,
        orgUnit: this.__state.orgUnitKey,
        name: this.$data.viewShape.Name,
        description: this.$data.viewShape.Description,
        getViewListOnSuccess: true
      });

      this.$data.viewShape.Name = '';
      this.$data.viewShape.Description = '';
    },

    clearTemporaryData () {
      this.$hub.$emit(EVENT_INFO, 'Temporary data has been cleared. You may notice slower performance temporarily.');

      window.sessionStorage.clear();
    },

    // clearFilters () {
    //   this.$hub.$emit(EVENT_INFO, 'View filters have been cleared.');

    //   this.__uncacheFilter('*');

    //   forEach(this.RECORDS__settings.Grids, grid => {
    //     grid.Filters = [];
    //   });

    //   this.save({
    //     userViewKey: '',
    //     orgUnit: '',
    //     name: this.RECORDS__settings.Title,
    //     description: this.RECORDS__settings.Description,
    //     closeOnSuccess: true,
    //   });
    // },

    resetView () {
      this.$hub.$emit(EVENT_INFO, 'View columns and filters have been cleared.');

      this.USERVIEW__deleteDefaultView({
        key: this.RECORDS__settings.Key,
        callback: () => {
          this.close();
          this.$emit('viewLoaded');
        }
      });
    },

    close () {
      this.$data.isVisible = false;
    },

    toggleVisibility () {
      this.$data.isVisible = !this.$data.isVisible;
    }
  },

  mounted () {
    this.getViewList();
  }
};
</script>
