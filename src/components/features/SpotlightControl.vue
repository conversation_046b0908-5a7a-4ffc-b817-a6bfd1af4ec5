<template>
  <div class="spotlight-control" v-if="inputsProxy.length">
    <control-flyout :id="flyoutId" :close-on-blur="false">
      <app-button type="white" title="Record search">
        <i class="_icon far fa-search"></i> Quick Search
      </app-button>

      <div class="spotlight-control__reveal" @keyup.enter="search" slot="reveal">
        <app-grid-form>
          <div class="columns is-multiline">
            <div class="column is-12 is-left" v-for="input in inputsProxy" :key="input.Key">
              <component
                :is="input.Tag"
                v-model="input.Value">
                {{ input.Name }}
              </component>
            </div>
          </div>
        </app-grid-form>
        <div class="_controls">
          <app-button type="default" @click="cancel">Cancel</app-button>
          <div class="_flexer"></div>
          <app-button type="primary" @click="search">Find</app-button>
        </div>
      </div>
    </control-flyout>
  </div>
</template>

<script>
import {
  EVENT_TOGGLE_FLYOUT,
  STASH_FILTER_RELAY
} from '@/config';

export default {
  name: 'spotlight-control',

  props: {
    noun: { default: null }
  },

  data () {
    return {
      inputs: [],
      filters: [],
      flyoutId: 'spotlight_flyout'
    };
  },

  computed: {
    inputsProxy () {
      return this.$_.get(this.$data, 'inputs', []);
    }
  },

  methods: {
    fetchSearchFields () {
      this.$store.dispatch('RECORDS__getQuickSearchColumns', {
        noun: this.noun,
        success: response => {
          this.$data.inputs = response.map(input => ({
            ...input,
            Value: '',
            Tag: 'app-text' // Default to app-text for all types currently
          }));
        }
      });
    },

    cancel () {
      this.$data.filters = [];
      window.sessionStorage.removeItem(STASH_FILTER_RELAY);
      this.$emit('resetFilters');
      this.$hub.$emit(EVENT_TOGGLE_FLYOUT, {
        id: this.$data.flyoutId,
        visible: false
      });
    },

    search () {
      const targetInput = this.$_.find(this.inputsProxy, input => input.Value);
      if (!targetInput) return;

      this.$store.dispatch('RECORDS__createQuickSearch', {
        noun: this.noun,
        columnKey: targetInput.Key,
        columnId: targetInput.ID,
        value: targetInput.Value,
        success: response => {
          this.$data.filters = response;

          if (this.$data.filters.length) {
            window.sessionStorage.setItem(
              STASH_FILTER_RELAY,
              JSON.stringify(this.$data.filters)
            );
          }

          this.$emit('setFilters', this.$data.filters);
          this.$hub.$emit(EVENT_TOGGLE_FLYOUT, {
            id: this.$data.flyoutId,
            visible: false
          });
        }
      });
    },

    setInitialFocus () {
      const input = document.querySelector('.spotlight-control input');
      if (!input) return;
      setTimeout(() => input.focus(), 300);
    }
  },

  mounted () {
    this.fetchSearchFields();
  }
};
</script>
