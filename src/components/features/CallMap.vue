<template>
  <div class="call-map" style="height: 100%">
    <app-button
      class="undo"
      v-if="markerPositions.length > 1"
      @click="undoPosition"
      type="white">
      <i class="fas fa-rotate-left"></i> Put back
    </app-button>

    <div ref="mapContainer" style="height: 100%"></div>
  </div>
</template>

<script>
export default {
  name: 'call-map',

  props: {
    center: { default: () => ({ lat: 0, lng: 0 }) },
    zoom: { default: 13 },
    markers: { default: () => [] },
    callKey: { default: null },
    isRetow: { default: false },
    zoomControl: { default: false }
  },

  data() {
    return {
      map: null,
      googleMarkers: [],
      options: {
        zoomControl: this.zoomControl,
        mapTypeControl: false,
        scaleControl: false,
        streetViewControl: false,
        rotateControl: false,
        fullscreenControl: false,
        disableDefaultUI: false
      },
      markerPositions: []
    };
  },

  watch: {
    markers: {
      deep: true,
      handler: function() {
        this.updateMarkers();
      }
    },
    center: {
      deep: true,
      handler: function(newCenter) {
        if (this.map) {
          this.map.setCenter(newCenter);
        }
      }
    },
    zoom: function(newZoom) {
      if (this.map) {
        this.map.setZoom(newZoom);
      }
    }
  },

  mounted() {
    this.initMap();
  },

  methods: {
    initMap() {
      if (!window.google || !window.google.maps) {
        // Load Google Maps script if not already loaded
        this.loadGoogleMapsScript().then(() => {
          this.createMap();
        });
      } else {
        this.createMap();
      }
    },

    loadGoogleMapsScript() {
      return new Promise((resolve) => {
        if (window.google && window.google.maps) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?client=${this.$googleMapsSettings.client}`;
        script.async = true;
        script.defer = true;
        script.onload = resolve;
        document.head.appendChild(script);
      });
    },

    createMap() {
      const google = window.google;
      this.map = new google.maps.Map(this.$refs.mapContainer, {
        center: this.center,
        zoom: this.zoom,
        ...this.options
      });

      this.initializeMarkerPositions();
      this.updateMarkers();
    },

    updateMarkers() {
      if (!this.map || !window.google) return;

      // Clear existing markers
      this.googleMarkers.forEach(marker => marker.setMap(null));
      this.googleMarkers = [];

      // Add new markers
      this.markers.forEach((marker, index) => {
        const googleMarker = new google.maps.Marker({
          position: marker.position,
          map: this.map,
          icon: marker.icon,
          draggable: marker.draggable
        });

        if (marker.clickable) {
          googleMarker.addListener('click', (event) => {
            this.$emit('marker-click', {
              $event: { domEvent: event },
              marker,
              index
            });
          });
        }

        if (marker.draggable) {
          googleMarker.addListener('dragend', (event) => {
            this.onMarkerDrop(event, marker);
          });
        }

        this.googleMarkers.push(googleMarker);
      });
    },

    async onMarkerDrop(event, marker) {
      marker.position.lat = event.latLng.lat();
      marker.position.lng = event.latLng.lng();

      this.markerPositions.push({
        key: marker.key,
        latitude: event.latLng.lat(),
        longitude: event.latLng.lng()
      });

      this.$emit('marker-drop', marker);

      await this.setTowLocation({
        latitude: event.latLng.lat(),
        longitude: event.latLng.lng()
      });
    },

    async undoPosition() {
      // Pop current position
      this.markerPositions.pop();

      // Get previous position
      const previousPosition = this.markerPositions[this.markerPositions.length - 1];

      if (!previousPosition) { return; }

      this.markers.forEach(marker => {
        if (marker.key === previousPosition.key) {
          marker.position.lat = previousPosition.latitude;
          marker.position.lng = previousPosition.longitude;

          this.$emit('marker-drop', marker);

          this.setTowLocation({
            latitude: previousPosition.latitude,
            longitude: previousPosition.longitude
          });
        }
      });

      this.updateMarkers();
    },

    setTowLocation({ latitude, longitude }) {
      return new Promise(resolve => {
        if (!this.callKey) { resolve(); }

        this.$store.dispatch('TOPSCALL__updateTowLocation', {
          callKey: this.callKey,
          latitude: latitude,
          longitude: longitude,
          isRetow: this.isRetow,
          success: response => {
            resolve(response);
          }
        });
      });
    },

    initializeMarkerPositions() {
      this.markerPositions = [];
      this.markers.forEach(marker => {
        if (!marker.draggable) { return; }

        this.markerPositions.push({
          key: marker.key,
          latitude: marker.position.lat,
          longitude: marker.position.lng
        });
      });
    }
  }
};
</script>
