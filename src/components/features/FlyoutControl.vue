<template>
  <button class="flyout-control" :id="id">
    <div class="flyout-control__trigger" ref="trigger" @focus="onFocus" @click="onFocus" tabindex="-1">
      <slot></slot>
    </div>

    <div class="flyout-control__content" ref="reveal" @blur="onBlur" tabindex="-1" :style="coordinatesToCss">
      <slot name="reveal"></slot>
    </div>
  </button>
</template>

<script>
import { EVENT_TOGGLE_FLYOUT } from '@/config.js';

export default {
  name: 'flyout-control',

  props: {
    id: { default: 'flyout_' + Math.floor((Math.random() * 999) + 1) },
    closeOnBlur: { default: true }
  },

  data () {
    return {
      visible: false,

      revealCoordinates: {
        stale: {},
        fresh: {
          top: 0,
          left: 0,
          pad: ''
        }
      },

      triggerCoordinates: {}
    };
  },

  computed: {
    visibleProxy: {
      get () {
        return this.$data.visible;
      },

      set (value) {
        this.$data.visible = value;

        this.$nextTick(() => {
          this.animate();
        });
      }
    },

    coordinatesToCss () {
      return {
        '--top': this.$data.revealCoordinates.fresh.top,
        '--left': this.$data.revealCoordinates.fresh.left,
        '--pad': this.$data.revealCoordinates.fresh.pad
      };
    }
  },

  methods: {
    animate () {
      let timeline = this.$gsap.timeline({
        defaults: {
          duration: 0.4,
          stagger: 0.05
        }
      });

      if (this.visibleProxy) {
        timeline
          .call(() => {
            this.exploreCoordinates();
          })
          .fromTo(this.$refs.reveal, {
            autoAlpha: 0,
            y: -10
          }, {
            autoAlpha: 1,
            y: 0,
            ease: 'back.out(2)'
          })
          ;
      } else {
        timeline.to(this.$refs.reveal, {
          autoAlpha: 0,
          y: -10
        });
      }
    },

    onFocus () {
      this.visibleProxy = true;

      this.$nextTick(() => {
        this.$refs.reveal.focus();
      });
    },

    onBlur () {
      if (this.closeOnBlur) {
        this.visibleProxy = false;
      }
    },

    exploreCoordinates () {
      if (this.$refs.trigger) {
        this.$data.triggerCoordinates = this.$refs.trigger.getBoundingClientRect();
      }

      if (this.$refs.reveal) {
        this.$data.revealCoordinates.stale = this.$refs.reveal.getBoundingClientRect();
      }

      // Position relative to trigger
      this.$data.revealCoordinates.fresh.top = this.$data.triggerCoordinates.height;

      // Adjust relative to viewport
      if (this.$data.revealCoordinates.stale.right > window.innerWidth) {
        this.$data.revealCoordinates.fresh.left = -(this.$data.revealCoordinates.stale.right - window.innerWidth);
        this.$data.revealCoordinates.fresh.pad = '0.5rem';
      }
    }
  },

  mounted () {
    this.$gsap.set(this.$refs.reveal, { autoAlpha: 0 });

    this.$hub.$on(EVENT_TOGGLE_FLYOUT, payload => {
      if (payload.id === this.id) {
        this.visibleProxy = payload.visible;
      }
    });

    window.addEventListener('resize', () => {
      this.exploreCoordinates();
    });
  },

  beforeDestroy () {
    window.removeEventListener('resize', () => {
      this.exploreCoordinates();
    });
    this.$hub.$off(EVENT_TOGGLE_FLYOUT);
  }
};
</script>
