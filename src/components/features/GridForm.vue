<template>
  <div :class="formClasses">
    <div :class="linerClasses">
      <slot></slot>
    </div>
  </div>  <!-- /grid-form -->
</template>

<script>
export default {
  name: 'grid-form',

  props: {
    context: { type: String, required: false, default: 'full' }
  },

  computed: {
    formClasses () {
      return {
        'grid-form': true,
        'is-inline-context': this.context === 'inline', // Section within a larger form
        'is-full-context': this.context === 'full', // Single full form on a view
        'is-modal-context': this.context === 'modal' // Displayed inside a modal
      };
    },

    linerClasses () {
      return {
        'grid-form__liner': true,
        'form': true,
        'is-grid': true,
        'is-inline-context': this.context === 'inline', // Section within a larger form
        'is-full-context': this.context === 'full', // Single full form on a view
        'is-modal-context': this.context === 'modal' // Displayed inside a modal
      };
    }
  }
};
</script>
