<template>
  <span class="payment-type">{{ label }}</span>
</template>

<script>
export default {
  name: 'payment-type',

  props: {
    id: { required: true }
  },

  data () {
    return {
      value: '',
      options: []
    };
  },

  computed: {
    label () {
      const paymentType = this.options.find(option => option.Key === this.id);
      return paymentType ? paymentType.Value : '';
    }
  },

  methods: {
    async fetchOptions () {
      this.$store.dispatch('PAYMENT__getPaymentTypes', {
        callback: response => {
          this.options = response;
        }
      });
    }
  },

  async mounted () {
    this.fetchOptions();
  }
};
</script>
