<template>
  <div class="price-guard"
    @focusin.capture="onFocusIn"
    @focusout.capture="onFocusOut"
    @change.capture="onChange">

    <slot></slot>

  </div>
</template>

<script>
import { EVENT_RESET_CALL_PRICING } from '@/config';

export default {
  name: 'price-guard',

  props: {
    call: {
      type: Object,
      required: true
    },
    forceReset: {
      type: Boolean,
      default: false
    },
    isSedated: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      isGuardAlert: false
    };
  },

  computed: {
    shouldPromptReset () {
      return this.isGuardAlert &&
        Number(this.call.lTowTypeKey) > 0 &&
        Number(this.call.lCustomerKey) > 0 &&
        Number(this.call.lSubterminalKey) > 0;
    }
  },

  methods: {
    onFocusIn () {
      if (this.isSedated) return;

      this.isGuardAlert = true;
    },

    onFocusOut () {
      if (this.$el.contains(document.activeElement)) return false;

      this.isGuardAlert = false;
    },

    async onChange () {
      const child = this.$children[0];

      const model = child.$vnode.data.model.expression;
      const modelSegments = model.split('.');
      const fieldId = modelSegments[modelSegments.length - 1];

      // const callKeyVariant1 = this.call.lCallKey;
      // const callKeyVariant2 = this.call.callKey;
      // const callKey = callKeyVariant1 || callKeyVariant2;

      // if (!callKey) return;
      if (!this.shouldPromptReset) return;

      setTimeout(async () => {
        const childState = await child.getInternalState();

        if (this.forceReset) {
          // Do not prompt
        } else {
          const isConfirmed = await this.confirmReset();
          if (!isConfirmed) return;
        }

        this.$hub.$emit(EVENT_RESET_CALL_PRICING, {
          fieldId: fieldId,
          newValue: childState.newValue,
          oldValue: childState.oldValue
        });
      }, 300);
    },

    confirmReset () {
      return new Promise(resolve => {
        this.$confirm('This may affect the pricing currently in place.', 'Reset Pricing',
          {
            confirmButtonText: 'Reset pricing',
            cancelButtonText: 'Keep pricing',
            type: 'warning'
          })
          .then(() => {
            // Reset
            resolve(true);
          }).catch(() => {
            // Keep
            resolve(false);
          });
      });
    }
  }
};
</script>
