<template>
  <span class="employee-name">{{ name }}</span>
</template>

<script>
export default {
  name: 'employee-name',

  props: {
    id: { required: true }
  },

  data () {
    return {
      value: '',
      options: []
    };
  },

  computed: {
    name () {
      const employee = this.options.find(option => option.Key === this.id);
      return employee ? employee.Value : '';
    }
  },

  methods: {
    async fetchOptions () {
      this.$store.dispatch('TOPSCOMPANY__getEmployees', {
        callback: response => {
          this.options = response;
        }
      });
    }
  },

  async mounted () {
    this.fetchOptions();
  }
};
</script>
