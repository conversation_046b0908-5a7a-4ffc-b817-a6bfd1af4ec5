<template>
  <label class="card-swipe" @click="swipedInput = ''">
    <div class="_icon-placeholder">
      <i class="_icon -processing fal fa-spinner-third fa-spin" v-if="state.processing" key="processing"></i>
      <i class="_icon -complete fal fa-check-circle" v-else-if="state.complete" key="complete"></i>
      <i class="_icon -error fal fa-exclamation-circle" v-else-if="state.error" key="error"></i>
      <i class="_icon -card fal fa-id-card" v-else key="card"></i>
    </div>

    <div class="_text-placeholder">
      <div class="_text -initialize" v-if="!state.ready" key="make-ready">Swipe license</div>
      <div class="_text -ready" v-else key="is-ready">
        <span v-if="state.processing">Processing&hellip;</span>
        <span v-else>Awaiting swipe</span>
      </div>
    </div>

    <input class="_input" type="text" v-model="swipedInput" @focus="state.ready = true" @blur="state.ready = false" ref="input"/>
  </label>
</template>

<script>
import { debounce } from 'lodash';
import { mapActions } from 'vuex';
import { EVENT_WARNING } from '@/config.js';

export default {
  name: 'card-swipe',

  data () {
    return {
      state: {
        ready: false,
        processing: false,
        complete: false,
        error: false
      },

      swipedInput: '',

      parsedInput: {
        type: '',
        state: '',
        city: '',
        lastname: '',
        firstname: '',
        middlename: '',
        fullName: '',
        address1: '',
        address2: '',
        licenseNumber: '',
        zip: '',
        dob: '',
        expiry: '',
        otherInfo: ''
      }
    };
  },

  watch: {
    swipedInput () {
      if (this.$data.swipedInput) {
        this.$data.state.processing = true;

        this.parseInput();
      }
    }
  },

  methods: {
    ...mapActions(['LICENSE__parse']),

    parseInput: debounce(function () {
      this.$data.swipedInput.replace(/\\/g, '');
      this.$data.swipedInput.replace(/"/g, '');

      this.LICENSE__parse({
        value: this.$data.swipedInput,
        callback: response => {
          this.$data.parsedInput.type = response.Type;
          this.$data.parsedInput.state = response.State;
          this.$data.parsedInput.city = response.City;
          this.$data.parsedInput.lastName = response.LastName;
          this.$data.parsedInput.firstName = response.FirstName;
          this.$data.parsedInput.middleName = response.MiddleName;
          this.$data.parsedInput.address1 = response.Address1;
          this.$data.parsedInput.address2 = response.Address2;
          this.$data.parsedInput.licenseNumber = response.LicenseNum;
          this.$data.parsedInput.zip = response.Zip;
          this.$data.parsedInput.dob = response.DOB;
          this.$data.parsedInput.expiry = response.Expiry;
          this.$data.parsedInput.otherInfo = response.OtherInfo;

          this.$data.parsedInput.fullName = [
            this.$data.parsedInput.firstName,
            this.$data.parsedInput.middleName,
            this.$data.parsedInput.lastName
          ].join(' ');

          this.$data.state.complete = true;

          setTimeout(() => {
            this.$data.state.complete = false;
          }, 3000);

          this.$emit('swipe', this.$data.parsedInput);

          if (this.$data.parsedInput.otherInfo) {
            this.$hub.$emit(EVENT_WARNING, this.$data.parsedInput.otherInfo);
          }
        },
        fail: () => {
          this.$data.state.error = true;

          setTimeout(() => {
            this.$data.state.error = false;
          }, 3000);
        },
        always: () => {
          this.$refs.input.blur();
          this.$data.state.processing = false;
        }
      });
    }, 600)
  }
};
</script>
