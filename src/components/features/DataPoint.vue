<template>
  <div class="data-point" @click="$emit('click')" :data-format="format">
    <div class="_label"><slot name="prefix"></slot> {{ label }} <slot name="affix"></slot></div>
    <div class="_value"><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: 'data-point',

  props: {
    label: { default: '' },
    format: { default: 'block' } // ['block', 'inline']
  }
};
</script>
