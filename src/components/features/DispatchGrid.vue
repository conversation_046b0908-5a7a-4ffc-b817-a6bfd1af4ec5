<script>
import is from 'is_js';
import Grid from './Grid.vue';

import {
  get,
  set,
  toLower
} from 'lodash';

export default {
  name: 'dispatch-grid',

  extends: Grid,

  template: '',

  methods: {
    cellClasses (record, column) {
      try {
        if (column.ID === 'tPriority') throw new Error(`priority-${record[column.ID]}`);

        if (column.ID === 'sStatus') throw new Error('status-' + toLower(record[column.ID]));

        if (['dETA_Assigned', 'dETA_Unassigned', 'lAppointmentETAMinutes'].includes(column.ID)) {
          let etaHighlightRange = get(this.USER__state.Profile, 'lHighlightETA', '');

          if (is.empty(etaHighlightRange)) {
            etaHighlightRange = this.TOPSCOMPANY__settings.lHighlightETA_Default;
          }

          etaHighlightRange = parseInt(etaHighlightRange);
          let etaValue = parseInt(record.lAppointmentETAMinutes) || 0;
          let etaPercent = (etaValue / etaHighlightRange) || 0;

          if (etaHighlightRange === 0) return;
          if (is.falsy(record[column.ID]) && record[column.ID] !== 0) return;

          if (etaValue > etaHighlightRange) throw new Error('eta-0');
          if (etaPercent < 0) throw new Error('eta-4');
          if (etaPercent < 0.33) throw new Error('eta-3');
          if (etaPercent < 0.66) throw new Error('eta-2');
          throw new Error('eta-1');
        }
      } catch (tag) {
        let tags = { '-cell-tag': true };

        set(tags, tag.message, true);

        return tags;
      }

      return;
    },

    rowClasses (record) {
      return {
        '-row': true,
        '-highlighted': this.isSelected(record),
        '-internet': this.isInternetCall(record)
      };
    },

    isInternetCall (record) {
      return record.lTowTypeKey === '0';
    }
  }
};
</script>

<style scoped>
:is(.priority-1,
.priority-2,
.priority-3,
.priority-4,
.priority-5,
.priority-6,
.priority-7,
.priority-8,
.status-dispatched,
.status-acknowledged,
.status-arrived,
.status-hooked,
.status-dropped,
.eta-1,
.eta-2,
.eta-3,
.eta-4) {
  font-weight: bold;
  color: var(--color);
  background: linear-gradient(135deg, var(--background-start), var(--background-end));
}

/* Priority */
.priority-1 {
  --color: color-mix(in oklch, var(--pure-white), var(--pure-red) 10%);
  --background-start: color-mix(in oklch, var(--pure-red), var(--pure-white) 10%);
  --background-end: var(--pure-red);
}

:is(.priority-2, .priority-3, .priority-4) {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-yellow) 50%);
  --background-start: color-mix(in oklch, var(--pure-yellow), var(--pure-white) 10%);
  --background-end: var(--pure-yellow);
}

:is(.priority-5, .priority-6) {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-lime) 50%);
  --background-start: var(--pure-lime);
  --background-end: color-mix(in oklch, var(--pure-lime), var(--pure-black) 10%);
}

:is(.priority-7, .priority-8) {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-gray) 50%);
  --background-start: color-mix(in oklch, var(--pure-gray), var(--pure-white) 10%);
  --background-end: var(--pure-gray);
}

/* Status */
.status-dispatched {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-gray) 50%);
  --background-start: color-mix(in oklch, var(--pure-gray), var(--pure-white) 40%);
  --background-end: var(--pure-gray);
}

.status-acknowledged {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-aqua) 50%);
  --background-start: color-mix(in oklch, var(--pure-aqua), var(--pure-white) 40%);
  --background-end: var(--pure-aqua);
}

.status-arrived {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-teal) 40%);
  --background-start: color-mix(in oklch, var(--pure-teal), var(--pure-white) 40%);
  --background-end: var(--pure-teal);
}

.status-hooked {
  --color: color-mix(in oklch, var(--pure-white), var(--pure-blue) 10%);
  --background-start: color-mix(in oklch, var(--pure-blue), var(--pure-white) 40%);
  --background-end: var(--pure-blue);
}

.status-dropped {
  --color: color-mix(in oklch, var(--pure-white), var(--pure-navy) 10%);
  --background-start: color-mix(in oklch, var(--pure-navy), var(--pure-white) 40%);
  --background-end: var(--pure-navy);
}

/* ETA */
.eta-1 {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-gray) 60%);
  --background-start: color-mix(in oklch, var(--pure-gray), var(--pure-white) 10%);
  --background-end: var(--pure-gray);
}

.eta-2 {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-lime) 60%);
  --background-start: var(--pure-lime);
  --background-end: color-mix(in oklch, var(--pure-lime), var(--pure-black) 10%);
}

.eta-3 {
  --color: color-mix(in oklch, var(--pure-black), var(--pure-yellow) 60%);
  --background-start: color-mix(in oklch, var(--pure-yellow), var(--pure-white) 10%);
  --background-end: var(--pure-yellow);
}

.eta-4 {
  --color: color-mix(in oklch, var(--pure-white), var(--pure-red) 10%);
  --background-start: color-mix(in oklch, var(--pure-red), var(--pure-white) 10%);
  --background-end: var(--pure-red);
}
</style>
