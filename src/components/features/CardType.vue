<template>
  <span class="card-type">{{ label }}</span>
</template>

<script>
export default {
  name: 'card-type',

  props: {
    id: { required: true }
  },

  data () {
    return {
      value: '',
      options: []
    };
  },

  computed: {
    label () {
      const cardType = this.options.find(option => option.Key === this.id);
      return cardType ? cardType.Value : '';
    }
  },

  methods: {
    async fetchOptions () {
      this.$store.dispatch('PAYMENT__getCreditCardTypes', {
        callback: response => {
          this.options = response;
        }
      });
    }
  },

  async mounted () {
    this.fetchOptions();
  }
};
</script>
