<template>
  <div :class="dropdownClasses">
    <div class="dropdown-trigger"
      @focus.capture="focus"
      @blur.capture="blur"
      tabindex="-1">
      <slot></slot>
    </div>
    <div class="dropdown-menu" id="dropdown-menu" role="menu">
      <transition :name="transitionName">
        <div v-show="isVisible" class="dropdown-content">
          <slot name="menu"></slot>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dropdown-control',

  props: {
    disabled: { type: Boolean, default: false },
    isUp: { type: Boolean, required: false, default: false },
    isRight: { type: Boolean, required: false, default: false },
    isHoverable: { type: Boolean, required: false, default: false }
  },

  data () {
    return {
      isVisible: false
    };
  },

  computed: {
    dropdownClasses () {
      return {
        'dropdown': true,
        'is-active': true,
        'is-hoverable': this.isHoverable,
        'is-right': this.isRight,
        'is-up': this.isUp
      };
    },

    transitionName () {
      return this.isUp ? 'drop-up' : 'drop-down';
    }
  },

  methods: {
    focus () {
      if (this.disabled) return;

      this.$data.isVisible = true;
    },

    blur () {
      this.$data.isVisible = false;
    }
  }
};
</script>
