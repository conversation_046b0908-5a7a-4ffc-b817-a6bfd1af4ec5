<template>
  <div class="form-section-controller" ref="controller">
    <slot></slot>
  </div>
</template>

<script>
import keyCodes from '@/utils/keycodes.js';

import {
  EVENT_SECTION_EXPANDED,
  EVENT_JUMP_BACK_SECTION,
  EVENT_JUMP_FORWARD_SECTION
} from '@/config.js';

import {
  get,
  forEach,
  indexOf,
  throttle
} from 'lodash';

export default {
  name: 'form-section-controller',

  data () {
    return {
      guide: {},
      sectionKeys: [],
      focusedSectionKey: ''
    };
  },

  methods: {
    focusSection: throttle(function ({ sectionKey, notifyHub = true }) {
      this.focusedSectionKey = sectionKey;

      this.updateGuide();

      if (notifyHub) {
        this.$hub.$emit(EVENT_SECTION_EXPANDED, sectionKey);
      }
    }, 50),

    initializeSectionKeys () {
      let $sections = this.$refs.controller.querySelectorAll('.form-section');

      forEach($sections, section => {
        let sectionId = get(section, 'id', null);

        if (!sectionId) return;

        this.sectionKeys.push(sectionId);
      });
    },

    updateGuide () {
      let focusedIndex = indexOf(this.sectionKeys, this.focusedSectionKey);

      if (focusedIndex < 0) return;

      let previousIndex = focusedIndex === 0
        ? this.sectionKeys.length - 1
        : focusedIndex - 1;

      let nextIndex = focusedIndex === (this.sectionKeys.length - 1)
        ? 0
        : focusedIndex + 1;

      this.guide = {
        previousKey: this.sectionKeys[previousIndex],
        focusedKey: this.focusedSectionKey,
        nextKey: this.sectionKeys[nextIndex]
      };
    }
  },

  mounted () {
    this.initializeSectionKeys();
    this.updateGuide();

    this.$hub.$on(EVENT_SECTION_EXPANDED, sectionKey => {
      this.focusSection({ sectionKey: sectionKey, notifyHub: false });
    });

    this.$hub.$on(EVENT_JUMP_BACK_SECTION, () => {
      this.focusSection({ sectionKey: this.guide.previousKey });
    });

    this.$hub.$on(EVENT_JUMP_FORWARD_SECTION, () => {
      this.focusSection({ sectionKey: this.guide.nextKey });
    });
  }
};
</script>
