<template>
  <label class="keep-option">
    <input type="checkbox" v-model="$value" />
    <div>
      <slot></slot>
    </div>
  </label>
</template>

<script>
export default {
  name: 'KeepOption',

  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  }
};
</script>

<style scoped>
.keep-option {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.5rem;

  padding: 0.25rem 0.5rem;
  width: 100%;
  border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.5rem;
}
</style>
