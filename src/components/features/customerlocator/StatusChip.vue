<template>
  <div class="status-chip is-small">
    <div class="icon" :data-variant="variant"></div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'StatusChip',

  props: {
    variant: {
      type: String, // success, danger, warning, info
      required: true
    }
  }
};
</script>

<style scoped>
.status-chip {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.5rem;
  align-items: center;
}

.icon {
  display: inline-block;

  width: 0.75em;
  height: 0.75em;
  border-radius: 50%;

  &[data-variant="success"] {
    background: hsla(var(--pure-lime-h), var(--pure-lime-s), var(--pure-lime-l), 1);
  }

  &[data-variant="danger"] {
    background: hsla(var(--pure-red-h), var(--pure-red-s), var(--pure-red-l), 1);
  }

  &[data-variant="warning"] {
    background: hsla(var(--pure-orange-h), var(--pure-orange-s), var(--pure-orange-l), 1);
  }

  &[data-variant="info"] {
    background: hsla(var(--pure-aqua-h), var(--pure-aqua-s), var(--pure-aqua-l), 1);
  }
}
</style>
