<template>
  <div>
    <button id="customer-locator-trigger"
      popovertarget="customer-locator-popover"
      class="button"
      tabindex="-1"
      title="Locate customer by mobile device.">
      <i class="far fa-location-crosshairs"></i>
    </button>

    <div id="customer-locator-popover"
      anchor="customer-locator-trigger"
      popover
      @toggle="onPopoverToggle">

      <RequestPane
        v-if="requestState === 'idle'"
        v-model="callerPhone"
        :subcompany-key="subcompanyKey"
        :call-key="callKey"
        @on-get-position="requestState = 'requesting'" />

      <RadarPane
        v-if="requestState === 'requesting'"
        :phone="callerPhone"
        :subcompany-key="subcompanyKey"
        @on-restart="restart({ reset: false })"
        @on-start-get-position=""
        @on-did-get-position="setPosition" />

      <ResponsePane
        v-if="requestState === 'success'"
        :phone="callerPhone"
        :position="position"
        :location="location"
        :keepPhone.sync="keepPhone"
        :keepLocation.sync="keepLocation"
        :keepPosition.sync="keepPosition"
        @on-keep="onKeep"
        @on-restart="restart({ reset: true })" />

    </div>
  </div>
</template>

<script>
import RequestPane from './RequestPane.vue';
import RadarPane from './RadarPane.vue';
import ResponsePane from './ResponsePane.vue';

export default {
  name: 'CustomerLocator',

  props: {
    phone: {
      type: String,
      default: ''
    },
    subcompanyKey: {
      type: [String, Number],
      default: ''
    },
    callKey: {
      type: [String, Number],
      default: ''
    }
  },

  components: {
    RequestPane,
    RadarPane,
    ResponsePane
  },

  data () {
    return {
      requestState: 'idle', // idle, requesting, success, error
      keepPhone: true,
      keepPosition: true,
      keepLocation: true,

      callerPhone: '',

      position: {
        latitude: null,
        longitude: null,
        accuracy: ''
      },

      location: {
        address: '',
        isApproximate: false
      }
    };
  },

  methods: {
    onPopoverToggle (event) {
      if (event.newState === 'open') {
        if (!this.callerPhone) {
          this.callerPhone = this.phone;
        }
      } else {
        this.restart({ reset: true });
      }
    },

    async setPosition ({ position, location }) {
      this.position.latitude = position.Latitude;
      this.position.longitude = position.Longitude;
      this.position.accuracy = position.Accuracy;

      this.location.address = location.Location;
      this.location.isApproximate = location.Approximation;

      this.requestState = 'success';
    },

    onKeep () {
      if (this.keepPhone) {
        this.$emit('on-keep-phone', this.callerPhone);
      }

      if (this.keepPosition) {
        this.$emit('on-keep-position', this.position);
      }

      if (this.keepLocation) {
        this.$emit('on-keep-location', this.location);
      }

      this.restart({ reset: true });
      document.querySelector('#customer-locator-popover').hidePopover();
    },

    restart ({ reset = true }) {
      if (reset) {
        this.reset();
      }

      this.requestState = 'idle';
    },

    reset () {
      this.keepPhone = true;
      this.keepPosition = true;
      this.keepLocation = true;

      this.position = {
        latitude: null,
        longitude: null,
        accuracy: ''
      };

      this.location = {
        address: '',
        isApproximate: false
      };
    }
  }
};
</script>

<style scoped>
#customer-locator-popover {
  &:popover-open {
    display: grid;
    place-content: center;
  }

  padding: 1rem;
  width: 20rem;
  height: 20rem;
  aspect-ratio: 1 / 1;
  background-color: white;
  border: 0.1rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 1rem;
  box-shadow: var(--box-shadow-200);
  z-index: 10;
}

.interrupt-button {
  position: absolute;
  top: 1rem;
  left: 1rem;
}
</style>
