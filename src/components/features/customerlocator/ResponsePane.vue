<template>
  <section class="response-pane">
    <KeepOption v-model="$keepPhone">
      {{ phone }}
    </KeepOption>

    <KeepOption v-model="$keepLocation">
      <StatusChip variant="warning" v-if="location.isApproximate">Approximate</StatusChip>
      <div>{{ location.address }}</div>
    </KeepOption>

    <KeepOption v-model="$keepPosition">
      <StatusChip variant="warning" v-if="position.accuracy !== 'High'">{{ position.accuracy }} accuracy</StatusChip>
      <div>{{ position.latitude }}, {{ position.longitude }}</div>
    </KeepOption>

    <div class="v-space-05"></div>

    <!-- // Todo: $store.state.topsCompany.settings.bAllowCustomerUpdate === '1' -->
    <ActionButton variant="blue" @click="$emit('on-keep')">
      Keep selected
    </ActionButton>
    <ActionButton class="interrupt-button" @click="$emit('on-restart')">
      <i class="far fa-arrow-rotate-left"></i>
    </ActionButton>
  </section>
</template>

<script>
import ActionButton from '@/components/tower/liens/inputs/Button.vue';
import KeepOption from './KeepOption.vue';
import StatusChip from './StatusChip.vue';

export default {
  name: 'ResponsePane',

  props: {
    phone: {
      type: String,
      default: ''
    },
    keepPhone: {
      type: Boolean,
      default: true
    },
    keepLocation: {
      type: Boolean,
      default: true
    },
    keepPosition: {
      type: Boolean,
      default: true
    },
    location: {
      type: Object,
      default: () => ({})
    },
    position: {
      type: Object,
      default: () => ({})
    }
  },

  components: {
    ActionButton,
    KeepOption,
    StatusChip
  },

  computed: {
    $keepPhone: {
      get () {
        return this.keepPhone;
      },
      set (value) {
        this.$emit('update:keepPhone', value);
      }
    },

    $keepPosition: {
      get () {
        return this.keepPosition;
      },
      set (value) {
        this.$emit('update:keepPosition', value);
      }
    },

    $keepLocation: {
      get () {
        return this.keepLocation;
      },
      set (value) {
        this.$emit('update:keepLocation', value);
      }
    }
  }
};
</script>

<style scoped>
.response-pane {
  display: grid;
  gap: 0.5rem;
  justify-content: stretch;
}

.interrupt-button {
  position: absolute;
  top: 1rem;
  left: 1rem;
}
</style>
