<template>
  <section class="radar-pane">
    <svg class="radar" clip-rule="evenodd" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="1.5" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <radialGradient id="a" cx="75" cy="75" gradientUnits="userSpaceOnUse" r="75">
        <stop offset="0" stop-color="#5b96c1"/>
        <stop offset=".64" stop-color="#5b96c1" stop-opacity=".847059"/>
        <stop offset=".83" stop-color="#5b96c1" stop-opacity=".454902"/>
        <stop offset="1" stop-color="#5b96c1" stop-opacity="0"/>
      </radialGradient>
      <clipPath id="b">
        <path clip-rule="evenodd" d="m0 0h150v150h-150z"/>
      </clipPath>
      <path d="m0 0h150v150h-150z" fill="none"/>
      <g clip-path="url(#b)">
        <circle cx="75" cy="75" fill="#5bc169" r="75"/>
        <path d="m75 24.212c28.031 0 50.788 22.757 50.788 50.788s-22.757 50.788-50.788 50.788-50.788-22.757-50.788-50.788 22.757-50.788 50.788-50.788zm0 2c-26.927 0-48.788 21.861-48.788 48.788s21.861 48.788 48.788 48.788 48.788-21.861 48.788-48.788-21.861-48.788-48.788-48.788z" fill="#93e89e"/>
        <path id="orientation" d="m0 75c0-41.394 33.606-75 75-75v75z" fill="url(#a)"/>
        <circle cx="75" cy="75" fill="#5b96c1" r="25"/>
        <path d="m75 50c13.798 0 25 11.202 25 25s-11.202 25-25 25-25-11.202-25-25 11.202-25 25-25zm0 6c-10.486 0-19 8.514-19 19s8.514 19 19 19 19-8.514 19-19-8.514-19-19-19z" fill="#fff"/>
      </g>
    </svg>

    <ActionButton class="interrupt-button" @click="$emit('on-restart')">
      <i class="far fa-xmark"></i>
    </ActionButton>
  </section>
</template>

<script>
import ActionButton from '@/components/tower/liens/inputs/Button.vue';

export default {
  name: 'RadarPane',

  components: {
    ActionButton
  },

  props: {
    phone: {
      type: String,
      required: true
    },
    subcompanyKey: {
      type: [String, Number],
      default: ''
    }
  },

  methods: {
    async getPosition () {
      this.$emit('on-start-get-position');

      const position = await this.requestPosition();
      const location = await this.coordinatesToLocation(position.Latitude, position.Longitude);

      this.$emit('on-did-get-position', { position, location });
    },

    requestPosition () {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('TOWSTATUS__requestLocation', {
          companyKey: this.$store.state.orgUnitKey,
          subcompanyKey: this.subcompanyKey,
          phone: this.phone,
          success: async response => {
            if ('Key' in response) {
              resolve(await this.getRequestedPosition(response.Key));
            } else {
              reject(new Error('Failed to request location'));
            }
          },
          fail: error => {
            reject(error);
          }
        });
      });
    },

    async getRequestedPosition (requestKey) {
      let isInterrupted = false;

      const _getRequestedPosition = (requestKey) => {
        return new Promise((resolve, reject) => {
          this.$on('on-restart', () => {
            isInterrupted = true;
          });

          this.$store.dispatch('TOWSTATUS__getRequestedLocation', {
            requestKey,
            success: response => {
              const latitude = this.$_.get(response, 'Latitude', null);
              const longitude = this.$_.get(response, 'Longitude', null);

              if (latitude && longitude) {
                resolve(response);
              } else {
                if (!isInterrupted) {
                  setTimeout(() => {
                    _getRequestedPosition(requestKey).then(resolve).catch(reject);
                  }, 5000);
                }
              }
            }
          });
        });
      };

      return await _getRequestedPosition(requestKey);
    },

    coordinatesToLocation (latitude, longitude) {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('MAP__coordinatesToAddress', {
          latitude,
          longitude,
          success: response => {
            resolve(response);
          }
        });
      });
    }
  },

  mounted () {
    this.getPosition();
  },

  beforeDestroy () {
    this.$off('on-restart');
  }
};
</script>

<style scoped>
.radar-pane {
  .radar {
    width: 10rem;
    height: 10rem;
    transform: rotate(45deg);

    #orientation {
      transform-origin: center;
      animation: rotate 6s infinite ease-in-out;
    }

    @keyframes rotate {
      100% {
        transform: rotate(360deg);
      }
    }
  }
}

.interrupt-button {
  position: absolute;
  top: 1rem;
  left: 1rem;
}
</style>
