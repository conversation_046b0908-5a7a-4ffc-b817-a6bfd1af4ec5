<template>
  <div id="actions">
    <app-modal
      :title="modalTitle"
      @close="close"
      @reset="reset"
      :pad="false"
      :show="smartShow">

      <div v-if="isLoading" class="loading-overlay">
        <i class="fas fa-spinner-third fa-spin loading-icon"></i>
        <p>Loading actions...</p>
      </div>

      <div class="sections" :class="{ 'is-loading': isLoading }">
        <div class="index-section">
          <ul class="_liner">
            <li class="index__item"
              v-for="action in actions"
              :key="action.vueKey"
              @click="lookAt(slugify(action.Display))">
              {{ action.Display }}
            </li>
          </ul>
        </div>

        <ul class="actions-section">
          <li class="action"
            v-for="(action, index) in actions"
            :id="slugify(action.Display)"
            :data-index="index"
            :key="action.vueKey">

            <div slot="header" class="action__header">
              <span class="action__name">{{ action.Display }}</span>

              <app-button
                @click="confirmHandleAction(action)"
                type="primary"
                :disabled="handleActionDisabled(action)"
                v-if="!getVisibleItems(action).length && !actionIs(action, 'auctionconditionreports')">
                <i class="fas fa-arrow-right"></i>
              </app-button>

              <div v-if="!getVisibleItems(action).length && actionIs(action, 'auctionconditionreports')">
                <app-button
                  @click="getAuctionConditionReport"
                  type="primary"
                  :disabled="conditionReport.isLoading"
                  v-if="!conditionReport.isLinksVisible">

                  <i class="fas fa-arrow-right" v-if="!conditionReport.isLoading"></i>
                  <i class="fas fa-spinner-third fa-spin" v-if="conditionReport.isLoading"></i>

                </app-button>

                <app-button
                  @click="openLink(conditionReport.urlEdit)"
                  type="primary"
                  :disabled="!$_.get(conditionReport, 'urlEdit', null)"
                  v-if="conditionReport.isLinksVisible">
                  <i class="fas fa-pencil"></i>
                </app-button>

                <app-button
                  @click="openLink(conditionReport.urlView)"
                  type="primary"
                  :disabled="!$_.get(conditionReport, 'urlView', null)"
                  v-if="conditionReport.isLinksVisible">
                  <i class="fas fa-eye"></i>
                </app-button>
              </div>
            </div>

            <div class="action__body" v-if="getVisibleItems(action).length">
              <div v-if="lastDispatchTipVisible(action)" class="columns">
                <div class="column is-one-third"></div>
                <div class="column">
                  <AppTip>
                    <p class="small">This appears to be the last (or only) dispatch on this call. Please complete these final&nbsp;details.</p>
                  </AppTip>
                </div>
              </div>

              <div v-if="actionIs(action, 'release')" class="columns">
                <div class="_card-swipe column is-two-thirds is-offset-one-third">
                  <card-swipe @swipe="processSwipe"></card-swipe>
                  <div class="_or">Or&nbsp;enter</div>
                </div>
              </div>

              <div v-if="actionIs(action, 'lotpass')" class="columns">
                <div class="_card-swipe column is-two-thirds is-offset-one-third">
                  <card-swipe @swipe="processSwipe"></card-swipe>
                  <div class="_or">Or&nbsp;enter</div>
                </div>
              </div>

              <div v-if="actionIs(action, ['assign', 'dispatchadditional'])" class="dispatch-units-control">
                <DispatchUnitsController
                  @on-driver-selected="setField('DriverNum', 'ResponseValue', $event.Code)"
                  @on-truck-selected="setField('TruckNum', 'ResponseValue', $event.Number)" />
              </div>

              <template v-else>
                <div v-for="item in getVisibleItems(action)" :key="item.vueKey">
                  <div v-if="actionIs(action, ['undo'])" class="columns">
                    <div class="column is-one-third"></div>
                    <div class="column">
                      <AppTip>
                        <i class="fas fa-triangle-exclamation pure-orange" slot="icon"></i>
                        <p class="small">This call has been reconciled or has payments associated with it. If you proceed, all reconciliations and the tow ticket will be&nbsp;deleted.</p>
                      </AppTip>
                    </div>
                  </div>

                  <div class="columns item">
                    <div class="column _label is-one-third">
                      <label><span v-if="item.Required == 'true'" class="required-indicator"><i class="fas fa-circle-small"></i></span>{{ item.Display }}</label>
                    </div>

                    <!-- TowTicketNum -->
                    <div v-if="item.ResponseTag === 'TowTicketNum'" class="tow-ticket-container">
                      <component
                        :is="makeControl(item)"
                        :id="item.ResponseTag"
                        :options="transformedOptions(item.Options)"
                        v-model="item.ResponseValue"
                        :disabled="useCallNumber"
                        @change="change($event, action)">
                      </component>
                      <label class="_use-call-number" v-if="useCallNumberSwitchVisible">
                        <input type="checkbox" v-model="useCallNumber" />
                        <span class="_label">Use Call #</span>
                      </label>
                    </div>

                    <div v-else class="item__control column">
                      <component
                        v-model="item.ResponseValue"
                        :is="makeControl(item)"
                        :id="item.ResponseTag"
                        :options="transformedOptions(item.Options)"
                        :disabled="!canEditField(item)"
                        :is-range="false"
                        :subterminalKey="subterminalKey"
                        @change="change($event, action)">
                      </component>
                    </div>
                  </div>
                </div>
              </template>
            </div>

            <div class="action__footer" v-if="getVisibleItems(action).length || isAssignMapVisible(action) || isAddPaymentVisible(action)">
              <app-button @click="gotoAssignMap" v-if="isAssignMapVisible(action)" type="default">View Map</app-button>
              <app-button @click="gotoPayments" v-if="isAddPaymentVisible(action)" type="default">Add Payment</app-button>
              <div v-else></div>

              <app-button
                @click="confirmHandleAction(action)"
                type="primary"
                :disabled="handleActionDisabled(action)"
                v-if="getVisibleItems(action).length">
                <i class="fas fa-arrow-right"></i>
              </app-button>
            </div>
          </li>
          <li class="end-of-actions">
            <i class="fas fa-circle"></i>
          </li>
        </ul>
      </div>

      <span v-show="false">{{ updated_at }}</span>
    </app-modal>

    <invoice-preview
      :callKey="callKey"
      v-if="invoicePreview.isVisible"
      @close="invoicePreview.isVisible = false"
      @sent="onInvoiceSent">
    </invoice-preview>

    <tow-pay
      :callKey="callKey"
      :isTowPayment="towPay.isTowPayment"
      v-if="towPay.isVisible"
      @close="towPay.isVisible = false">
    </tow-pay>

    <release-vehicle
      :callKey="callKey"
      :subterminalKey="subterminalKey"
      v-if="releaseVehicle.isModalVisible"
      @close="releaseVehicle.isModalVisible = false">
    </release-vehicle>

  </div>
</template>

<script>
import is from 'is_js';
import numeral from 'numeral';
import Access from '@/utils/access.js';
import { throttle } from 'lodash';
import { mapGetters, mapActions } from 'vuex';

import TowPay from '@/components/features/TowPay.vue';
import CardSwipe from '@/components/features/CardSwipe.vue';
import ReleaseVehicle from '@/components/features/ReleaseVehicle.vue';
import InvoicePreview from '@/components/features/InvoicePreview.vue';
import DispatchUnitsController from '@/components/dispatchunits/Controller.vue';

import {
  EVENT_WARNING,
  EVENT_OPEN_PAYMENTS,
} from '@/config.js';

export default {
  name: 'actions',

  props: {
    mileageRequired: { default: null },
    callKey: { type: [Number, String], required: false },
    subterminalKey: { type: [Number, String], required: false },
    dispatchKey: { type: [Number, String], required: false, default: '' },
    datesVisible: { type: Boolean, required: false, default: true },
    show: { type: [String, Boolean], required: true, default: false },
    mutations: { type: [Object, Array], required: false, default: () => {} }
  },

  components: {
    TowPay,
    CardSwipe,
    ReleaseVehicle,
    InvoicePreview,
    DispatchUnitsController
  },

  data () {
    return {
      actionPool: {},
      updated_at: null,
      dispatchedUnit: null,
      useCallNumber: false,
      isLoading: false,

      conditionReport: {
        isLinksVisible: false,
        isLoading: false,
        urlEdit: '',
        urlView: ''
      },

      invoicePreview: {
        isVisible: false
      },

      towPay: {
        isVisible: false,
        isTowPayment: true
      },

      releaseVehicle: {
        isModalVisible: false
      },

      backfillableFields: [
        { order: 1, name: 'Dispatched' },
        { order: 2, name: 'Acknowledged' },
        { order: 3, name: 'Arrived' },
        { order: 4, name: 'Hooked' },
        { order: 5, name: 'Dropped' },
        { order: 6, name: 'Completed' }
      ]
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'TOPSCOMPANY__settings'
    ]),

    smartShow () {
      return this.show &&
        !!this.actions.length &&
        !this.invoicePreview.isVisible &&
        !this.towPay.isVisible;
    },

    actions: {
      get () {
        return this.actionPool.Actions || [];
      },
      set (actions) {
        this.actionPool.Actions = actions;
      }
    },

    modalTitle () {
      return `Actions for Call #${this.callKey}`;
    },

    useCallNumberSwitchVisible () {
      return this.hasField('InvoiceNumEqualCallNum');
    },

    mileageRequiredProxy () {
      return this.$_.isBoolean(this.mileageRequired)
        ? this.mileageRequired
        : this.actionPool.bMileageRequired;
    }
  },

  watch: {
    show () {
      this.getActions();
    },

    useCallNumber () {
      let useCallNumber = this.useCallNumber ? 'Yes' : 'No';

      this.setFieldValue('InvoiceNumEqualCallNum', useCallNumber);

      if (useCallNumber) this.setFieldValue('TowTicketNum', '');
    }
  },

  methods: {
    ...mapActions([
      '__getNow',
      'CALL__getActions',
      'CALL__showReceipt',
      'CALL__handleAction',
      'MAP__getCoordinates',
      'TOPSCALL__getDriverTruckToAssign',
      'TOPSCALL__getConditionReportLinks',
      'NOTIFICATION__getDispatchableDriverTruck'
    ]),

    resetConditionReport () {
      this.conditionReport.isLinksVisible = false;
      this.conditionReport.isLoading = false;
      this.conditionReport.urlEdit = '';
      this.conditionReport.urlView = '';
    },

    getVisibleItems (action) {
      if (this.actionIs(action, 'release')) { return []; }

      return this.$_.filter(action.AdditionalItems, item => item.IsVisible);
    },

    getAuctionConditionReport () {
      this.conditionReport.isLoading = true;

      this.TOPSCALL__getConditionReportLinks({
        callKey: this.callKey,
        success: response => {
          this.conditionReport = response;
          this.conditionReport.isLinksVisible = true;
          this.conditionReport.isLoading = false;
        },
        fail: error => {
          this.conditionReport.isLoading = false;
          console.error('Failed to get condition report links:', error);
          this.$hub.$emit(EVENT_WARNING, 'Failed to load condition report. Please try again.');
        }
      });
    },

    forceUpdate () {
      // Make Vue see recent change so that it is visible to the user.
      // Be sure to leave the updated_at property in the template.
      this.$set(this, 'updated_at', new Date());
    },

    getActions () {
      if (!this.show) return;
      if (!this.callKey) return;

      this.resetConditionReport();

      this.isLoading = true;

      this.CALL__getActions({
        callKey: this.callKey,
        dispatchKey: this.dispatchKey,
        callback: response => {
          try {
            this.actionPool = this.injectVueKeys(response);

            this.$nextTick(() => {
              this.setDefaultAttributes();
              this.applyMutations();
              this.applyDataFormats();
              this.setUseCallNumber();
              this.isLoading = false;
            });
          } catch (error) {
            console.error('Error processing actions:', error);
            this.$hub.$emit(EVENT_WARNING, 'Error loading actions. Please try again.');
            this.isLoading = false;
          }
        },
        error: error => {
          console.error('Error fetching actions:', error);
          this.$hub.$emit(EVENT_WARNING, 'Failed to load actions. Please try again.');
          this.isLoading = false;
        }
      });
    },

    handleActionDisabled (action) {
      if (this.isLoading) {
        return true;
      }

      const requiredFieldsMissing = this.validateRequiredFields(action);
      return requiredFieldsMissing.length > 0;
    },

    applyDataFormats () {
      this.actionPool.bMileageRequired = this.actionPool.bMileageRequired === 'true';

      // Convert boolean string values in AdditionalItems
      if (this.actions && this.actions.length) {
        this.actions.forEach(action => {
          if (action.AdditionalItems && action.AdditionalItems.length) {
            action.AdditionalItems.forEach(item => {
              if (item.Required === 'true' || item.Required === 'false') {
                this.$set(item, 'Required', item.Required === 'true');
              }

              if (item.IsVisible === 'true' || item.IsVisible === 'false') {
                this.$set(item, 'IsVisible', item.IsVisible === 'true');
              }
            });
          }
        });
      }

      if (this.hasField('BalanceDue')) {
        this.setFieldValue('BalanceDue', numeral(this.getFieldValue('BalanceDue')).format('0.00'));
      }

      if (this.hasField('Assigned')) {
        this.setField('Assigned', 'disabled', true);
      }
    },

    applyMutations () {
      if (this.$_.isEmpty(this.mutations)) return;

      this.$_.forEach(this.$_.castArray(this.mutations), mutation => {
        let targetAction = this.$_.find(this.actions, ['ResponseValue', mutation.action]);

        if (this.$_.isNil(targetAction)) return;

        let targetItem = this.$_.find(targetAction.AdditionalItems, ['ResponseTag', mutation.item]);

        if (this.$_.isNil(targetItem)) return;

        this.$set(targetItem, 'ResponseValue', mutation.value);
      });
    },

    injectVueKeys (response) {
      this.$_.forEach(response.Actions, action => {
        this.$_.set(action, 'vueKey', this.$_.uniqueId());

        this.$_.forEach(action.AdditionalItems, item => {
          this.$_.set(item, 'vueKey', this.$_.uniqueId());
        });
      });

      return response;
    },

    makeControl (item) {
      const options = this.$_.get(item, 'Options', []);
      const value = this.$_.get(item, 'ValueType', '');

      if (!this.$_.isEmpty(options)) return 'app-select';
      if (item.ResponseTag === 'DriverNum') return 'app-select';

      if (item.ResponseTag === 'Location') {
        return 'app-address-suggestor';
      }

      if (item.ResponseTag === 'Destination') {
        return 'app-address-suggestor';
      }

      switch (value) {
        case 'Date':
        case 'DateTime':
          return 'app-date-time';
        case 'Float':
        case 'String':
        case 'Boolean':
        case 'Integer':
        case 'Currency':
        default:
          return 'app-text';
      }
    },

    getFieldValue (field, action = null) {
      let target = this.getField(field, action);

      return this.$_.get(target, 'ResponseValue', null);
    },

    // This is not meant to be used directly, but extended by other setter methods.
    _setField ({ action, field, property, value, force }) {
      const actions = action ? [action] : this.actions;

      if (this.$_.isEmpty(actions)) return;

      this.$_.forEach(actions, action => {
        let shouldUpdate = false;
        let target = this.$_.find(action.AdditionalItems, ['ResponseTag', field]);

        if (this.$_.isNil(target)) return;

        if (force) {
          shouldUpdate = true;
        } else if (this.$_.isEmpty(this.getFieldValue(target.ResponseTag, action))) {
          shouldUpdate = true;
        }

        if (shouldUpdate) {
          this.$set(target, property, value);
          this.forceUpdate();
        }
      });

      return true;
    },

    setField (field, property, value, action = null) {
      this._setField({
        action: action,
        field: field,
        property: property,
        value: value,
        force: true
      });
    },

    weakSetField (field, property, value, action = null) {
      this._setField({
        action: action,
        field: field,
        property: property,
        value: value,
        force: false
      });
    },

    setFieldValue (field, value, action = null) {
      this._setField({
        action: action,
        field: field,
        property: 'ResponseValue',
        value: value,
        force: true
      });
    },

    weakSetFieldValue (field, value, action = null) {
      this._setField({
        action: action,
        field: field,
        property: 'ResponseValue',
        value: value,
        force: false
      });
    },

    getField (field, action = null) {
      let targetAction = action ? [action] : this.actions;
      let targetField = null;

      this.$_.forEach(targetAction, action => {
        targetField = this.$_.find(action.AdditionalItems, ['ResponseTag', field]);

        if (!this.$_.isNil(targetField)) return false;
      });

      return targetField;
    },

    hasField (field) {
      return !this.$_.isNil(this.getField(field));
    },

    setUseCallNumber () {
      if (!this.hasField('InvoiceNumEqualCallNum')) return;

      this.useCallNumber = this.getFieldValue('InvoiceNumEqualCallNum') === 'Yes';
    },

    setDefaultAttributes () {
      this.$_.forEach(this.actions, action => {
        this.$_.forEach(action.AdditionalItems, item => {
          let isVisible = true;

          // 'RemoveLienPricing' Re-enabled for the "Terminate Lien" action
          if (['InvoiceNumEqualCallNum'].includes(item.ResponseTag)) {
            isVisible = false;
          }

          if (['Assigned', 'Dispatched', 'Acknowledged', 'Arrived', 'Hooked', 'Dropped', 'Completed'].includes(item.ResponseTag)) {
            isVisible = !this.isFinalDispatch();
          }

          if (['Mileage', 'TowTicketNum', 'CompletionState', 'LotKey', 'PrintReceipt'].includes(item.ResponseTag)) {
            isVisible = this.isFinalDispatch();
          }

          this.$_.set(item, 'IsVisible', isVisible);

          if (item.ResponseTag === 'RemoveLienPricing') {
            this.$_.set(item, 'ResponseValue', 'Yes');
          }
        });
      });
    },

    close () {
      this.$emit('close');
    },

    confirmHandleAction(action) {
      // Validate required fields before proceeding
      const requiredFieldsMissing = this.validateRequiredFields(action);
      if (requiredFieldsMissing.length > 0) {
        const fieldNames = requiredFieldsMissing.join(', ');
        this.$hub.$emit(EVENT_WARNING, `Required fields missing: ${fieldNames}`);
        return;
      }

      if (this.$_.get(action, 'ConfirmMessage', '').length) {
        this.$confirm(action.ConfirmMessage, action.Description, {
          confirmButtonText: action.Display,
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          this.handleAction(action);
        }).catch(() => {
          return;
        });
      } else if (this.actionIs(action, 'previewinvoice')) {
        this.invoicePreview.isVisible = true;
      } else {
        this.handleAction(action);
      }
    },

    validateRequiredFields (action) {
      const missingFields = [];

      if (!action || !action.AdditionalItems) {
        return missingFields;
      }

      this.$_.forEach(action.AdditionalItems, item => {
        if (item.Required === 'true' &&
            item.IsVisible &&
            this.$_.isEmpty(item.ResponseValue)) {
          missingFields.push(item.Display);
        }
      });

      return missingFields;
    },

    onInvoiceSent () {
      this.invoicePreview.isVisible = false;
      this.$emit('close');
    },

    getDispatchedUnit (action) {
      let responseValue = this.$_.get(action, 'ResponseValue', '');

      if (!responseValue) return;

      let fragments = responseValue.split('_');
      let dispatchKey = fragments[1];

      if (!dispatchKey) return;

      return new Promise((resolve, reject) => {
        this.NOTIFICATION__getDispatchableDriverTruck({
          dispatchKey: dispatchKey,
          callback: response => {
            resolve({
              driverKey: response.EmployeeKey,
              truckKey: response.TruckKey
            });
          }
        });
      });
    },

    openLink (link) {
      if (!link) return;

      window.open(link, '_blank');

      this.resetConditionReport();
    },

    async handleAction (action) {
      let parameters = {
        CallKey: this.callKey,
        DispatchKey: this.dispatchKey,
        ResponseTag: action.ResponseTag,
        ResponseValue: action.ResponseValue
      };

      if (!this.$_.isEmpty(this.getFieldValue('Completed')) &&
        this.mileageRequiredProxy &&
        this.$_.isEmpty(this.getFieldValue('Mileage'))) {
        this.$hub.$emit(EVENT_WARNING, 'Mileage is required.');
        return;
      }

      /**
       * This has to be done prior to CALL_handleAction to
       * make the driver and truck number available to the Notify
       * component. The dispatch record will already be deleted.
       */
      if (this.actionIs(action, 'dispatchunassign')) {
        this.dispatchedUnit = await this.getDispatchedUnit(action);
      }

      if (this.actionIs(action, 'towcreditcardpayment')) {
        this.towPay.isVisible = true;
        this.towPay.isTowPayment = true;
        return;
      }

      if (this.actionIs(action, 'salecreditcardpayment')) {
        this.towPay.isVisible = true;
        this.towPay.isTowPayment = false;
        return;
      }

      if (this.actionIs(action, 'release')) {
        this.releaseVehicle.isModalVisible = true;
        return;
      }

      this.$_.forEach(action.AdditionalItems, item => {
        this.$_.set(parameters, item.ResponseTag, item.ResponseValue);
      });

      this.CALL__handleAction({
        lastRead: this.actionPool.Now,
        data: parameters,
        callback: response => {
          let reportLink = {
            path: import.meta.env.VITE_TXI_API,
            parameters: this.$_.get(response, 'ReportLink.ReportLinkParams', null),
            apiVersion: Number(this.$_.get(response, 'ReportLink.ReportLinkAPIVersion', null))
          };

          if (reportLink.apiVersion === 1) {
            reportLink.path = import.meta.env.VITE_TXI_API_V1;
          }

          if (reportLink.parameters) {
            window.open(reportLink.path + '?' + reportLink.parameters);
          }

          let shouldNotify = Number(this.TOPSCOMPANY__settings.bSendDispatchPage) === 1;

          if (this.isAssignMapVisible(action) && shouldNotify) {
            this.$emit('notify', {
              callKey: response.CallKey,
              dispatchKey: response.DispatchKey,
              reason: 'assigned'
            });
          } else if (this.actionIs(action, 'dispatchunassign') && shouldNotify) {
            this.$emit('notify', {
              callKey: response.CallKey,
              dispatchKey: response.DispatchKey,
              dispatchDriverKey: this.$_.get(this.dispatchedUnit, 'driverKey', ''),
              dispatchTruckKey: this.$_.get(this.dispatchedUnit, 'truckKey', ''),
              dispatchEmployeeKey: this.$_.get(this.dispatchedUnit, 'employeeKey', ''),
              reason: 'unassigned'
            });
          } else {
            this.$emit('close');
          }

          this.$nextTick(() => {
            this.reset();
          });
        },
        error: error => {
          const errorMessage = this.$_.get(error, 'message', 'An error occurred while processing this action.');
          this.$hub.$emit(EVENT_WARNING, errorMessage);
        }
      });
    },

    isAssignMapVisible (action) {
      return ['assign', 'dispatchadditional'].includes(action.ResponseValue.toLowerCase());
    },

    isAddPaymentVisible (action) {
      if (this.$_.toLower(action.ResponseValue) !== 'release') return false;

      let targetItem = this.$_.find(action.AdditionalItems, ['ResponseTag', 'BalanceDue']);

      return !this.$_.isNil(targetItem);
    },

    transformedOptions (options = []) {
      return this.$_.map(options, option => {
        return {
          value: option.Value,
          description: option.Description
        };
      });
    },

    canEditField (item) {
      if (item.ResponseTag === 'Assigned' && Access.has('dispatches.editAssigned')) return true;

      if (item.ResponseTag === 'RemoveLienPricing') {
        return is.all.truthy([
          Access.has('calls.price'),
          Access.has('prices.editDispatchAfterReconciled'),
          !Access.has('prices.restrictDelete')
        ]);
      }

      return !item.disabled;
    },

    gotoAssignMap () {
      this.$router.push({
        name: 'AssignCall',
        params: { key: this.callKey },
        query: { additional: true }
      });
    },

    gotoPayments () {
      this.$hub.$emit(EVENT_OPEN_PAYMENTS);
      this.close();
    },

    change (input, action) {
      this.backfill(input, action);
      this.afterChange(input, action);
    },

    afterChange: throttle(function (input, action) {
      switch (input.id) {
        case 'Destination':
          if (input.value.length > 3) {
            this.geocodeDestination(input);
          }
          break;

        case 'Completed':
          this.setField('Mileage', 'IsVisible', !this.$_.isEmpty(input.value), action);
          this.setField('TowTicketNum', 'IsVisible', !this.$_.isEmpty(input.value), action);
          this.setField('CompletionState', 'IsVisible', !this.$_.isEmpty(input.value), action);
          this.setField('PrintReceipt', 'IsVisible', !this.$_.isEmpty(input.value), action);
          this.setField('LotKey', 'IsVisible', this.getFieldValue('CompletionState') === 'Inventory', action);
          break;

        case 'TerminateLien':
          this.setField('RemoveLienPricing', 'IsVisible', this.$_.isEmpty(input.value), action);
          break;

        case 'CompletionState':
          this.setField('LotKey', 'IsVisible', this.getFieldValue('CompletionState') === 'Inventory', action);
          break;
      }
    }, 0.5 * 1000),

    geocodeDestination: throttle(function (input) {
      this.MAP__getCoordinates({
        location: input.value,
        callback: response => {
          this.setFieldValue('DestinationLat', response.Latitude);
          this.setFieldValue('DestinationLon', response.Longitude);
        }
      });
    }, 3 * 1000),

    lastDispatchTipVisible (action) {
      return this.isFinalDispatch() && this.actionIs(action, 'dispatchupdate');
    },

    isFinalDispatch () {
      return !this.datesVisible;
    },

    actionIs (action, names) {
      let isMatched = false;

      this.$_.forEach(this.$_.castArray(names), name => {
        if (this.$_.startsWith(this.$_.toLower(action.ResponseValue), name)) isMatched = true;
      });

      return isMatched;
    },

    backfill (input, action) {
      if (this.$_.isEmpty(input.value)) return;

      let watermark = this.$_.find(this.backfillableFields, ['name', input.id]);

      if (this.$_.isEmpty(watermark)) return;

      let fieldsBelowWatermark = this.$_.filter(this.backfillableFields, field => field.order < watermark.order);

      this.$_.forEach(fieldsBelowWatermark, field => {
        this.weakSetFieldValue(field.name, input.value);
      });
    },

    reset () {
      this.actionPool = {};
      this.useCallNumber = false;
      this.dispatchedUnit = null;
      this.isLoading = false;
      this.resetConditionReport();
    },

    processSwipe (swipe) {
      this.setFieldValue('Name', this.$_.get(swipe, 'fullName', ''));
      this.setFieldValue('Address1', this.$_.get(swipe, 'address1', ''));
      this.setFieldValue('City', this.$_.get(swipe, 'city', ''));
      this.setFieldValue('State', this.$_.get(swipe, 'state', ''));
      this.setFieldValue('Zip', this.$_.get(swipe, 'zip', ''));
      this.setFieldValue('LicenseNum', this.$_.get(swipe, 'licenseNumber', ''));
      this.setFieldValue('Phone', this.$_.get(swipe, 'phone', ''));
    },

    slugify (text) {
      return this.$_.kebabCase(text);
    },

    lookAt (section) {
      let actionsSection = window.getComputedStyle(document.querySelector('.actions-section'));
      let offsetY = Number(actionsSection.padding.replace('px', ''));

      this.$gsap.to('.modal-card-body', {
        duration: 0.8,
        ease: 'power4.out',
        scrollTo: {
          y: `#${section}`,
          offsetY: offsetY
        }
      });
    }
  }
};
</script>

<style scoped>
.sections {
  display: flex;
  position: relative;
}

.sections.is-loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
  border-radius: 0.5rem;
}

.loading-icon {
  font-size: 2rem;
  color: var(--pure-blue);
  margin-bottom: 1rem;
}

.index-section {
  position: relative;

  flex: 1;

  padding: 1rem 0.5rem;
  background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 50%));

  ._liner {
    position: sticky;
    top: 0.5rem;
  }

  .index__item {
    padding: 0.5rem 1rem;
    font-weight: bold;
    color: var(--pure-blue);
    border-radius: 0.5rem;
    will-change: background box-shadow;
  }
}

.actions-section {
  flex: 2;

  padding: 1.0rem;
  background-color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 55%));
}

.end-of-actions {
  text-align: center;
  padding: 1rem 0;
  color: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 45%));
}

.required-indicator {
  color: var(--danger);
  margin-right: .5em;
}

.action {
  padding: 1rem;
  margin-bottom: 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow-100);

  .action__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .action__body {
    padding-top: 1rem;
    margin-top: 0.5rem;
    border-top: 0.1rem solid var(--input-border);

    &:empty {
      display: none;
    }
  }

  .action__footer {
    display: flex;
    justify-content: space-between;

    padding-top: 1rem;
    margin-top: 1rem;
    border-top: 0.1rem solid var(--input-border);

    &:empty {
      display: none;
    }
  }

  .action__name {
    font-weight: bold;
  }
}

.item {
  ._label {
    text-align: right;

    label {
      font-weight: bold;
    }
  }

  .tow-ticket-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;

    padding: 0.75rem;
  }

  ._use-call-number {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;

    border: 1px solid var(--input-border);
    border-radius: 0.25rem;

    input {
      width: 1rem;
    }
  }
}

._or {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: 2rem;
  font-size: var(--font-size-small1);
  text-transform: uppercase;
  letter-spacing: var(--font-letter-spacing);

  &::before,
  &::after {
    content: "";
    width: 100%;
    height: 1px;
    background-color: var(--input-border);
  }

  &::before {
    margin-right: 2rem;
  }

  &::after {
    margin-left: 2rem;
  }
}

.dispatch-units-control {
  border: 1px solid var(--input-border);
  border-radius: 0.25rem;
  overflow: hidden;
}
</style>
