<script>
import set from 'lodash/set';
import uniqueId from 'lodash/uniqueId';

export default {
  props: {
    id: { default: () => uniqueId('control_') },
    value: {},
    size: { default: '' },
    context: { default: '' },
    maxlength: { default: false },
    placeholder: { default: false },
    fullwidth: { default: false },
    autofocus: { default: false },
    tabindex: { default: false },
    disabled: { default: false },
    required: { default: false },
    readonly: { default: false },
    autocomplete: { default: 'off' }
  },

  data () {
    return {
      controlType: 'input',
      sizeClasses: {
        'small': 'is-small',
        'normal': '',
        'medium': 'is-medium',
        'large': 'is-large'
      },
      modifierClasses: {
        'fullwidth': 'is-fullwidth'
      },
      oldValue: null
    };
  },

  computed: {
    valueProxy: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
        // this.$emit('input', this.formatValue(value));
      }
    },

    controlClasses () {
      let classes = {};

      set(classes, '-input', true);
      set(classes, this.$data.controlType, true);
      set(classes, this.$data.modifierClasses['fullwidth'], this.fullwidth);

      if (this.size) {
        set(classes, this.$data.sizeClasses[this.size], true);
      }

      return classes;
    }
  },

  watch: {
    valueProxy (newValue, oldValue) {
      this.oldValue = oldValue || newValue;
    }
  },

  methods: {
    emit (eventName) {
      this.$emit(eventName, {
        id: this.id,
        value: this.valueProxy
      });
    },

    // Optionally extended by child components...
    formatValue (value) {
      return value;
    },

    setValue (value) {
      this.valueProxy = value;

      this.$nextTick(() => {
        this.$hub.$emit('change', this.$refs.control);
      });
    },

    async getInternalState () {
      await this.$awaitNextTicks(2);
      return {
        oldValue: this.oldValue,
        newValue: this.valueProxy
      };
    }
  }
};
</script>
