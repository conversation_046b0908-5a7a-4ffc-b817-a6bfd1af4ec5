<script>
import is from 'is_js';
import datefns from 'date-fns';
import { mapGetters, mapActions } from 'vuex';
import QuickViews from '../features/QuickViews.vue';
import SpotlightControl from '../features/SpotlightControl.vue';

import {
  EVENT_INFO,
  STASH_FILTER_RELAY
} from '@/config.js';

export default {
  components: {
    QuickViews,
    SpotlightControl
  },

  data () {
    return {
      // Set by child component
      viewConfig: {
        key: null,
        uuid: '',
        noun: '',
        recordKeyName: '',
        readRouteName: '',
        addRouteName: '',

        requireData: false,
        requireFilters: false,
        shouldImmediatelyLoadData: true,
        shouldImmediatelySelectAllRecords: false,

        trimField: {
          key: '',
          name: ''
        },

        dataAdditional: {
          ShowInactive: false
        }
      },

      columnCap: 50,
      viewData: {},
      viewSettings: {},
      showLoader: false,
      canOpenOnlyRecord: false
    };
  },

  computed: {
    ...mapGetters([
      '__cachedFilters',
      'RECORDS__settings',
      '__selectedRecords'
    ]),

    preserveFilters: {
      get () {
        if (!this.$data.viewConfig.uuid) return false;

        return window.sessionStorage.getItem(`${this.$data.viewConfig.uuid}-preserve-filters`) || false;
      },
      set (boolean) {
        if (!this.$data.viewConfig.uuid) return;

        window.sessionStorage.setItem(`${this.$data.viewConfig.uuid}-preserve-filters`, boolean);
      }
    },

    gridSettings: {
      get () {
        let grid = {};
        let relayedFilters = JSON.parse(window.sessionStorage.getItem(STASH_FILTER_RELAY)) || [];

        if (!this.$_.hasIn(this.$data.viewSettings, 'Grids[0]')) {
          return {};
        }

        grid = this.$data.viewSettings.Grids[0];

        // Replace default filters with temporary filters relayed from another screen
        if (relayedFilters.length > 0) {
          grid.Filters = relayedFilters;
          this.$data.canOpenOnlyRecord = true;
          window.sessionStorage.removeItem(STASH_FILTER_RELAY);
        }

        return grid;
      },
      set (grid) {
        this.$data.viewSettings.Grids[0] = grid;
      }
    },

    gridData () {
      if (!this.$_.hasIn(this.$data.viewData, `Grids[${this.viewKey}]`)) {
        return [];
      }

      return this.$data.viewData.Grids[this.viewKey];
    },

    refreshedAt () {
      return this.$_.get(this.$data.viewData, 'Current', datefns.format(new Date(), 'YYYY-MM-DD HH:mm:ss'));
    },

    hasData () {
      if (!this.$_.hasIn(this.$data.viewData, `Grids[${this.viewKey}]`)) {
        return false;
      }

      return this.$data.viewData.Grids[this.viewKey].length > 0;
    },

    inactiveToggleClasses () {
      return {
        'fal': true,
        'fa-eye': this.$data.viewConfig.dataAdditional.ShowInactive,
        'fa-eye-slash': !this.$data.viewConfig.dataAdditional.ShowInactive
      };
    },

    viewKey () {
      return this.RECORDS__settings.Key;
    },

    oneRecordIsSelected () {
      return this.__selectedRecords.length === 1;
    },

    anyRecordsAreSelected () {
      return this.__selectedRecords.length >= 1;
    },

    manyRecordsAreSelected () {
      return this.__selectedRecords.length > 1;
    },

    selectedRecordKey () {
      if (!this.oneRecordIsSelected) return false;

      return this.__selectedRecords[0][this.$data.viewConfig.recordKeyName];
    },

    isDeletable () {
      if (!this.oneRecordIsSelected) return false;

      if (!this.$data.viewConfig.dataAdditional.ShowInactive) return true;

      return is.inArray(toString(this.__selectedRecords[0].bActive), ['1', 'true']);
    },

    isUndeletable () {
      if (!this.oneRecordIsSelected) return false;

      return is.inArray(toString(this.__selectedRecords[0].bActive), ['0', 'false']);
    }
  },

  methods: {
    ...mapActions([
      '__cacheRecords',
      'RECORD__delete',
      '__selectRecords',
      'REPORT__getData',
      '__setCurrentView',
      'RECORD__undelete',
      'RECORDS__exportPDF',
      'RECORDS__exportCSV',
      'RECORDS__getSettings',
      'RECORDS__saveSettings',
      'RECORDS__cacheSettings',
      'TOPSCOMPANY__getSettings',
      'TOPSCOMPANY__setSettings',
      'RECORDS__createQuickSearch'
    ]),

    openRecord (record) {
      let key = this.$data.viewConfig.readRouteKey || this.$data.viewConfig.recordKeyName;

      this.$router.push({
        name: this.$data.viewConfig.readRouteName,
        params: { key: record[key] }
      });
    },

    refresh () {
      this.loadData();
    },

    getTOPSCompanySettings () {
      this.TOPSCOMPANY__getSettings({
        callback: response => {
          this.TOPSCOMPANY__setSettings(response);

          if (this.afterGetTOPSCompanySettings !== undefined) {
            this.afterGetTOPSCompanySettings();
          }
        }
      });
    },

    getSettings (props) {
      let viewKey = this.$_.get(props, 'viewKey', '');

      if (!this.$data.viewConfig.noun) return;

      this.$data.showLoader = true;

      this.RECORDS__getSettings({
        noun: this.$data.viewConfig.noun,
        userViewKey: viewKey,
        callback: response => {
          this.RECORDS__cacheSettings(this.pruneColumns(response));
          this.__setCurrentView(this.RECORDS__settings.Title);

          this.$data.viewSettings = this.$_.clone(this.RECORDS__settings);

          if (this.$data.viewConfig.shouldImmediatelyLoadData) {
            this.$nextTick(() => {
              this.loadData();
            });
          } else {
            this.$data.showLoader = false;
          }
        }
      });
    },

    pruneColumns (settings) {
      this.$_.forEach(settings.Grids, grid => {
        // Remove filters if this is a clean session or
        // if the user has changed orgs
        if (!this.preserveFilters) {
          grid.Filters = [];
        }

        if (grid.Columns.length > this.$data.columnCap) {
          grid.Columns = this.$_.take(grid.Columns, this.$data.columnCap);
        }
      });

      if (!this.preserveFilters) {
        this.preserveFilters = true;
      }

      return settings;
    },

    cloneCustomizer (value) {
      if (this.$_.isString(value)) return value;
    },

    async beforeLoadData () {
      if (this.$data.viewConfig.requireFilters &&
        this.$_.get(this.gridSettings, 'Filters', []).length === 0) {
        throw new Error('Push to search');
      }

      return;
    },

    async loadData (targetGridSettings = this.RECORDS__settings) {
      let settingsPresent = false;

      this.$_.forEach(targetGridSettings.Grids, grid => {
        if (this.$_.keys(grid).length > 0) settingsPresent = true;
      });

      if (!settingsPresent) return;

      this.beforeLoadData()
        .then(() => {
          // Hydrate column changes that may have happened in Vuex.
          // Relevant to loading quick views.
          this.$data.viewSettings = this.$_.clone(this.RECORDS__settings);

          this.$store.dispatch('RECORDS__getData', {
            noun: this.$data.viewConfig.noun,
            data: targetGridSettings,
            dataAdditional: this.$data.viewConfig.dataAdditional,
            callback: response => {
              this.$data.showLoader = false;
              this.$data.viewData = response;

              this.cacheRecords();
              this.afterLoadData();

              if (this.$data.viewConfig.requireData && !this.hasData) {
                this.$notify.info({
                  title: 'Info',
                  message: 'No data for this search.'
                });

                return;
              }

              if (this.$data.viewConfig.shouldImmediatelySelectAllRecords) {
                this.selectAllRecords();
              }

              if (this.shouldOpenOnlyRecord()) {
                this.openRecord(this.$data.viewData.Grids[this.viewKey][0]);
              }
            },
            failCallback: () => {
              // this.$router.go(-1);
            }
          });
        })
        .catch(() => {
          this.search();
        });
    },

    afterLoadData () {
      // Extended by children
    },

    search () {
      this.$router.push({
        name: 'GridSearch',
        params: { key: this.gridSettings.Key },
        query: {
          noun: this.$data.viewConfig.noun,
          mode: 'form'
        }
      });
    },

    cacheRecords () {
      let keys = [];

      this.$_.forEach(this.$data.viewData.Grids[this.viewKey], record => {
        keys.push(record[this.$data.viewConfig.recordKeyName]);
      });

      this.__cacheRecords(keys);
    },

    selectAllRecords () {
      let records = [];

      this.$_.forEach(this.$data.viewData.Grids[this.viewKey], record => {
        records.push(record);
      });

      this.__selectRecords(records);
    },

    shouldOpenOnlyRecord () {
      return this.$data.canOpenOnlyRecord && this.$data.viewData.Grids[this.viewKey].length === 1;
    },

    trimToSelection (grid = this.gridSettings) {
      if (!this.manyRecordsAreSelected) return;

      let recordKeys = this.$_.map(this.__selectedRecords, record => {
        if (this.$_.has(record, this.$data.viewConfig.recordKeyName)) return record[this.$data.viewConfig.recordKeyName];
      });

      let newFilter = {
        And: false,
        Or: false,
        Not: false,
        OpenParen: false,
        FieldID: this.$data.viewConfig.trimField.key,
        FieldName: this.$data.viewConfig.trimField.name,
        preset: '',
        Operator: 'In',
        Value: recordKeys.join(', '),
        DisplayValue: recordKeys.join(', '),
        CloseParen: false
      };

      grid.Filters = [];
      grid.Filters.push(newFilter);

      this.save(grid);
    },

    save (grid) {
      let targetGrid = this.$_.find(this.RECORDS__settings.Grids, ['Key', grid.Key]);

      targetGrid = grid;

      let uselessValue = true;
      if (!uselessValue) {
        console.log('Pacifier', targetGrid);
      }

      this.RECORDS__saveSettings({
        noun: this.$data.viewConfig.noun,
        data: this.RECORDS__settings,
        callback: response => {
          this.refresh();
        }
      });
    },

    copyToClipboard (event) {
      if (!this.manyRecordsAreSelected) return;

      this.$hub.$emit(EVENT_INFO, 'Copied to clipboard.');

      let recordKeys = [];

      this.$_.forEach(this.__selectedRecords, record => {
        recordKeys.push(record[this.$data.viewConfig.recordKeyName]);
      });

      event.clipboardData.setData('text/plain', this.$_.join(recordKeys, '\n'));

      event.preventDefault();
    },

    exportData ({ format, gridKey }) {
      let actionName = '';

      switch (format) {
        case 'CSVFile':
          actionName = 'RECORDS__exportCSV';
          break;

        case 'PDF':
          actionName = 'RECORDS__exportPDF';
          break;
      }

      this[actionName]({
        noun: this.$data.viewConfig.noun,
        viewKey: this.viewKey,
        gridKey: gridKey,
        name: this.viewSettings.Title,
        description: this.viewSettings.Description,
        grids: this.$data.viewSettings.Grids,
        showInactive: this.$data.viewConfig.dataAdditional.ShowInactive
      });
    },

    toggleInactiveRecords () {
      this.$data.viewConfig.dataAdditional.ShowInactive = !this.$data.viewConfig.dataAdditional.ShowInactive;

      this.__selectRecords([]);
      this.loadData();
    },

    add () {
      this.$router.push({ name: this.$data.viewConfig.addRouteName });
    },

    duplicate () {
      this.$router.push({
        name: this.$data.viewConfig.addRouteName,
        query: { duplicate: this.selectedRecordKey }
      });
    },

    deleteRecord () {
      let dataPackage = { OverwriteUpdate: false };

      this.$_.set(dataPackage, this.$data.viewConfig.recordKeyName, this.selectedRecordKey);

      this.RECORD__delete({
        noun: this.$data.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.loadData();
        }
      });
    },

    undeleteRecord () {
      let dataPackage = { OverwriteUpdate: false };

      this.$_.set(dataPackage, this.$data.viewConfig.recordKeyName, this.selectedRecordKey);

      this.RECORD__undelete({
        noun: this.$data.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.loadData();
        }
      });
    },

    setFilters (filters) {
      this.gridSettings.Filters = filters;
      this.$data.canOpenOnlyRecord = true;

      this.loadData();
    }
  },

  mounted () {
    document.addEventListener('copy', this.copyToClipboard);

    this.$data.viewConfig.key = this.viewKey;

    this.getSettings();
    this.getTOPSCompanySettings();
  },

  beforeDestroy () {
    document.removeEventListener('copy', this.copyToClipboard);

    this.$data.canOpenOnlyRecord = false;
  }
};
</script>
