<template>
<span id="contact-section">
  <app-accordian class="_contact" v-for="(contact, index) in contactsProxy" :expand-on-mount="shouldExpand(index, contact.isJustAdded)" :key="index">
    <div class="_thumbnail">
      <app-data-point label="Name">{{ contact.vc30Name1 || '--' }}</app-data-point>
      <app-data-point label="Type">{{ getTypeValue(contact.lAddressTypeKey) || '--' }}</app-data-point>
    </div>

    <template slot="controls">
      <app-button class="_delete" @click="removeContact(index)" :disabled="!canDelete">
        <i class="far fa-trash-alt"></i>
      </app-button>
    </template>

    <template slot="body">
      <div class="_card-swipe" v-if="contact.isJustAdded">
        <card-swipe @swipe="processSwipe($event, contact)"></card-swipe>
      </div>

      <app-grid-form class="_editor" context="inline">
        <div class="columns is-multiline">
          <div class="column is-6">
            <app-text v-model="contact.vc30Name1" :required="true" :disabled="!canEditProperty()">
              Name 1
            </app-text>
          </div>
          <div class="column is-3">
            <app-text v-model="contact.vc30Name2" :disabled="!canEditProperty()">
              Name 2
            </app-text>
          </div>
          <div class="column is-3">
            <app-select v-model="contact.lAddressTypeKey" :options="types" keyAlias="Key" valueAlias="Value" :required="true" :disabled="!canEditProperty()">
              Type
            </app-select>
          </div>
          <div class="column is-12 is-left">
            <app-text v-model="contact.vc30Address1" :disabled="!canEditProperty()">
              Address 1
            </app-text>
          </div>
          <div class="column is-12 is-left">
            <app-text v-model="contact.vc30Address2" :disabled="!canEditProperty()">
              Address 2
            </app-text>
          </div>
          <div class="column is-6 is-left">
            <app-text v-model="contact.vc30City" :disabled="!canEditProperty()">
              City
            </app-text>
          </div>
          <div class="column is-3">
            <app-select-state v-model="contact.ch2StateKey" :disabled="!canEditProperty()">
              State
            </app-select-state>
          </div>
          <div class="column is-3">
            <app-text v-model="contact.vc10Zip" :disabled="!canEditProperty()">
              Zip
            </app-text>
          </div>
          <div class="column is-6 is-left">
            <app-text v-model="contact.vc20Phone1" :disabled="!canEditProperty()">
              Phone 1
            </app-text>
          </div>
          <div class="column is-6">
            <app-text v-model="contact.vc20Phone2" :disabled="!canEditProperty()">
              Phone 2
            </app-text>
          </div>
          <div class="column is-6 is-left">
            <app-text v-model="contact.vc20Fax" :disabled="!canEditProperty()">
              Fax
            </app-text>
          </div>
          <div class="column is-6">
            <app-text v-model="contact.vc50Email" :disabled="!canEditProperty()">
              Email
            </app-text>
          </div>
          <div class="column is-6 is-left">
            <app-text v-model="contact.vc50UserDefined1" :disabled="!canEditProperty()">
              User Defined 1
            </app-text>
          </div>
          <div class="column is-6">
            <app-text v-model="contact.vc50UserDefined2" :disabled="!canEditProperty()">
              User Defined 2
            </app-text>
          </div>
          <div class="column is-6 is-left">
            <app-data-point class="_faux-input" label="Created Date">
              {{ contact.dCreated }}
            </app-data-point>
          </div>
          <div class="column is-6">
            <app-data-point class="_faux-input" label="Created By">
              {{ resolveUserName(contact.lUserKeyCreatedBy) }}
            </app-data-point>
          </div>
        </div>
      </app-grid-form>
    </template>
  </app-accordian>

  <section class="_controls">
    <app-button id="contact.add" @click="addContact">
      Add Contact
    </app-button>
  </section>
</span>
</template>

<script>
import Access from '@/access.js';
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';
import cardSwipe from '../features/CardSwipe.vue';

import {
  CALL_SECTION_CONTACTS,
  CONTACT_PARENT_TYPE_KEY
} from '@/config.js';

export default {
  name: CALL_SECTION_CONTACTS,

  extends: BaseSection,

  components: {
    cardSwipe
  },

  data () {
    return {
      sectionName: CALL_SECTION_CONTACTS,

      types: [],
      users: []
    };
  },

  computed: {
    ...mapGetters(['__state']),

    contactsProxy: {
      get () {
        return this.$_.sortBy(this.$_.get(this.call, 'Contacts', []), ['tOrder']);
      },
      set (value) {
        this.$set(this.call, 'Contacts', value);

        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },

    canDelete () {
      return Access.has('deleteContacts');
    }
  },

  watch: {
    'call.Contacts' () {
      this.getUsers();
    }
  },

  methods: {
    ...mapActions([
      'USER__getID',
      'CONTACT__getTypes'
    ]),

    getTypes () {
      this.CONTACT__getTypes({
        success: response => {
          this.$data.types = response;
        }
      });
    },

    getUsers () {
      let userKeys = [];

      this.contactsProxy.forEach(contact => {
        if (contact.lUserKeyCreatedBy.length > 0) {
          userKeys.push(contact.lUserKeyCreatedBy);
        }
      });

      if (userKeys.length === 0) return;

      this.USER__getID({
        keys: userKeys,
        success: response => {
          this.$data.users = response;
        }
      });
    },

    getTypeValue (key) {
      let type = this.$_.find(this.$data.types, ['Key', key]);

      return this.$_.get(type, 'Value', '');
    },

    shouldExpand (index, isJustAdded = null) {
      if (isJustAdded) return true;

      return this.contactsProxy.length === 1 && index === 0;
    },

    resolveUserName (userKey) {
      let user = this.$_.find(this.$data.users, ['Key', userKey]);

      return this.$_.get(user, 'Value', '');
    },

    async addContact () {
      this.createIfMissing('Contacts', []);

      this.call.Contacts.push({
        lParentKey: this.call.lCallKey,
        lParentTypeKey: CONTACT_PARENT_TYPE_KEY,
        lAddressTypeKey: '',
        tOrder: this.contactsProxy.length + 1,
        vc30Name1: '',
        vc30Name2: '',
        vc30Address1: '',
        vc30Address2: '',
        vc30City: '',
        ch2StateKey: '',
        vc10Zip: '',
        vc20Phone1: '',
        vc20Phone2: '',
        vc20Fax: '',
        vc50Email: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        dCreated: await this.getNow(),
        lUserKeyCreatedBy: this.__state.user.Key,
        isJustAdded: true
      });
    },

    removeContact (index) {
      let targetContact = this.contactsProxy[index];
      let trimmedContacts = this.$_.without(this.contactsProxy, targetContact);

      this.contactsProxy = trimmedContacts;
    },

    processSwipe (swipe, contact) {
      contact.vc30Name1 = this.$_.get(swipe, 'fullName', '');
      contact.vc30Address1 = this.$_.get(swipe, 'address1', '');
      contact.vc30City = this.$_.get(swipe, 'city', '');
      contact.ch2StateKey = this.$_.get(swipe, 'state', '');
      contact.vc10Zip = this.$_.get(swipe, 'zip', '');
      contact.vc20Phone1 = this.$_.get(swipe, 'phone', '');
    }
  },

  mounted () {
    this.getTypes();
    this.getUsers();
  }
};
</script>
