<template>
  <span :id="sectionName">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-4">
          <app-data-point label="Sent At">
            {{ invoiceProxy.dSent }}
          </app-data-point>
        </div>
        <div class="column is-4">
          <app-data-point label="Sent By">
            {{ getUserKey(invoiceProxy.lSentBy) }}
          </app-data-point>
        </div>
        <div class="column is-4">
          <app-data-point label="Total">
            ${{ Number(invoiceProxy.tcTotal).toFixed(2) }}
          </app-data-point>
        </div>
        <div class="column is-4">
          <app-data-point label="Submission Status">
            {{ invoiceProxy.sSubmissionStatus }}
          </app-data-point>
        </div>
        <div class="column is-4">
          <app-data-point label="Approval">
            {{ approvedProxy }}
          </app-data-point>
        </div>
        <div class="column is-4">
          <app-data-point label="Response Date">
            {{ invoiceProxy.dResponse }}
          </app-data-point>
        </div>
        <div class="column is-12" v-if="invoiceProxy.sMessage">
          <app-data-point label="Error Message">
            {{ invoiceProxy.sMessage }}
          </app-data-point>
        </div>
      </div>
    </app-grid-form>

    <section class="action-panel">
      <app-button @click="reviewInvoice" type="default">
        Show invoice
      </app-button>

      <invoice-preview
        :callKey="invoicePreview.callKey"
        :invoiceKey="invoiceProxy.lKey"
        v-if="invoicePreview.isModalVisible"
        @close="invoicePreview.isModalVisible = false">
      </invoice-preview>
    </section>
  </span>
</template>

<script>
import Access from '@/access.js';
import BaseSection from './BaseSection.vue';
import InvoicePreview from '../features/InvoicePreview.vue';
import { CALL_SECTION_MOTOR_CLUB_BILLING } from '@/config.js';

export default {
  name: CALL_SECTION_MOTOR_CLUB_BILLING,

  extends: BaseSection,

  components: {
    InvoicePreview
  },

  data () {
    return {
      sectionName: CALL_SECTION_MOTOR_CLUB_BILLING,

      invoicePreview: {
        callKey: this.call.lCallKey,
        isModalVisible: false
      }
    };
  },

  computed: {
    rawUserKeys () {
      let keys = [];

      if (this.invoiceProxy.lSentBy) {
        keys.push(this.invoiceProxy.lSentBy);
      }

      return keys;
    },

    invoiceProxy () {
      return this.$_.get(this.call, 'MCInvoice', {});
    },

    canEdit () {
      return Access.has('motorClubBilling.edit');
    },

    approvedProxy () {
      if (!this.invoiceProxy.dResponse) return 'Pending';

      return [true, 'true'].includes(this.invoiceProxy.bApproved) ? 'Approved' : 'Rejected';
    }
  },

  watch: {
    rawUserKeys () {
      this.getUserIds();
    }
  },

  mounted () {
    this.getUserIds();
  },

  methods: {
    reviewInvoice () {
      this.$data.invoicePreview.isModalVisible = true;
    }
  }
};
</script>
