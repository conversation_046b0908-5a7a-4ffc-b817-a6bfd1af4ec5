<template>
  <section class="service-list">
    <ServiceItem
      v-for="service in services"
      :service="service"
      :dispatch-key="dispatchKey"
      :call="call"
      @on-add-service="$emit('on-add-service', $event)"
      :key="service.ServiceKey" />
  </section>
</template>

<script>
import ServiceItem from './ServiceItem.vue';

export default {
  name: 'ServiceList',

  components: {
    ServiceItem
  },

  props: {
    services: {
      type: Array,
      required: true
    },
    call: {
      type: Object,
      required: true
    },
    dispatchKey: {
      type: [String, Number],
      required: true
    }
  }
};
</script>

<style scoped>
.service-list {
  display: grid;
  gap: 1px;

  color: var(--body-fg);
  background-color: hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-color: var(--input-border);
  box-shadow: var(--box-shadow-100);
}
</style>
