<template>
  <span id="retow-dispatch-section">
    <app-accordian class="_dispatch" v-for="dispatch in dispatchesProxy" :key="dispatch.lDispatchKey">
      <div class="_thumbnail">
        <app-data-point label="Driver Number">{{ getProperty(dispatch.lDispatch<PERSON>ey, 'DriverNum') }}</app-data-point>
        <app-data-point label="Truck Number">{{ getProperty(dispatch.lDispatchKey, 'TruckNum') }}</app-data-point>
        <app-data-point label="Status">{{ getProperty(dispatch.lDispatchKey, 'Status') }}</app-data-point>
      </div>

      <template slot="body">
        <app-grid-form class="_editor" context="inline">
          <div class="columns is-multiline">
            <div class="column is-3">
              <app-shortcode id="DIS_lDriverKey" v-model="dispatch.lDriverKey" :options="drivers" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditProperty()">
                Driver Number
              </app-shortcode>
            </div>
            <div class="column is-3">
              <app-shortcode id="DIS_lTruckKey" v-model="dispatch.lTruckKey" :options="trucks" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditProperty()">
                Truck Number
              </app-shortcode>
            </div>
            <div class="column is-3">
              <label>Mileage</label>
              <input v-model="dispatch.lTotalMileage" type="text" class="input" autocomplete="off" :disabled="!canEditProperty()" />
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="Status">{{ getProperty(dispatch.lDispatchKey, 'Status') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Assigned Date</label>
              <app-date-time v-model="dispatch.dAssigned" @change="backfill(dispatch, $event)" id="DIS_dAssigned" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Assigned By">{{ getProperty(dispatch.lDispatchKey, 'AssignedBy') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Dispatched Date</label>
              <app-date-time v-model="dispatch.dDispatched" @change="backfill(dispatch, $event)" id="DIS_dDispatched" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Dispatched By">{{ getProperty(dispatch.lDispatchKey, 'DispatchedBy') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Acknowledged Date</label>
              <app-date-time v-model="dispatch.dAcknowledged" @change="backfill(dispatch, $event)" id="DIS_dAcknowledged" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Acknowledged By">{{ getProperty(dispatch.lDispatchKey, 'AcknowledgedBy') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Arrived Date</label>
              <app-date-time v-model="dispatch.dArrived" @change="backfill(dispatch, $event)" id="DIS_dArrived" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Arrived By">{{ getProperty(dispatch.lDispatchKey, 'ArrivedBy') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Hooked Date</label>
              <app-date-time v-model="dispatch.dHooked" @change="backfill(dispatch, $event)" id="DIS_dHooked" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Hooked By">{{ getProperty(dispatch.lDispatchKey, 'HookedBy') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Dropped Date</label>
              <app-date-time v-model="dispatch.dDropped" @change="backfill(dispatch, $event)" id="DIS_dDropped" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Dropped By">{{ getProperty(dispatch.lDispatchKey, 'DroppedBy') }}</app-data-point>
            </div>
            <div class="column is-6 is-left">
              <label>Completed Date</label>
              <app-date-time v-model="dispatch.dCompleted" @change="backfillAndComplete(dispatch, $event)" id="DIS_dCompleted" :disabled="!canEditProperty()"></app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="Completed By">{{ getProperty(dispatch.lDispatchKey, 'CompletedBy') }}</app-data-point>
            </div>
            <div class="column is-12 is-left is-bottom" v-if="canEditCommissionAmounts">
              <label>Commissionable Amount</label>
              <input v-model="dispatch.tcCommission" type="text" class="input" autocomplete="off" :disabled="!canEditProperty()" />
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>
  </span>
</template>

<script>
import DispatchSection from './DispatchSection.vue';
import { CALL_SECTION_RETOW_DISPATCH } from '@/config.js';

export default {
  name: 'redispatch-section',

  extends: DispatchSection,

  data () {
    return {
      sectionName: CALL_SECTION_RETOW_DISPATCH
    };
  },

  computed: {
    dispatchesProxy () {
      let dispatches = this.$_.get(this.call, 'Dispatches', []);

      return this.$_.filter(dispatches, ['bRetow', true]);
    }
  }
};
</script>
