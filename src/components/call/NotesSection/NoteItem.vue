<template>
  <div class="note">
    <div class="_payload">
      <template v-if="isInEditMode">
        <input class="_input input" v-model="note.vc255Note" maxlength="255" autocomplete="off" />
      </template>
      <template v-else>
        <div class="_message">{{ note.vc255Note }}</div>
        <cite class="_author">
          <template v-if="note.dCreatedOn">
            {{ note.dCreatedOn | verbalDate }} at {{ note.dCreatedOn | verbalTime }} by <UsernameShow :user-key="note.lCreatedBy" />
          </template>
          <template v-else>
            Not saved
          </template>
        </cite>
      </template>
    </div>
    <div class="_controls">

      <app-button class="_edit" type="default" size="normal" v-show="!isInEditMode" @click="startEdit" :disabled="!canEdit">
        <i class="far fa-pen"></i>
      </app-button>
      <app-button class="_undo" type="default" size="normal" v-show="isInEditMode" @click="cancelEdit">
        <i class="far fa-xmark"></i>
      </app-button>

      <app-button class="_remove" @click="onRemove(index)" type="default" size="normal" :disabled="!canDelete">
        <i class="far fa-trash-alt"></i>
      </app-button>
    </div>
  </div>
</template>

<script>
import Access from '@/utils/access.js';
import UsernameShow from '@/components/inputs/UsernameShow.vue';

export default {
  name: 'note-item',

  components: {
    UsernameShow
  },

  props: {
    note: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    }
  },

  data () {
    return {
      isInEditMode: false,
      startingValue: ''
    };
  },

  computed: {
    canEdit () {
      if (this.isCallConfirmed) {
        return Access.has('calls.editNotesAfterConfirmed');
      }

      return Access.has('calls.edit') && (Number(this.$store.state.user.Key) === Number(this.note.lCreatedBy));
    },

    canDelete () {
      return this.canEdit;
    }
  },

  methods: {
    startEdit () {
      this.isInEditMode = true;
      this.startingValue = this.note.vc255Note;
    },

    cancelEdit () {
      this.isInEditMode = false;
      this.note.vc255Note = this.startingValue;
    },

    onRemove (index) {
      this.$emit('on-remove', index);
    }
  }
};
</script>
