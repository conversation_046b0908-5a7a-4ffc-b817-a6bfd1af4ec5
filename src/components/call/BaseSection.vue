<script>
import Access from '@/access.js';
import { mapActions } from 'vuex';
import FooterBar from '../features/FooterBar.vue';

import {
  AFTER_CALL_READ,
  BEFORE_CALL_READ,
  EVENT_TRIGGER_CALL_READ
} from '@/config.js';

export default {
  props: {
    searchMode: { type: Boolean, default: false },
    quoteMode: { type: Boolean, default: false },
    call: { type: Object, required: false, default: null },
    isVisible: { type: Boolean, required: false, default: true }
  },

  components: { FooterBar },

  data () {
    return {
      users: [],
      lastReadAt: '',
      sectionName: '',
      subterminal: {},
      isLoaded: false,
      callSectionDetails: '', // [String, Array]: Get section-specific details (e.g. 'towpricing', 'cancel')

      postConfirmationAccess: {
        sections: [
          'miscellaneous',
          'accounting',
          'notes',
          'holds',
          'contacts'
        ],
        properties: [
          'vc20PoliceNum',
          'vc10PoliceBeat',
          'vc100UserDefined1',
          'vc50UserDefined2',
          'vc50UserDefined3',
          'vc255AccountingNotes'
        ]
      }
    };
  },

  computed: {
    rawUserKeys () {
      /**
       * Optionally extended by children. Example:
       *
       * let keys = [];
       * if (this.call.lEmailSentBy) {
       *   keys.push(this.call.lEmailSentBy);
       * }
       *
       * return keys;
       */

      return [];
    },

    callKey () {
      return this.$route.params.key || this.call.lCallKey;
    },

    isNewCall () {
      return !Number(this.callKey) > 0;
    },

    hasReconciledPayments () {
      let payments = this.$_.get(this.call, 'TowPayments', []);
      let reconciledPayments = this.$_.filter(payments, payment => !!payment.dReconciled);

      return reconciledPayments.length > 0;
    },

    isTowReconciled () {
      return !this.$_.isEmpty(this.$_.get(this.call, 'dAcctReconcil', ''));
    },

    isRetowReconciled () {
      return !this.$_.isEmpty(this.$_.get(this.call, 'dRetowAcctReconcil', ''));
    },

    isCallConfirmed () {
      return !this.$_.isEmpty(this.$_.get(this.call, 'dAccConfirm', ''));
    },

    subterminalKey () {
      return this.$_.get(this.call, 'lSubterminalKey', '');
    }
  },

  watch: {
    rawUserKeys () {
      this.getUserIds();
    },

    subterminalKey () {
      if (this.$_.isEmpty(this.subterminalKey)) return;
      if (this.searchMode) return;

      this.TOPSCALL__getSubcompanyDetails({
        subterminalKey: this.subterminalKey,
        success: response => {
          this.$data.subterminal = response;
        }
      });
    }
  },

  methods: {
    ...mapActions([
      '__getNow',
      'USER__getID',
      'TOPSCALL__getSubcompanyDetails'
    ]),

    getUserKey (key) {
      let user = this.$_.find(this.$data.users, ['Key', Number(key)]);

      return this.$_.get(user, 'Value', '');
    },

    getUserIds () {
      if (!this.rawUserKeys.length) return;

      this.USER__getID({
        keys: this.rawUserKeys,
        success: response => {
          this.$data.users = response;
        }
      });
    },

    async getNow () {
      return new Promise((resolve, reject) => {
        this.__getNow({
          callback: response => {
            resolve(response.Now);
          }
        });
      });
    },

    createIfMissing (name, value = '') {
      if (!this.$_.has(this.call, name)) {
        this.$set(this.call, name, value);
      }
    },

    canSave (section = '') {
      if (!this.call.lCallKey) return false;

      if (!this.isCallConfirmed) return true;

      if (Access.has('calls.fullEditAfterConfirmed')) return true;

      return Access.has('calls.partialEditAfterConfirmed') && this.$data.postConfirmationAccess.sections.includes(section);
    },

    canEditProperty (property = '') {
      if (!this.isCallConfirmed) return true;

      if (Access.has('calls.fullEditAfterConfirmed')) return true;

      return Access.has('calls.partialEditAfterConfirmed') && this.$data.postConfirmationAccess.properties.includes(property);
    },

    readCall () {
      this.$hub.$emit(EVENT_TRIGGER_CALL_READ);
    }
  },

  mounted () {
    // Initialize on mount and after each call read
    if (this.afterCallRead) this.afterCallRead();

    this.$hub.$on(BEFORE_CALL_READ, () => {
      if (this.call) {
        this.$set(this.call, 'lCustomerKey', '');
      }
    });

    this.$hub.$on(AFTER_CALL_READ, () => {
      if (this.afterCallRead) this.afterCallRead();
    });
  }
};
</script>
