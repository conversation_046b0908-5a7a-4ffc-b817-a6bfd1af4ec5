<template>
  <section :id="sectionName">
    <app-accordian class="_order-line" v-for="(orderLine, index) in saleOrderLinesProxy" group="order-line" :key="orderLine.lOrderLineKey">
      <div class="_thumbnail">
        <app-data-point label="Description">{{ orderLine.vc100Description }}</app-data-point>
        <app-data-point label="Quantity">{{ orderLine.pQty }} <span v-if="Number(orderLine.bCalculated)" slot="affix" title="Calculated value">(Calc.)</span></app-data-point>
        <app-data-point label="Line Total">{{ orderLine.tcTotalPrice | usd }}</app-data-point>
      </div>

      <template slot="controls">
        <app-button @click="removeOrderLine(index)" class="delete-control" :disabled="!canDelete(orderLine)"><i class="far fa-trash-alt"></i></app-button>
        <app-button @click="toggleOrderLines"><i :class="orderLineGroupExpandedIcons"></i></app-button>
      </template>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns is-multiline">
            <div class="column is-4">
              <app-text v-model="orderLine.vc100Description" @change="calculateOrderPricing" maxlength="100" :disabled="!canEditDescription(orderLine)">
                Description
              </app-text>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.pQty" :min="getMininumQuantity(orderLine.fMinimumQty)" :max="getMaximumQuantity(orderLine.fMaximumQty)" :disabled="true" @blur="enforceQuantityRange(orderLine)" @change="calculateOrderPricing" @keyup="calculateOrderPricing">
                Quantity <span v-if="Number(orderLine.bCalculated)" title="Calculated value">(Calc.)</span>
              </app-number>
            </div>
            <div class="column is-4">
              <app-select v-model.number="orderLine.lDispatchKey" v-if="dispatchIsVisible(orderLine)" :options="dispatchesProxy" keyAlias="lDispatchKey" valueAlias="DriverTruckPair" :required="true" :disabled="true">
                Dispatch
              </app-select>
            </div>
            <div class="column is-3 is-left">
              <app-checkbox v-model="orderLine.bDiscountable" :disabled="!canEditDiscountable(orderLine)">
                Discountable
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox v-model="orderLine.bTaxable" :disabled="!canEditTaxable(orderLine)">
                Taxable
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox v-model="orderLine.bCommissionable" :disabled="!canEditCommissionable(orderLine)">
                Commissionable
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox v-model="orderLine.bSurchargeable" :disabled="!canEditSurchargeable(orderLine)">
                Surchargeable
              </app-checkbox>
            </div>
            <div class="column is-4 is-left">
              <app-number
                v-model="orderLine.scInitialRate"
                @change="calculateOrderPricing"
                :min="getMinimumRate(orderLine.bNoNegativePrice)"
                :max="getMaximumRate(orderLine.bNoNegativePrice)"
                :disabled="true">
                Initial <span v-if="orderLine.bInitialFlatRate">Flat</span> Rate
              </app-number>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.fInitialQty" :disabled="true">
                Initial Quantity
              </app-number>
            </div>
            <div class="column is-4">
              <app-text v-model="orderLine.sInitialUnit" :disabled="true">
                Initial Unit
              </app-text>
            </div>
            <div class="column is-4 is-left">
              <app-number
                v-model="orderLine.scSecondaryRate"
                @change="calculateOrderPricing"
                :min="getMinimumRate(orderLine.bNoNegativePrice)"
                :max="getMaximumRate(orderLine.bNoNegativePrice)"
                :disabled="true">
                Secondary <span v-if="orderLine.bSecondaryFlatRate">Flat</span> Rate
              </app-number>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.fSecondaryQty" :disabled="true">
                Secondary Quantity
              </app-number>
            </div>
            <div class="column is-4">
              <app-text v-model="orderLine.sSecondaryUnit" :disabled="true">
                Secondary Unit
              </app-text>
            </div>
            <div class="column is-4 is-left">
              <app-number
                v-model="orderLine.scTertiaryRate"
                @change="calculateOrderPricing"
                :min="getMinimumRate(orderLine.bNoNegativePrice)"
                :max="getMaximumRate(orderLine.bNoNegativePrice)"
                :disabled="true">
                Tertiary <span v-if="orderLine.bTertiaryFlatRate">Flat</span> Rate
              </app-number>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.pTertiaryQtyApplied" :disabled="true">
                Tertiary Quantity
              </app-number>
            </div>
            <div class="column is-4">
              <app-text v-model="orderLine.sTertiaryUnit" :disabled="true">
                Tertiary Unit
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <div v-if="!saleOrderLinesProxy.length">
      <transition name="fade" mode="out-in">
        <app-placeholder v-if="!canGetPricing" key="requirements">
          <p>To add a service, please:</p>
          <br>
          <ul>
            <li :data-complete="isSubterminalSet">
              <i class="_mark" :class="subterminalRequirementsIcon"></i> <span>Select a Company.</span>
            </li>
            <li :data-complete="isCustomerSet">
              <i class="_mark" :class="customerRequirementsIcon"></i> <span>Select a Customer.</span>
            </li>
            <li :data-complete="isTowTypeSet">
              <i class="_mark" :class="towTypeRequirementsIcon"></i> <span>Select a Tow Type.</span>
            </li>
          </ul>
        </app-placeholder>
        <app-placeholder v-else key="empty-state">No pricing items added.</app-placeholder>
      </transition>
    </div>

    <section class="_controls">
      <app-button @click="toggleServicePicker(true)" :disabled="true" id="salePricingAddService">
        Add services&hellip;
      </app-button>
      <app-button @click="getPricing" :disabled="true">
        Get defaults
      </app-button>

      <div class="flex-space"></div>

      <div v-if="canGetPricing" class="field has-addons is-pulled-right">
        <p class="control width-auto">
          <a class="button is-small is-static">Override Tax</a>
        </p>
        <p class="control width-auto">
          <input v-model="call.fTaxRate_Override" @change="calculateOrderPricing" class="input is-small" type="number" :disabled="!canOverrideTax()" min="0" max="100" style="width: 135px">
        </p>
        <p class="control width-auto">
          <a class="button is-small is-static">%</a>
        </p>
      </div>
    </section>

    <transition name="fade" mode="out-in">
      <div class="_summary" v-if="saleOrderLinesProxy.length">
        <section class="_liner" v-if="canSeeLineDetail">
          <div class="_subtotal -label">Subtotal</div>
          <div class="_subtotal -amount">{{ subtotal | usd }}</div>

          <div class="_discount -label">Discount</div>
          <div class="_discount -formula">{{ discountableSubtotal | usd }} x {{ call.fDiscountPct | wholeNumber }}%</div>
          <div class="_discount -amount">{{ call.tcDiscountTotal | usd }}</div>

          <div class="_tax -label">Tax</div>
          <div class="_tax -formula">{{ taxableSubtotal | usd }} x {{ priceRepository.sale.TaxRateOverride || priceRepository.sale.TaxRate | wholeNumber(0) }}%</div>
          <div class="_tax -amount">{{ priceRepository.sale.TaxTotal | usd }}</div>

          <div class="_total -label">Total</div>
          <div class="_total -amount">{{ priceRepository.sale.Total | usd }}</div>
        </section>
      </div>
    </transition>

    <app-grid-form class="comments" context="inline">
      <app-textarea v-model="call.SaleOrderComments" :disabled="true">
        Invoice Notes
      </app-textarea>
    </app-grid-form>

    <portal to="modals">
      <template v-if="servicesVisible">
        <app-modal title="Sale Services" class="pricing-services" :show="true" @close="toggleServicePicker(false)" :pad="false">

          <section class="filters-panel">
            <select id="serviceFilterDispatch" v-model.number="serviceFilterDispatchKey">
              <option value="">Select a dispatch</option>
              <option v-for="dispatch in dispatchesProxy"
                :value="dispatch.lDispatchKey"
                :disabled="!dispatch.isEnabled"
                :key="dispatch.lDispatchKey">
                {{ dispatch.DriverTruckPair }}
              </option>
            </select>
            <input type="search" id="serviceFilterControl" v-model="serviceFilterTerm" placeholder="Filter services..." />
            <tab-group v-model="serviceCategoryTerm">
              <tab-item value="All Services" :disabled="!canSeeAllServices">All</tab-item>
              <tab-item value="Basic Services">Basic</tab-item>
              <tab-item value="Customer Services">Customer</tab-item>
            </tab-group>
          </section>

          <section class="service-list">
            <button class="service-item"
              v-for="service in filteredServices"
              :key="service.ServiceKey"
              :disabled="!canAddService(service)"
              @click="addService(service)">

              <div class="name is-bold">{{ service.Description }}</div>
              <div>
                <div class="is-small is-upper is-bold opacity-30">Type</div>
                <div>{{ service.Type || '-' }}</div>
              </div>
              <div>
                <div class="is-small is-upper is-bold opacity-30">Unit</div>
                <div>{{ service.Units || '-' }}</div>
              </div>
              <div>
                <div class="is-small is-upper is-bold opacity-30">Class</div>
                <div>{{ service.TowClass || '-' }}</div>
              </div>
              <div>
                <div class="is-small is-upper is-bold opacity-30">GL Location</div>
                <div>{{ service.GLLocation || '-' }}</div>
              </div>

            </button>
          </section>

        </app-modal>
      </template>
    </portal>
  </section>
</template>

<script>
import numeral from 'numeral';
import Access from '@/access';
import { mapActions } from 'vuex';
import { debounce } from 'lodash';
import BaseSection from './BaseSection.vue';

import {
  VALUE_ID,
  AFTER_CALL_READ,
  BEFORE_CALL_READ,
  CALL_SECTION_SALE_PRICING,
  EVENT_RESET_CALL_PRICING,
  EVENT_TOGGLE_ACCORDIAN_GROUP
} from '@/config';

export default {
  name: 'pricing-section',

  extends: BaseSection,

  props: {
    priceRepository: { type: Object }
  },

  data () {
    return {
      sectionName: CALL_SECTION_SALE_PRICING,

      subtotal: 0,
      services: [],
      taxableSubtotal: 0,
      dispatchDetails: [],
      serviceFilterDispatchKey: '',
      serviceFilterTerm: '',
      serviceCategoryTerm: '',
      useSpeedBumps: false,
      servicesVisible: false,
      discountableSubtotal: 0,
      orderLineGroupExpanded: false,
      callSectionDetails: 'salepricing'
    };
  },

  computed: {
    saleOrderLinesProxy () {
      return this.$_.get(this.call, 'SaleOrderLines', []);
    },

    canSeeLineDetail () {
      return !Access.has('prices.restrictOrderLineDetail');
    },

    canSeeAllServices () {
      return Access.has('prices.allServices');
    },

    filteredServices () {
      let filteredServices = [];

      filteredServices = this.$_.isEmpty(this.$data.serviceFilterTerm)
        ? this.$data.services
        : this.$_.filter(this.$data.services, service => this.$_.includes(this.$_.toLower(service.Description), this.$_.toLower(this.$data.serviceFilterTerm)));

      return this.$_.orderBy(filteredServices, ['Description']);
    },

    isSubterminalSet () {
      return Number(this.call.lSubterminalKey) > 0;
    },

    subterminalRequirementsIcon () {
      return {
        'far': true,
        'fa-circle': !this.isSubterminalSet,
        'fa-check-circle': this.isSubterminalSet
      };
    },

    isCustomerSet () {
      return Number(this.call.lCustomerKey) > 0;
    },

    customerRequirementsIcon () {
      return {
        'far': true,
        'fa-circle': !this.isCustomerSet,
        'fa-check-circle': this.isCustomerSet
      };
    },

    isTowTypeSet () {
      return Number(this.call.lTowTypeKey) > 0;
    },

    towTypeRequirementsIcon () {
      return {
        'far': true,
        'fa-circle': !this.isTowTypeSet,
        'fa-check-circle': this.isTowTypeSet
      };
    },

    canGetPricing () {
      return this.isSubterminalSet && this.isCustomerSet && this.isTowTypeSet;
    },

    canAddPricing () {
      return this.canEdit() && this.canGetPricing;
    },

    // canGetDefaultPricing () {
    //   return this.canGetPricing && this.saleOrderLinesProxy.length === 0;
    // },

    // shouldGetPricingForNewCall () {
    //   return this.isNewCall && !this.call.DefaultPricingRetrieved;
    // },

    dispatchesProxy () {
      let dispatches = this.$_.get(this.call, 'Dispatches', []);

      dispatches.forEach(dispatch => {
        this.$set(dispatch, 'DriverNum', this.getDispatchProperty(dispatch.lDispatchKey, 'DriverNum'));
        this.$set(dispatch, 'TruckNum', this.getDispatchProperty(dispatch.lDispatchKey, 'TruckNum'));

        let driverTruckPair = dispatch.DriverNum
          ? `${dispatch.DriverNum} in truck ${dispatch.TruckNum}`
          : '';

        if (dispatch.bRetow) {
          driverTruckPair = [driverTruckPair, '(Retow)'].join(' ');
        }

        this.$set(dispatch, 'DriverTruckPair', driverTruckPair);
        this.$set(dispatch, 'isEnabled', this.canSelectDispatch(dispatch));
      });

      return dispatches;
    },

    orderLineGroupExpandedIcons () {
      return {
        'far': true,
        'fa-chevron-double-down': !this.$data.orderLineGroupExpanded,
        'fa-chevron-double-up': this.$data.orderLineGroupExpanded
      };
    },

    isCallDispatchComplete () {
      return [
        VALUE_ID.callStatus.dispatched,
        VALUE_ID.callStatus.unassigned
      ].includes(this.$_.get(this.call, 'lCallStatusTypeKey', ''));
    }
  },

  watch: {
    servicesVisible () {
      if (this.$_.isEmpty(this.$data.serviceCategoryTerm)) {
        this.$data.serviceCategoryTerm = this.canSeeAllServices ? 'All Services' : 'Basic Services';
      }

      if (this.$data.servicesVisible) {
        this.getAvailableServices();
      }
    },

    saleOrderLinesProxy: {
      handler () {
        if (this.$data.useSpeedBumps) {
          this.$emit('addSpeedBump', 'sale pricing');
        }
      },
      deep: true
    },

    serviceCategoryTerm () {
      this.getAvailableServices();
    }
  },

  methods: {
    ...mapActions([
      '__state',
      'CALL__getDispatchDetails',
      'TOPSCALL__getOrderPricing',
      'TOPSCALL__getOrderPricingForNew',
      'TOPSCALL__getPricingForNew',
      'TOPSCALL__getServicePricing',
      'TOPSCALL__getAvailableServices',
      'TOPSCALL__getPricingAfterChange',
      'TOPSCALL__getServicePricingForNew',
      'TOPSCALL__getAvailableServicesForNew'
    ]),

    isDispatchPricingType (orderLine) {
      return Number(orderLine.lServicePricingTypeKey) === VALUE_ID.pricingType.dispatch;
    },

    isReconciled (orderLine) {
      return (this.isTowReconciled && !Number(orderLine.bRetow)) || (this.isRetowReconciled && Number(orderLine.bRetow));
    },

    isDispatchReconciled (dispatch) {
      return (this.isTowReconciled && !Number(dispatch.bRetow)) || (this.isRetowReconciled && Number(dispatch.bRetow));
    },

    canEdit (orderLine = null) {
      return false;
      // if (this.isCallConfirmed) {
      //   return Access.has('calls.fullEditAfterConfirmed');
      // }

      // if (orderLine) {
      //   if (this.isDispatchPricingType(orderLine) && this.isReconciled(orderLine)) {
      //     return Access.has('prices.editDispatchAfterReconciled');
      //   }
      // }

      // return true;
    },

    canEditDescription (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editDescription');
    },

    canEditQuantity (orderLine) {
      if (Number(orderLine.scInitialRate) > 0 &&
        orderLine.bInitialFlatRate &&
        Number(orderLine.scSecondaryRate) <= 0) {
        return false;
      }

      return this.canEdit(orderLine) && !Number(orderLine.bCalculated);
    },

    canOverrideTax () {
      return this.canEdit() && Access.has('prices.overrideTax');
    },

    canEditDiscountable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editDiscountable');
    },

    canEditTaxable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editTaxable');
    },

    canEditCommissionable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editCommissionable');
    },

    canEditSurchargeable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editSurchargeable');
    },

    canOverrideRate (orderLine) {
      return this.canEdit(orderLine) && (orderLine.bRatesOverridable || Access.has('prices.overrideRates'));
    },

    canDelete (orderLine) {
      return false;
      // Not using this.canEdit() because of slight difference in handling reconciled calls
      // if (this.isCallConfirmed) {
      //   return Access.has('calls.fullEditAfterConfirmed');
      // }

      // if (!this.isCallDispatchComplete && Access.has('prices.restrictDelete')) return false;

      // if (orderLine) {
      //   if (this.isDispatchPricingType(orderLine) && this.isReconciled(orderLine)) {
      //     return Access.has('prices.editDispatchAfterReconciled');
      //   }
      // }

      // return true;
    },

    canSelectDispatch (dispatch) {
      if (this.isDispatchReconciled(dispatch)) {
        return Access.has('prices.editDispatchAfterReconciled');
      }

      return true;
    },

    toggleServicePicker (value) {
      if (!this.canGetPricing) {
        this.$data.servicesVisible = false;
        return;
      }

      this.$data.servicesVisible = value;

      if (!this.serviceFilterDispatchKey &&
        this.dispatchesProxy.length === 1 &&
        this.dispatchesProxy[0].isEnabled) {
        this.serviceFilterDispatchKey = this.dispatchesProxy[0].lDispatchKey;
      }

      if (this.$data.servicesVisible) {
        // Focus on the filter control
        setTimeout(() => {
          let $serviceFilterControl = document.querySelector('#serviceFilterControl');

          if (this.$data.servicesVisible && $serviceFilterControl) {
            $serviceFilterControl.focus();
            $serviceFilterControl.setSelectionRange(0, -1);
          }
        }, 300);
      } else {
        // Focus on the add service button
        let $addServiceButton = document.querySelector('#salePricingAddService');

        if ($addServiceButton) {
          $addServiceButton.focus();
        }
      }
    },

    isDispatchEditable (orderline) {
      return false;
    },

    async getPricing (fieldProps = null) {
      if (!this.call.lCustomerKey) return;
      if (!this.call.lTowTypeKey) return;
      if (!this.call.lSubterminalKey) return;

      if (this.$_.isEmpty(this.call.dCallTaken)) {
        this.$set(this.call, 'dCallTaken', await this.getNow());
      }

      if (this.isNewCall) {
        this.$set(this.call, 'SaleOrderLines', []);

        this.TOPSCALL__getPricingForNew({
          data: this.call,
          callback: response => {
            const variantOne = this.$_.get(response, 'SaleOrderLines', null);
            const variantTwo = this.$_.get(response, 'OrderLines', null);

            this.$set(this.call, 'SaleOrderLines', variantOne || variantTwo);
            this.$set(this.call, 'DefaultPricingRetrieved', true);

            this.enhanceOrderLines();
            this.calculateOrderPricing();
          }
        });
      } else {
        this.TOPSCALL__getPricingAfterChange({
          fieldId: this.$_.get(fieldProps, 'fieldId', ''),
          oldValue: this.$_.get(fieldProps, 'oldValue', ''),
          newValue: this.$_.get(fieldProps, 'newValue', ''),
          keepExistingItems: this.$_.get(fieldProps, 'keepExistingItems', false),
          data: this.call,
          callback: response => {
            const variantOne = this.$_.get(response, 'SaleOrderLines', null);
            const variantTwo = this.$_.get(response, 'OrderLines', null);

            this.$set(this.call, 'SaleOrderLines', variantOne || variantTwo);

            let changes = this.$_.get(response, 'OrderLineChanges', []);
            if (changes.length) {
              this.$notify.info({
                title: 'Info',
                message: changes.join(', ')
              });
            }

            this.enhanceOrderLines();
            this.calculateOrderPricing();
          }
        });
      }
    },

    removeOrderLine (index) {
      this.call.SaleOrderLines.splice(index, 1);
      this.calculateTotals();
    },

    calculateTotals () {
      this.$nextTick(() => {
        this.$data.subtotal = 0;
        this.$data.taxableSubtotal = 0;
        this.$data.discountableSubtotal = 0;

        this.$_.forEach(this.call.SaleOrderLines, line => {
          if (line.tcTotalPrice) {
            this.$data.subtotal += Number(line.tcTotalPrice);
          }

          if (line.bTaxable) {
            this.$data.taxableSubtotal += Number(line.tcTotalPrice);
          }

          if (line.bDiscountable) {
            this.$data.discountableSubtotal += Number(line.tcTotalPrice);
          }
        });
      });
    },

    getAvailableServices () {
      if (this.isNewCall) {
        this.TOPSCALL__getAvailableServicesForNew({
          towTypeKey: this.call.lTowTypeKey,
          subterminalKey: this.call.lSubterminalKey,
          customerKey: this.call.lCustomerKey,
          callback: response => {
            this.$data.services = response;
          }
        });
      } else {
        this.TOPSCALL__getAvailableServices({
          callKey: this.call.lCallKey,
          orderType: 'S',
          filter: this.$data.serviceCategoryTerm,
          callback: response => {
            this.$data.services = response;
          }
        });
      }
    },

    canAddService (service) {
      if (service.Type === 'Dispatch' && this.dispatchesProxy.length > 0) {
        const dispatch = this.$_.find(this.dispatchesProxy, ['lDispatchKey', this.serviceFilterDispatchKey]);

        if (!dispatch) return false;

        // If tow and not reconciled
        if (!this.isTowReconciled && !Number(dispatch.bRetow)) return true;

        // If retow and not reconciled
        if (!this.isRetowReconciled && Number(dispatch.bRetow)) return true;

        // If reconciled
        return Access.has('prices.editDispatchAfterReconciled');
      }

      return true;
    },

    addService (service) {
      if (!this.canAddService(service)) return;

      this.createIfMissing('SaleOrderLines', []);

      if (this.isNewCall) {
        this.TOPSCALL__getServicePricingForNew({
          towTypeKey: this.call.lTowTypeKey,
          subterminalKey: this.call.lSubterminalKey,
          customerKey: this.call.lCustomerKey,
          serviceKey: service.ServiceKey,
          callback: response => {
            response.lDispatchKey = this.serviceFilterDispatchKey;

            this.call.SaleOrderLines.push(response);

            this.calculateOrderPricing();
          }
        });
      } else {
        this.TOPSCALL__getServicePricing({
          callKey: this.call.lCallKey,
          orderType: 'S',
          serviceKey: service.ServiceKey,
          callback: response => {
            response.lDispatchKey = this.serviceFilterDispatchKey;

            this.call.SaleOrderLines.push(response);

            this.calculateOrderPricing();
          }
        });
      }
    },

    calculateOrderPricing: debounce(function () {
      if ((this.$_.get(this.call, 'SaleOrderLines', [])).length === 0) return;

      if (this.isNewCall) {
        this.TOPSCALL__getOrderPricingForNew({
          towTypeKey: this.call.lTowTypeKey,
          subterminalKey: this.call.lSubterminalKey,
          customerKey: this.call.lCustomerKey,
          taxRateOverride: this.call.fTaxRate_Override,
          discountPercent: this.call.fDiscountPct,
          orderLines: this.call.SaleOrderLines,
          callback: response => {
            const variantOne = this.$_.get(response, 'SaleOrderLines', null);
            const variantTwo = this.$_.get(response, 'OrderLines', null);

            this.$set(this.call, 'SaleOrderLines', variantOne || variantTwo);

            this.priceRepository.sale.TaxRateOverride = response.TaxRateOverride;
            this.priceRepository.sale.TaxRate = response.TaxRate;
            this.priceRepository.sale.TaxTotal = response.TaxTotal;
            this.priceRepository.sale.TaxExempt = response.TaxExempt;

            this.priceRepository.sale.Total = response.Total;
            this.priceRepository.sale.DiscountTotal = response.DiscountTotal;
            this.priceRepository.sale.DiscountPct = response.DiscountPct;

            this.enhanceOrderLines();
            this.calculateTotals();
          }
        });
      } else {
        this.TOPSCALL__getOrderPricing({
          callKey: this.call.lCallKey,
          orderType: 'S',
          taxRateOverride: this.call.fTaxRate_Override,
          orderLines: this.call.SaleOrderLines,
          callback: response => {
            const variantOne = this.$_.get(response, 'SaleOrderLines', null);
            const variantTwo = this.$_.get(response, 'OrderLines', null);

            this.$set(this.call, 'SaleOrderLines', variantOne || variantTwo);

            this.priceRepository.sale.TaxRateOverride = response.TaxRateOverride;
            this.priceRepository.sale.TaxRate = response.TaxRate;
            this.priceRepository.sale.TaxTotal = response.TaxTotal;
            this.priceRepository.sale.TaxExempt = response.TaxExempt;

            this.priceRepository.sale.Total = response.Total;
            this.priceRepository.sale.DiscountTotal = response.DiscountTotal;
            this.priceRepository.sale.DiscountPct = response.DiscountPct;

            this.enhanceOrderLines();
            this.calculateTotals();
          }
        });
      }
    }, 500),

    enforceQuantityRange (orderLine) {
      if (orderLine.pQty < this.getMininumQuantity(orderLine.fMinimumQty)) {
        orderLine.pQty = this.getMininumQuantity(orderLine.fMinimumQty);
      }

      if (orderLine.pQty > this.getMaximumQuantity(orderLine.fMaximumQty)) {
        orderLine.pQty = this.getMaximumQuantity(orderLine.fMaximumQty);
      }
    },

    getMininumQuantity (value) {
      return this.$_.isEmpty(value) ? 0 : Number(value);
    },

    getMaximumQuantity (value) {
      return this.$_.isEmpty(value) ? 99999999 : Number(value);
    },

    enforceRateRange (orderLine, fieldId) {
      if (orderLine[fieldId] < this.getMinimumRate(orderLine.bNoNegativePrice)) {
        orderLine[fieldId] = this.getMinimumRate(orderLine.bNoNegativePrice);
      }

      if (orderLine[fieldId] > this.getMaximumRate(orderLine.bNoNegativePrice)) {
        orderLine[fieldId] = this.getMaximumRate(orderLine.bNoNegativePrice);
      }
    },

    getMinimumRate (allowNegativeValue) {
      if (Access.has('prices.bypassPricingPolarity')) return null;

      // Convert double negative in bNoNegativePrice for easier reading
      // bNoNegativePrice = true means disallow negative value
      // bNoNegativePrice = false means allow negative value
      allowNegativeValue = !allowNegativeValue;

      return allowNegativeValue ? null : 0;
    },

    getMaximumRate (allowPositiveValue) {
      if (Access.has('prices.bypassPricingPolarity')) return null;

      // Convert double negative in bNoNegativePrice for easier reading
      // bNoNegativePrice = true means disallow positive value
      // bNoNegativePrice = false means allow positive value

      return allowPositiveValue ? null : 0;
    },

    enhanceOrderLines () {
      // Use a negative order line key for new items.
      // This is necessary to remove items.
      if (this.call.SaleOrderLines) {
        let minimumKey = Math.min(...this.call.SaleOrderLines.map(line => Number(line.lOrderLineKey))) - 1;

        this.$_.forEach(this.call.SaleOrderLines, line => {
          if (line.lOrderLineKey === '') {
            line.lOrderLineKey = minimumKey;
            minimumKey--;
          }
        });
      }
    },

    afterCallRead () {
      this.enhanceOrderLines();
      this.calculateTotals();

      this.$nextTick(() => {
        this.getDispatchDetails();
      });

      setTimeout(() => {
        this.calculateOrderPricing();
      }, 300);
    },

    getDispatchProperty (dispatchKey, property) {
      let dispatch = this.$_.find(this.$data.dispatchDetails, ['Key', dispatchKey]);

      if (!dispatch) return;

      return this.$_.get(dispatch, property, '');
    },

    getDispatchDetails () {
      if (!this.call.lCallKey) return;

      this.CALL__getDispatchDetails({
        callKey: this.call.lCallKey,
        callback: response => {
          this.$data.dispatchDetails = response;
        }
      });
    },

    dispatchIsVisible (orderLine) {
      return this.dispatchesProxy.length &&
        this.$data.dispatchDetails.length &&
        this.isDispatchPricingType(orderLine);
    },

    toggleOrderLines () {
      this.$data.orderLineGroupExpanded = !this.$data.orderLineGroupExpanded;

      this.$hub.$emit(EVENT_TOGGLE_ACCORDIAN_GROUP, {
        group: 'order-line',
        expanded: this.$data.orderLineGroupExpanded
      });
    }
  },

  mounted () {
    this.$hub.$on(BEFORE_CALL_READ, () => {
      this.$data.useSpeedBumps = false;
    });

    this.$hub.$on(AFTER_CALL_READ, () => {
      setTimeout(() => {
        this.createIfMissing('SaleOrderComments', '');
      }, 300);

      setTimeout(() => {
        this.$data.useSpeedBumps = true;
      }, 2000);
    });

    this.$hub.$on(EVENT_RESET_CALL_PRICING, props => {
      this.getPricing(props);
    });
  },

  beforeDestroy () {
    this.call.lTowTypeKey = null;
  }
};
</script>
