<template>
  <span id="miscellaneous-section">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-6 is-left">
          <app-number v-model="call.tPriority" id="CAL_tPriority" min="1" max="9" :disabled="!canEditProperty()">
            Priority
          </app-number>
        </div>
        <div class="column is-6">
          <PriceGuard :call="call" :isSedated="isNewCall">
            <app-text v-model="call.ch5Zone" id="CAL_ch5Zone" maxlength="5" :disabled="!canEditProperty()">
              Zone
            </app-text>
          </PriceGuard>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="call.vc20PONum" id="CAL_vc20PONum" maxlength="20" :disabled="!canEditProperty()">
            P.O. Number
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.vc20RONum" id="CAL_vc20RONum" maxlength="20" :disabled="!canEditProperty()">
            R.O. Number
          </app-text>
        </div>
        <div class="column is-4 is-left">
          <app-text v-model="call.vc100UserDefined1" id="CAL_vc100UserDefined1" maxlength="100" :disabled="!canEditProperty('vc100UserDefined1')">
            {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef1 }}
          </app-text>
        </div>
        <div class="column is-4">
          <app-text v-model="call.vc20MembershipNum" id="CAL_vc20MembershipNum" maxlength="20" :disabled="!canEditProperty()">
            Membership Number
          </app-text>
        </div>
        <div class="column is-4">
          <app-date-time v-model="call.dExpirationDate" id="CAL_dExpirationDate" :disabled="!canEditProperty()" :now-timestamp="false">
            Membership Expiry
          </app-date-time>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="call.vc50UserDefined2" id="CAL_vc50UserDefined2" maxlength="50" :disabled="!canEditProperty('vc50UserDefined2')">
            {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef2 }}
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.vc50UserDefined3" id="CAL_vc50UserDefined3" maxlength="50" :disabled="!canEditUserDefined3">
            {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef3 }}
          </app-text>
        </div>
        <div class="column is-4 is-left">
          <app-checkbox v-model="call.bMileageRequired" id="CAL_bMileageRequired" :disabled="!canEditProperty()">
            Mileage Required
          </app-checkbox>
        </div>
        <div class="column is-4">
          <app-text v-model="call.vc20PoliceNum" id="CAL_vc20PoliceNum" maxlength="20" :disabled="!canEditPoliceNumber">
            Police Number
          </app-text>
        </div>
        <div class="column is-4">
          <app-text v-model="call.vc10PoliceBeat" id="CAL_vc10PoliceBeat" maxlength="10" :disabled="!canEditProperty('vc10PoliceBeat')">
            Police Beat
          </app-text>
        </div>
      </div> <!-- /columns -->
    </app-grid-form>
  </span>
</template>

<script>
import Access from '@/utils/access.js';
import BaseSection from './BaseSection.vue';
import { mapGetters } from 'vuex';
import AppSelectSimple from '../inputs/SelectSimple.vue';
import PriceGuard from '@/components/features/PriceGuard.vue';
import { CALL_SECTION_MISCELLANEOUS } from '@/config.js';

export default {
  name: 'miscellaneous-section',

  extends: BaseSection,

  components: {
    AppSelectSimple,
    PriceGuard
  },

  data () {
    return {
      sectionName: CALL_SECTION_MISCELLANEOUS
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings']),

    canEditPoliceNumber () {
      return Access.has('calls.editPoliceNumber');
    },

    canEditUserDefined3 () {
      return !Access.has('calls.restrictUserDefined3');
    }
  }
};
</script>
