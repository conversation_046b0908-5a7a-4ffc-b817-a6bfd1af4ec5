/**
 * Creates a new leg object with default values
 * @returns {Object} A leg object with the following properties:
 * - lCall: Call number (number)
 * - tOrder: Order number (number)
 * - sLocation: Starting location (string, max=255)
 * - sDestination: Destination location (string, max=255)
 * - sItemDescription: Description of item being transported (string, max=100)
 * - sNotes: Additional notes (string, max=255)
 * - dStart: Start date/time (ISO string)
 * - dFinish: Finish date/time (ISO string)
 * - lMiles: Miles traveled (number)
 */
export function createLeg () {
  return {
    lCall: null,
    tOrder: null,
    sLocation: '',
    sDestination: '',
    sItemDescription: '',
    sNotes: '',
    dStart: null,
    dFinish: null,
    lMiles: null,
    isJustAdded: true
  };
}

/**
 * Validates a leg object
 * @param {Object} leg The leg object to validate
 * @returns {Object} Object with isValid and errors properties
 */
export function validateLeg (leg) {
  const errors = [];

  // Required fields
  if (!leg.sLocation) {
    errors.push('Location is required');
  }
  if (!leg.sDestination) {
    errors.push('Destination is required');
  }

  // Length constraints
  if (leg.sLocation && leg.sLocation.length > 255) {
    errors.push('Location must be 255 characters or less');
  }
  if (leg.sDestination && leg.sDestination.length > 255) {
    errors.push('Destination must be 255 characters or less');
  }
  if (leg.sItemDescription && leg.sItemDescription.length > 100) {
    errors.push('Description must be 100 characters or less');
  }
  if (leg.sNotes && leg.sNotes.length > 255) {
    errors.push('Notes must be 255 characters or less');
  }

  // Date validation
  if (leg.dFinish && !leg.dStart) {
    errors.push('Start time is required when finish time is set');
  }
  if (leg.dStart && leg.dFinish && new Date(leg.dFinish) < new Date(leg.dStart)) {
    errors.push('Finish time cannot be before start time');
  }

  // Numeric validation
  if (leg.lMiles !== null && (isNaN(leg.lMiles) || leg.lMiles < 0)) {
    errors.push('Miles must be a non-negative number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
