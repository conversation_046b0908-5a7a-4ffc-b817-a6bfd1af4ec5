<template>
  <span class="elapsed-time">{{ formattedTime }}</span>
</template>

<script>
export default {
  name: 'elapsed-time',

  props: {
    startTime: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      now: new Date(),
      timerId: null
    };
  },

  computed: {
    formattedTime () {
      // * Adding 1 hour because the server's time is 1 hour different from the browser's time
      const start = new Date(this.startTime).getTime() + 60 * 60 * 1000;
      const diff = Math.max(this.now - start, 0);

      const hours = Math.floor(diff / 1000 / 60 / 60);
      const minutes = Math.floor((diff / 1000 / 60) % 60);

      return (hours > 0) ? `${hours}h ${minutes}m elapsed` : `${minutes}m elapsed`;
    }
  },

  methods: {
    updateTime () {
      this.now = new Date();
    },

    startTimer () {
      if (this.timerId) return;
      // Update every minute
      this.timerId = setInterval(() => {
        this.updateTime();
      }, 60 * 1000);
      // Initial update
      this.updateTime();
    },

    stopTimer () {
      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }
    }
  },

  mounted () {
    this.startTimer();
  },

  beforeDestroy () {
    this.stopTimer();
  }
};
</script>

<style scoped>
.elapsed-time {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}
</style>
