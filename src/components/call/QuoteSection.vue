<template>
  <span id="subterminal-section">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-12">
          <app-text v-model="call.vc100GivenTo" :required="true">
            Given To
          </app-text>
        </div>
        <div class="column is-12">
          <app-text v-model="call.vc1000Notes">
            Notes
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.vc255EmailTo" :disabled="true">
            Emailed To
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.dEmailSent" :disabled="true">
            Emailed At
          </app-text>
        </div>
        <div class="column is-12">
          <app-data-point label="Email Sent By">
            {{ getUserKey(call.lEmailSentBy) }}
          </app-data-point>
        </div>
        <div class="column is-6">
          <app-data-point label="Created By">
            {{ getUserKey(call.lCreatedBy) }}
          </app-data-point>
        </div>
        <div class="column is-6">
          <app-text v-model="call.dCreated" :disabled="true">
            Created At
          </app-text>
        </div>
        <div class="column is-12" v-if="getUserKey(call.lDeletedBy)">
          <app-data-point label="Deleted By">
            {{ getUserKey(call.lDeletedBy) }}
          </app-data-point>
        </div>
      </div>
    </app-grid-form>
  </span>
</template>

<script>
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';
import { CALL_SECTION_QUOTE } from '@/config.js';

export default {
  name: CALL_SECTION_QUOTE,

  extends: BaseSection,

  data () {
    return {
      customer: {},
      sectionName: CALL_SECTION_QUOTE
    };
  },

  computed: {
    rawUserKeys () {
      let keys = [];

      if (this.call.lEmailSentBy) {
        keys.push(this.call.lEmailSentBy);
      }

      if (this.call.lCreatedBy) {
        keys.push(this.call.lCreatedBy);
      }

      if (this.call.lDeletedBy) {
        keys.push(this.call.lDeletedBy);
      }

      return keys;
    }
  },

  watch: {
    rawUserKeys () {
      this.getUserIds();
    }
  },

  mounted () {
    this.getCustomers();
  },

  methods: {
    ...mapActions([
      'CUSTOMER__readBasic'
    ]),

    getCustomers () {
      if (!this.call.lCustomer) return;

      this.CUSTOMER__readBasic({
        customerKey: this.call.lCustomer,
        callback: response => {
          this.$data.customer = response;
        }
      });
    }
  }
};
</script>
