<template>
  <dialog id="authorities-picker" ref="dialog" @click.self="close">
    <header class="header">
      <i class="icon fad fa-id-badge"></i>
      <label class="is-small is-bold is-uppercase">Authorized callers</label>
    </header>

    <ul class="authorities">
      <li class="authority" v-for="authority in authorities" @click="setAuthority(authority)">
        <div class="name">{{ authority.Name }}</div>
        <div class="phone is-small">{{ authority.Phone }}</div>
      </li>
    </ul>
  </dialog>
</template>

<script>
export default {
  name: 'authorities-picker',

  inject: ['setAuthority'],

  props: {
    authorities: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  data () {
    return {
      callStatuses: []
    };
  },

  methods: {
    open () {
      this.$refs.dialog.showModal();
    },

    close () {
      this.$refs.dialog.close();
    }
  }
};
</script>

<style>
#authorities-picker {
  width: calc(100% - 2rem);
  max-width: 25rem;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .icon {
      margin-right: 0.25rem;
      font-size: var(--font-size-h4);
      color: var(--pure-blue);
    }
  }

  .authority {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;

    &:hover {
      background-color: var(--placeholder-bg);
    }
  }
}
</style>
