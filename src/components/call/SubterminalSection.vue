<template>
  <span id="subterminal-section">
    <app-grid-form context="inline">
      <div class="columns">
        <div class="column is-left is-bottom">
          <PriceGuard :call="call" :isSedated="isNewCall">
            <app-select v-model.number="call.lSubterminalKey" :options="subterminals" keyAlias="Key" valueAlias="Value" id="CAL_lSubterminalKey" @blur="setCustomerFocus" :disabled="!canEdit">
              Company
            </app-select>
          </PriceGuard>
        </div>
      </div>
    </app-grid-form>
  </span>
</template>

<script>
import Access from '@/utils/access.js';
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';
import { CALL_SECTION_SUBTERMINAL } from '@/config.js';
import PriceGuard from '@/components/features/PriceGuard.vue';

export default {
  name: 'subterminal-section',

  extends: BaseSection,

  components: {
    PriceGuard
  },

  props: {
    focusOnReady: { type: Boolean, default: false }
  },

  data () {
    return {
      sectionName: CALL_SECTION_SUBTERMINAL,
      subterminals: []
    };
  },

  computed: {
    canEdit () {
      if (this.hasReconciledPayments) {
        return Access.has('payments.undoAfterReconciled');
      }

      return true;
    }
  },

  mounted () {
    this.getSubterminals();
  },

  methods: {
    ...mapActions(['CALL__getSubterminals']),

    getSubterminals () {
      this.CALL__getSubterminals({
        callback: response => {
          this.subterminals = response;

          setTimeout(() => {
            if (this.quoteMode) {
              if (this.focusOnReady) {
                this.setSubterminalFocus();
              }
            } else if (this.isNewCall) {
              this.setSubterminalFocus();
            }
          }, 1000);
        }
      });
    },

    setSubterminalFocus () {
      const element = document.getElementById('CAL_lSubterminalKey');

      if (element) {
        element.focus();
      }
    },

    setCustomerFocus () {
      const element = document.querySelector('#customerSketch');

      if (element) {
        element.focus();
      }
    }
  }
};
</script>
