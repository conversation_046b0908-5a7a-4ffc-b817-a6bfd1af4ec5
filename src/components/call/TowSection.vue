<template>
  <span id="tow-section">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-6" v-if="!quoteMode">
          <app-date-time v-model="call.dCallTaken" id="CAL_dCallTaken" tabindex="-1" :disabled="!canEditCallTaken">
            Call Taken
          </app-date-time>
        </div>
        <div class="column is-6" v-if="!quoteMode">
          <app-data-point label="Call Taken By">
            {{ getUserKey(call.lUserKey_CallTaken) }}
          </app-data-point>
        </div>
        <div class="column is-6 is-left" v-if="!quoteMode">
          <app-text v-model="call.vc30ContactName" id="CAL_vc30ContactName" :disabled="!canEditProperty()">
            Caller Name
          </app-text>
        </div>
        <div class="column is-6" v-if="!quoteMode">
          <app-text v-model="call.vc20ContactPhoneNum" id="CAL_vc20ContactPhoneNum" :disabled="!canEditProperty()">
            Caller Phone
          </app-text>
        </div>

        <template v-if="!call.lCustomerKey">
          <div class="column is-12 is-left">
            <PriceGuard :call="call" :isSedated="isNewCall">
              <app-customer class="initialize-customer" v-model="call.lControlCustomerKey" :show-ppi-control="true" :show-type="true" :disabled="!canEditCustomer" :required="true" @change="onControlCustomerChange">
                Customer
              </app-customer>
            </PriceGuard>
          </div>
        </template>

        <template v-if="call.lCustomerKey && call.lCustomerKey === call.lControlCustomerKey && !canEditControlCustomer">
          <div class="column is-12 is-left">
            <PriceGuard :call="call" :isSedated="isNewCall">
              <app-customer v-model="call.lCustomerKey" :show-type="true" :required="true" @change="onBillingCustomerChange">
                Customer
              </app-customer>
            </PriceGuard>
          </div>
        </template>

        <template v-if="call.lCustomerKey && call.lCustomerKey === call.lControlCustomerKey && canEditControlCustomer">
          <div class="column is-6 is-left">
            <PriceGuard :call="call" :isSedated="isNewCall">
              <app-customer v-model="call.lCustomerKey" :show-type="true" :required="true" @change="onBillingCustomerChange">
                Customer
              </app-customer>
            </PriceGuard>
          </div>
          <div class="column is-6">
            <PriceGuard :call="call" :isSedated="isNewCall">
              <app-customer v-model="call.lControlCustomerKey" :data-redundant="call.lControlCustomerKey === call.lCustomerKey" :show-ppi-control="true" :show-type="true" @change="onControlCustomerChange">
                Control Customer
              </app-customer>
            </PriceGuard>
          </div>
        </template>

        <template v-if="call.lCustomerKey && call.lCustomerKey !== call.lControlCustomerKey">
          <div class="column is-6 is-left">
            <PriceGuard :call="call" :isSedated="isNewCall">
              <app-customer v-model="call.lCustomerKey" :show-type="true" :required="true" @change="onBillingCustomerChange">
                Customer
              </app-customer>
            </PriceGuard>
          </div>
          <div class="column is-6">
            <PriceGuard :call="call" :isSedated="isNewCall">
              <app-customer v-model="call.lControlCustomerKey" :data-redundant="call.lControlCustomerKey === call.lCustomerKey" :show-ppi-control="canEditControlCustomer" :show-type="true" :disabled="!canEditControlCustomer" @change="onControlCustomerChange">
                Control Customer
              </app-customer>
            </PriceGuard>
          </div>
        </template>

        <div class="column is-12 is-left">
          <label>Tow Location <span class="is-required"><i class="fas fa-circle-small"></i></span></label>
          <div class="field has-addons">
            <p class="control" id="CAL_vc100Location">
              <app-address-suggestor
                v-model="call.vc100Location"
                :disabled="!canEditProperty()"
                @blur="enhanceLocation">
              </app-address-suggestor>
            </p>
            <p class="control" style="width: 25px">
              <a @click.prevent="toggleLocationSettings" tabindex="-1" class="button" :disabled="!canEditProperty()">
                <i :class="locationIconClasses"></i>
              </a>
            </p>
            <p class="control" style="width: 25px">
              <a @click.prevent="toggleLocationNote" tabindex="-1" class="button" :disabled="!canEditProperty()">
                <i class="far fa-sticky-note"></i>
              </a>
            </p>
            <CustomerLocator
              v-if="canLocateCustomer"
              :phone="call.vc20ContactPhoneNum"
              :subcompany-key="subcompanyKeyProxy"
              :call-key="call.lCallKey"
              @on-keep-phone="onKeepPhone"
              @on-keep-position="handleLocationCoordinates"
              @on-keep-location="onKeepLocation" />
          </div>
        </div>
        <div class="column is-12 is-left">
          <label>Tow Destination<span v-if="isLotRequired" class="is-required">&nbsp;<i class="fas fa-circle-small"></i></span></label>
          <div class="field has-addons">
            <p class="control" style="width: 33%">
              <app-shortcode :showLabel="false" :options="lots" v-model="destinationLot" placeholder="Lots..." keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" @change="formatDestinationLot" :disabled="!canEditProperty()"></app-shortcode>
            </p>
            <p class="control">
              <app-address-suggestor
                id="CAL_vc100Destination"
                v-model="call.vc100Destination"
                placeholder="Address"
                :disabled="!canEditProperty()"
                @blur="enhanceDestination">
              </app-address-suggestor>
            </p>
            <p class="control" style="width: 25px">
              <a @click.prevent="toggleDestinationSettings" tabindex="-1" class="button" :disabled="!canEditProperty()">
                <i :class="destinationIconClasses"></i>
              </a>
            </p>
            <p class="control" style="width: 25px" v-if="locationCoordinatesSet && destinationCoordinatesSet">
              <a @click="toggleRouteModal" tabindex="-1" class="button">
                <i class="far fa-route"></i>
              </a>
            </p>
          </div>
        </div>
        <div class="column is-4 is-left">
          <PriceGuard :call="call" :forceReset="true" :isSedated="isNewCall">
            <InputTowType v-model="call.lTowTypeKey" :disabled="!canEditProperty()" :required="true" />
          </PriceGuard>
        </div>
        <div class="column is-4">
          <PriceGuard :call="call" :isSedated="isNewCall">
            <InputReason id="CAL_lReasonTypeKey" v-model="call.lReasonTypeKey" :disabled="!canEditProperty()" />
          </PriceGuard>
        </div>
        <div class="column is-4">
          <PriceGuard :call="call" :isSedated="isNewCall">
            <app-shortcode id="CAL_lTruckTypeKey" v-model="call.lTruckTypeKey" :options="truckTypes" keyAlias="value" valueAlias="description" shortCodeAlias="shortCode" :disabled="!canEditProperty()">
              Truck Type
            </app-shortcode>
          </PriceGuard>
        </div>

        <template v-if="!quoteMode">
          <div class="column is-4 is-left">
            <label>ETA or Appointment</label>
            <span class="select">
              <select v-model="etaPivot" :disabled="!canEditProperty()">
                <option value="appointment">Appointment</option>
                <option value="eta">ETA</option>
              </select>
            </span>
          </div>
          <div class="column is-8">
            <div v-if="etaPivot === 'eta'">
              <app-date-time v-model="call.dETA" id="CAL_dETA" @blur="getEtaTimestamp" :disabled="!canEditProperty()">
                ETA
              </app-date-time>
            </div>
            <div v-if="etaPivot === 'appointment'">
              <app-date-time v-model="call.dAppointment" id="CAL_dAppointment" :disabled="!canEditProperty()">
                Appointment
              </app-date-time>
            </div>
          </div>
        </template>
        <template v-if="quoteMode">
          <div class="column is-12">
            <app-date-time v-model="call.dAppointment" id="CAL_dAppointment" :disabled="!canEditProperty()">
              Appointment
            </app-date-time>
          </div>
        </template>

        <div class="column is-12 is-left">
          <app-text v-model="call.vc50EquipmentRequired" id="CAL_vc50EquipmentRequired" :disabled="!canEditProperty()" maxlength="50">
            Equipment Required
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-textarea v-model="dispatchNotesProxy" id="CAL_vc255DispatchNotes" maxlength="1020" :disabled="!canEditProperty()">
            Dispatch Notes
          </app-textarea>
        </div>
        <div class="column is-12 is-left">
          <app-textarea v-model="call.vc255DriverNotes" id="CAL_vc255DriverNotes" maxlength="255" :disabled="!canEditProperty()">
            Driver Notes
          </app-textarea>
        </div>
        <div class="column is-4 is-left" v-if="!quoteMode">
          <app-checkbox v-model="call.bNoCharge" id="CAL_bNoCharge" :disabled="!canEditProperty()">
            No Charge
          </app-checkbox>
        </div>
        <div class="column is-4" v-if="!quoteMode">
          <app-checkbox v-model="call.bPortalToPortal" id="CAL_bPortalToPortal" :disabled="!canEditProperty()">
            Portal to Portal
          </app-checkbox>
        </div>
        <div class="column is-4" v-if="!quoteMode">
          <app-number v-model="call.fDiscountPct" id="CAL_fDiscountPct" min="0" max="100" :disabled="!canEditDiscounts">
            Discount %
          </app-number>
        </div>
        <div class="number-cluster column is-6 is-left is-bottom" v-if="!quoteMode">
          <transition name="flip">
            <div class="_call-number" @click="showTowTicketNumber" v-if="call.InvoiceNumEqualCallNum">
              <i class="_icon far fa-check"></i>
              <span class="_label">Use Call Number</span>
            </div>
          </transition>
          <transition name="flip">
            <app-text class="_tow-ticket" v-model="call.TowTicketNum" id="CAL_TowTicketNum" @blur="showUsingCallNumber" v-if="!call.InvoiceNumEqualCallNum" :required="isTowTicketRequired">
              Tow Ticket Number
            </app-text>
          </transition>
        </div>
        <div class="column is-6 is-bottom" v-if="!quoteMode">
          <app-checkbox v-model="call.bSecondCommission" id="CAL_bSecondCommission" :disabled="!canEditProperty()">
            Second Commission
          </app-checkbox>
        </div>
      </div> <!-- /columns -->
    </app-grid-form>

    <portal to="modals" v-if="locationSettingsVisible">
      <app-modal :show="locationSettingsVisible" title="Location Settings" @close="toggleLocationSettings" :pad="false">
        <section class="tow-settings">
          <div class="_map">
            <call-map
              v-if="locationCoordinatesSet"
              :center="spatialLocation.center"
              :zoom="spatialLocation.zoom"
              :markers="spatialLocation.markers"
              :call-key="call.lCallKey"
              :is-retow="!!call.lRetowKey"
              @marker-drop="onMarkerDrop">
            </call-map>
            <app-placeholder v-else>Set a valid location to view the map.</app-placeholder>
          </div>

          <app-grid-form context="inline">
            <div class="_details columns is-multiline">
              <div class="column is-3">
                <app-text v-model="call.gcLocationLatitude" id="CAL_gcLocationLatitude" :disabled="!canEditProperty()">
                  Latitude
                </app-text>
              </div>
              <div class="column is-3">
                <app-text v-model="call.gcLocationLongitude" id="CAL_gcLocationLongitude" :disabled="!canEditProperty()">
                  Longitude
                </app-text>
              </div>
              <div class="column is-3">
                <app-checkbox v-model="$store.state.preference.autoGeocodeLocation" :disabled="!canEditProperty()">
                  Auto Geocode
                </app-checkbox>
              </div>
              <div class="column is-3">
                <app-checkbox v-model="$store.state.topsCompany.settings.ShowCityStateForGeocoding" :disabled="!canEditProperty()">
                  Auto City, State
                </app-checkbox>
              </div>
            </div>
          </app-grid-form>

          <div class="_controls">
            <LatLonPasteButton  @update-coords="handleLocationCoordinates"/>
            <app-button v-show="!$store.state.preference.autoGeocodeLocation" @click="geocodeLocation({ verbose: true })" :disabled="!canGeocodelocation">
              Geocode Address
            </app-button>
            <app-button type="primary" @click="toggleLocationSettings">
              Close
            </app-button>
          </div>
        </section>
      </app-modal>
    </portal>

    <portal to="modals" v-if="destinationSettingsVisible">
      <app-modal :show="destinationSettingsVisible" title="Destination Settings" @close="toggleDestinationSettings" :pad="false">
        <section class="tow-settings">
          <div class="_map">
            <call-map
              v-if="destinationCoordinatesSet"
              :center="spatialDestination.center"
              :zoom="spatialDestination.zoom"
              :markers="spatialDestination.markers">
            </call-map>
            <app-placeholder v-else>Set a valid location to view the map.</app-placeholder>
          </div>

          <app-grid-form context="inline">
            <div class="_details columns is-multiline">
              <div class="column is-3">
                <app-text v-model="call.gcDestinationLatitude" id="CAL_gcDestinationLatitude" :disabled="!canEditProperty()">
                  Latitude
                </app-text>
              </div>
              <div class="column is-3">
                <app-text v-model="call.gcDestinationLongitude" id="CAL_gcDestinationLongitude" :disabled="!canEditProperty()">
                  Longitude
                </app-text>
              </div>
              <div class="column is-3">
                <app-checkbox v-model="$store.state.preference.autoGeocodeDestination" :disabled="!canEditProperty()">
                  Auto Geocode
                </app-checkbox>
              </div>
              <div class="column is-3">
                <app-checkbox v-model="$store.state.topsCompany.settings.ShowCityStateForGeocoding" :disabled="!canEditProperty()">
                  Auto City, State
                </app-checkbox>
              </div>
            </div>
          </app-grid-form>

          <div class="_controls">
            <LatLonPasteButton  @update-coords="handleDestinationCoordinates" />
            <app-button v-show="!$store.state.preference.autoGeocodeDestination" @click="geocodeDestination({ verbose: true })" :disabled="!canGeocodeDestination">
              Geocode Address
            </app-button>
            <app-button type="primary" @click="toggleDestinationSettings">
              Close
            </app-button>
          </div>
        </section>
      </app-modal>
    </portal>

    <AuthoritiesPicker :authorities="authorities" ref="authoritiesPicker" />
  </span>
</template>

<script>
import datefns from 'date-fns';
import Access from '@/utils/access.js';
import BaseSection from './BaseSection.vue';
import { fullDateTime } from '@/utils/filters.js';
import { mapActions, mapGetters } from 'vuex';
import AuthoritiesPicker from './AuthoritiesPicker.vue';
import CallMap from '@/components/features/CallMap.vue';
import PriceGuard from '@/components/features/PriceGuard.vue';
import LatLonPasteButton from '@/components/features/LatLonPasteTool.vue';
import CustomerLocator from '@/components/features/customerlocator/Index.vue';
import useTowType from '@/components/inputs/TowType/useTowType';

import { callMixin } from '@/mixins/call_mixin';

import {
  ASSET_PATH,
  CALL_SECTION_TOW,
  EVENT_ERROR,
  VALUE_ID
} from '@/config';

export default {
  name: 'tow-section',

  extends: BaseSection,

  mixins: [callMixin],

  components: {
    LatLonPasteButton,
    CallMap,
    AuthoritiesPicker,
    PriceGuard,
    CustomerLocator
  },

  provide () {
    return {
      setAuthority: this.setAuthority
    };
  },

  data () {
    const towTypeUtils = useTowType();

    return {
      sectionName: CALL_SECTION_TOW,
      towTypeUtils,

      lots: [],
      truckTypes: [],
      subterminal: {},
      infoContent: '',
      etaPivot: 'eta',
      relativeEta: '',
      destinationLot: '',
      towLocationNoteVisible: false,
      locationSettingsVisible: false,
      destinationSettingsVisible: false,

      spatialLocation: {
        center: {
          lat: 0,
          lng: 0
        },
        zoom: 13,
        markers: [{
          key: 'location',
          type: 'location',
          position: {
            lat: 0,
            lng: 0
          },
          clickable: false,
          draggable: true,
          icon: { url: ASSET_PATH + '/car-marker.svg' }
        }]
      },

      spatialDestination: {
        center: {
          lat: 0,
          lng: 0
        },
        zoom: 13,
        markers: [{
          key: 'destination',
          type: 'destination',
          position: {
            lat: 0,
            lng: 0
          },
          clickable: false,
          draggable: false,
          icon: { url: ASSET_PATH + '/checkered-flag-marker.svg' }
        }]
      },

      authorities: []
    };
  },

  computed: {
    ...mapGetters(['__state']),

    subcompanyKeyProxy () {
      return this.call.lSubterminalKey || this.$store.state.topsCompany.settings.SubterminalKey;
    },

    canLocateCustomer () {
      return this.$store.state.topsCompany.settings.bAllowCustomerLocate === '1' &&
        this.canEditProperty() &&
        this.subcompanyKeyProxy;
    },

    rawUserKeys () {
      let keys = [];

      if (this.call.lUserKey_CallTaken) {
        keys.push(this.call.lUserKey_CallTaken);
      }

      return keys;
    },

    canEditDiscounts () {
      return Access.has('calls.editDiscounts');
    },

    canEditCallTaken () {
      if (!this.isNewCall) return false;

      return Access.has('calls.editCallTaken');
    },

    locationCoordinatesSet () {
      return this.call.gcLocationLatitude && this.call.gcLocationLongitude;
    },

    destinationCoordinatesSet () {
      return this.call.gcDestinationLatitude && this.call.gcDestinationLongitude;
    },

    canGeocodelocation () {
      return this.call.vc100Location.length > 4;
    },

    canGeocodeDestination () {
      return this.call.vc100Destination.length > 4;
    },

    canSuggestTowLocations () {
      return this.call.vc100Location.length > 4 &&
        this.subterminal.gcLatitude &&
        this.subterminal.gcLongitude;
    },

    cityState () {
      if (this.__state.topsCompany.settings.vc50MappingCity && this.__state.topsCompany.settings.ch2MappingState) {
        return this.__state.topsCompany.settings.vc50MappingCity + ', ' + this.__state.topsCompany.settings.ch2MappingState;
      }

      if (this.subterminal.vc30City && this.subterminal.ch2StateKey) {
        return this.subterminal.vc30City + ', ' + this.subterminal.ch2StateKey;
      }

      return '';
    },

    shouldAppendCityStateToLocation () {
      if (this.call.vc100Location.includes(',')) return false;
      if (this.call.vc100Location.includes('{')) return false;
      if (this.call.vc100Location.includes(this.cityState)) return false;

      return this.$store.state.topsCompany.settings.ShowCityStateForGeocoding &&
        this.cityState &&
        this.call.vc100Location;
    },

    shouldAppendCityStateToDestination () {
      if (this.call.vc100Destination.includes(',')) return false;
      if (this.call.vc100Destination.includes('{')) return false;
      if (this.call.vc100Destination.includes(this.cityState)) return false;

      return this.$store.state.topsCompany.settings.ShowCityStateForGeocoding &&
        this.cityState &&
        this.call.vc100Destination;
    },

    shouldGeocodeLocation () {
      return this.$store.state.preference.autoGeocodeLocation && this.call.vc100Location.includes(',');
    },

    shouldGeocodeDestination () {
      return this.$store.state.preference.autoGeocodeDestination && this.call.vc100Destination.includes(',');
    },

    locationIconClasses () {
      return {
        'far': !this.locationCoordinatesSet,
        'fas': this.locationCoordinatesSet,
        'fa-map-marker-alt': true,
        'is-complete': this.locationCoordinatesSet
      };
    },

    destinationIconClasses () {
      return {
        'far': !this.destinationCoordinatesSet,
        'fas': this.destinationCoordinatesSet,
        'fa-map-marker-alt': true,
        'is-complete': this.destinationCoordinatesSet
      };
    },

    canEditControlCustomer () {
      return Access.has('calls.editControlCustomer');
    },

    canEditCustomer () {
      if (!this.isTowReconciled) return true;

      return Access.has('calls.editCustomerAfterReconciled');
    },

    isDropoffTowType () {
      if (!this.call || !this.call.lTowTypeKey) return false;

      return this.towTypeUtils.isDropoffTowType(this.call.lTowTypeKey);
    },

    isLotRequired () {
      return this.isDropoffTowType;
    },

    isTowTicketRequired () {
      return this.isDropoffTowType;
    }
  },

  watch: {
    'call.dETA' () {
      if (this.call.dETA) {
        this.call.dAppointment = '';
      }
    },

    'call.dAppointment' () {
      if (this.call.dAppointment) {
        this.call.dETA = '';
      }
    },

    'call.gcLocationLatitude' () {
      const latitude = Number(this.call.gcLocationLatitude);

      this.spatialLocation.markers[0].position.lat = latitude;
      this.spatialLocation.center.lat = latitude;
    },

    'call.gcLocationLongitude' () {
      const longitude = Number(this.call.gcLocationLongitude);

      this.spatialLocation.markers[0].position.lng = longitude;
      this.spatialLocation.center.lng = longitude;
    },

    'call.gcDestinationLatitude' () {
      const latitude = Number(this.call.gcDestinationLatitude);

      this.spatialDestination.markers[0].position.lat = latitude;
      this.spatialDestination.center.lat = latitude;
    },

    'call.gcDestinationLongitude' () {
      const longitude = Number(this.call.gcDestinationLongitude);

      this.spatialDestination.markers[0].position.lng = longitude;
      this.spatialDestination.center.lng = longitude;
    }
  },

  methods: {
    ...mapActions([
      'MAP__getCoordinates',
      'TOPSCOMPANY__getLots',
      'CALL__getFieldOptions',
      'PREFERENCE__setTowSection',
      'CUSTOMER__getRecordViewData',
      'TOPSCALL__getSubcompanyDetails',
      'TOPSCOMPANY__getLotCoordinates'
    ]),

    handleDestinationCoordinates (coords) {
      this.call.gcDestinationLatitude = coords.latitude;
      this.call.gcDestinationLongitude = coords.longitude;
    },

    handleLocationCoordinates (coords) {
      this.call.gcLocationLatitude = coords.latitude;
      this.call.gcLocationLongitude = coords.longitude;
    },

    onKeepPhone (value) {
      this.call.vc20ContactPhoneNum = value;
    },

    onKeepLocation (value) {
      this.call.vc100Location = value.address;
    },

    onMarkerDrop (marker) {
      if (marker.key !== 'location') { return; }

      this.call.gcLocationLatitude = marker.position.lat;
      this.call.gcLocationLongitude = marker.position.lng;
    },

    async onControlCustomerChange (customer) {
      if (Number(customer.TypeKey) === VALUE_ID.customerType.ppi) {
        const billingCustomer = await this.getCustomer(customer.Key);
        this.call.lCustomerKey = billingCustomer.lPPIBillingCustomerKey;
        this.call.vc100Location = `${customer.Address} ${customer.City}, ${customer.State} - ${customer.Name}`;
        this.setAuthority(this.$_.get(customer, 'Authorities', []));
        this.onBillingCustomerChange(billingCustomer);
      } else {
        this.call.lCustomerKey = customer.Key;
        this.onBillingCustomerChange(customer);
      }
    },

    onBillingCustomerChange (customer) {
      this.call.lCustomerTypeKey = customer.TypeKey || customer.lCustomerTypeKey;

      if (this.isNewCall) {
        this.call.lControlCustomerKey = customer.Key || customer.lCustomerKey;
      }

      document.querySelector('#CAL_vc100Location input').focus();
    },

    setAuthority (authority) {
      if (!authority) return;

      if (authority.length > 1) {
        this.authorities = authority;
        this.$refs.authoritiesPicker.open();
        return;
      }

      this.call.vc30ContactName = authority.Name || '';
      this.call.vc20ContactPhoneNum = authority.Phone || '';
      this.$refs.authoritiesPicker.close();
    },

    getCustomer (customerKey) {
      return new Promise(resolve => {
        this.CUSTOMER__getRecordViewData({
          key: customerKey,
          success: response => resolve(response)
        });
      });
    },

    showTowTicketNumber () {
      this.call.InvoiceNumEqualCallNum = false;

      this.$nextTick(() => {
        document.querySelector('#CAL_TowTicketNum').focus();
      });
    },

    showUsingCallNumber () {
      if (this.$_.isEmpty(this.call.TowTicketNum)) {
        this.call.InvoiceNumEqualCallNum = true;
      }
    },

    evaluateETAPivot () {
      this.etaPivot = this.$_.isEmpty(this.call.dAppointment) ? 'eta' : 'appointment';
    },

    async getEtaTimestamp () {
      if (this.call.dETA.length === 0) return;
      if (this.call.dETA.length > 5) return;

      let relativeEta = Number(this.call.dETA);
      let calculatedEta = await this.getNow();

      calculatedEta = datefns.parse(calculatedEta);
      calculatedEta = datefns.addMinutes(calculatedEta, relativeEta);
      calculatedEta = fullDateTime(calculatedEta.toString());

      this.$set(this.call, 'dETA', calculatedEta);
    },

    toggleRouteModal () {
      if (!this.locationCoordinatesSet) return;
      if (!this.destinationCoordinatesSet) return;

      window.open(`https://maps.google.be/maps?saddr=${this.call.gcLocationLatitude},${this.call.gcLocationLongitude}&daddr=${this.call.gcDestinationLatitude},${this.call.gcDestinationLongitude}`);
    },

    toggleDestinationSettings () {
      this.destinationSettingsVisible = !this.destinationSettingsVisible;
    },

    toggleLocationSettings () {
      this.locationSettingsVisible = !this.locationSettingsVisible;
    },

    formatDestinationLot () {
      let lot = this.$_.find(this.lots, ['Key', this.destinationLot]);

      if (lot) {
        this.call.vc100Destination = `{${lot.ShortCode}}`;
        this.getStorageLotCoordinates(lot);
      }
    },

    enhanceLocation () {
      if (this.shouldAppendCityStateToLocation) {
        this.call.vc100Location = this.call.vc100Location + ', ' + this.cityState;
      }

      if (this.shouldGeocodeLocation) {
        this.geocodeLocation();
      }
    },

    enhanceDestination () {
      if (this.shouldAppendCityStateToDestination) {
        this.call.vc100Destination = this.call.vc100Destination + ', ' + this.cityState;
      }

      if (this.shouldGeocodeDestination) {
        this.geocodeDestination();
      }
    },

    getFieldOptions (verb, prop, lMakeKey = '') {
      this.CALL__getFieldOptions({
        verb: verb,
        makeKey: lMakeKey,
        callback: response => {
          this.$_.map(response, option => {
            this.$data[prop].push({
              value: option[0],
              description: option[1],
              shortCode: option[2]
            });
          });
        }
      });
    },

    getCoordinates ({ location, verbose = false }) {
      return new Promise(resolve => {
        this.MAP__getCoordinates({
          location: location,
          callback: response => {
            let approximate = this.$_.get(response, 'Approximation', false);
            let latitude = this.$_.get(response, 'Latitude', '');
            let longitude = this.$_.get(response, 'Longitude', '');

            if (approximate && verbose) {
              this.$hub.$emit(EVENT_ERROR, 'Unable to pinpoint this location.');
            }

            resolve({
              latitude: latitude,
              longitude: longitude,
              approximate: approximate
            });
          }
        });
      });
    },

    async geocodeLocation (verbose = false) {
      const coordinates = await this.getCoordinates({ location: this.call.vc100Location, verbose: verbose });

      if (!coordinates.approximate) {
        this.$set(this.call, 'gcLocationLatitude', coordinates.latitude);
        this.$set(this.call, 'gcLocationLongitude', coordinates.longitude);
      }
    },

    toggleLocationNote () {
      this.towLocationNoteVisible = !this.towLocationNoteVisible;
      this.$prompt('Append a note to the Tow Location.', 'Note', {
        confirmButtonText: 'Append',
        cancelButtonText: 'Cancel'
      }).then(input => {
        this.call.vc100Location += ' – ' + this.$_.trim(input.value);
        document.querySelector('#CAL_vc100Location').focus();
      });
    },

    getStorageLotCoordinates (lot) {
      this.TOPSCOMPANY__getLotCoordinates({
        lotKey: lot.Key,
        callback: response => {
          let latitude = this.$_.get(response, 'Latitude', '');
          let longitude = this.$_.get(response, 'Longitude', '');

          this.$set(this.call, 'gcDestinationLatitude', latitude);
          this.$set(this.call, 'gcDestinationLongitude', longitude);
        }
      });
    },

    async geocodeDestination (verbose = false) {
      const coordinates = await this.getCoordinates({ location: this.call.vc100Destination, verbose: verbose });

      if (!coordinates.approximate) {
        this.$set(this.call, 'gcDestinationLatitude', coordinates.latitude);
        this.$set(this.call, 'gcDestinationLongitude', coordinates.longitude);
      }
    },

    afterCallRead () {
      this.$nextTick(() => {
        this.evaluateETAPivot();

        if (this.call.lSubterminalKey) {
          this.TOPSCALL__getSubcompanyDetails({
            subterminalKey: this.call.lSubterminalKey,
            success: response => {
              this.subterminal = response;
            }
          });
        }
      });
    }
  },

  mounted () {
    this.getFieldOptions('GetTruckTypes', 'truckTypes');

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.lots = response.filter(lot => lot.Active === true);
      }
    });
  }
};
</script>
