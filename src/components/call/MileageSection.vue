<template>
<span :id="sectionName">
  <app-grid-form class="_editor" context="inline">
    <div class="columns is-multiline">

      <div class="column is-4">
        <label>Unloaded</label>
        <div class="field has-addons">
          <p class="control">
            <input v-model="call.fMileageUnloaded" @change="applyRecalculatedPricing" min="0" type="text" class="input" autocomplete="off">
          </p>
          <p class="control width-auto">
            <button @click="setMileageUnloaded" class="button" tabindex="-1" title="Calculate mileage.">
              <i class="far fa-calculator"></i>
            </button>
          </p>
        </div>
      </div>

      <div class="column is-4">
        <label>Loaded</label>
        <div class="field has-addons">
          <p class="control">
            <input v-model="call.fMileageLoaded" @change="applyRecalculatedPricing" min="0" type="text" class="input" autocomplete="off">
          </p>
          <p class="control width-auto">
            <button @click="setMileageLoaded" class="button" tabindex="-1" title="Calculate mileage.">
              <i class="far fa-calculator"></i>
            </button>
          </p>
        </div>
      </div>

      <div class="column is-4">
        <label>Return</label>
        <div class="field has-addons">
          <p class="control">
            <input v-model="call.fMileageReturn" @change="applyRecalculatedPricing" min="0" type="text" class="input" autocomplete="off">
          </p>
          <p class="control width-auto">
            <button @click="setMileageReturn" class="button" tabindex="-1" title="Calculate mileage.">
              <i class="far fa-calculator"></i>
            </button>
          </p>
        </div>
      </div>

    </div>
  </app-grid-form>
</span>
</template>

<script>
import BaseSection from './BaseSection.vue';
import { CALL_SECTION_MILEAGE } from '@/config';

export default {
  name: CALL_SECTION_MILEAGE,

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_MILEAGE
    };
  },

  methods: {
    async setMileageLoaded () {
      const getMileageLoaded = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__calcMileageLoaded', {
            locationLatitude: this.call.gcLocationLatitude,
            locationLongitude: this.call.gcLocationLongitude,
            destinationLatitude: this.call.gcDestinationLatitude,
            destinationLongitude: this.call.gcDestinationLongitude,
            success: (response) => { resolve(response); }
          });
        });
      };

      const response = await getMileageLoaded();
      this.call.fMileageLoaded = response.Value;

      this.applyRecalculatedPricing();
    },

    async setMileageUnloaded () {
      const getMileageUnloaded = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__calcMileageUnloaded', {
            subterminalKey: this.subterminalKey,
            locationLatitude: this.call.gcLocationLatitude,
            locationLongitude: this.call.gcLocationLongitude,
            success: (response) => { resolve(response); }
          });
        });
      };

      const response = await getMileageUnloaded();
      this.call.fMileageUnloaded = response.Value;

      this.applyRecalculatedPricing();
    },

    async setMileageReturn () {
      const getMileageReturn = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__calcMileageReturn', {
            subterminalKey: this.subterminalKey,
            destinationLatitude: this.call.gcDestinationLatitude,
            destinationLongitude: this.call.gcDestinationLongitude,
            success: (response) => { resolve(response); }
          });
        });
      };

      const response = await getMileageReturn();
      this.call.fMileageReturn = response.Value;

      this.applyRecalculatedPricing();
    },

    async applyRecalculatedPricing () {
      const getRecalculatedPricing = () => {
        return new Promise(resolve => {
          this.$store.dispatch('QUOTE__recalculatePricing', {
            call: this.call,
            success: (response) => { resolve(response); }
          });
        });
      };

      if (!this.quoteMode) { return; }

      const response = await getRecalculatedPricing();

      if ('TowOrderLines' in response) {
        this.call.TowOrderLines = response.TowOrderLines;
      }
    }
  },

  mounted () {
    this.createIfMissing('fMileageUnloaded', '');
    this.createIfMissing('fMileageLoaded', '');
    this.createIfMissing('fMileageReturn', '');
  }
};
</script>
