<template>
  <span id="dispatch-section">
    <app-accordian class="_dispatch" v-for="(dispatch, index) in dispatchesProxy" :expand-on-mount="shouldExpandOnMount(index)" :key="dispatch.lDispatchKey">
      <div class="_thumbnail">
        <app-data-point label="Driver Number">{{ getProperty(dispatch.lDispatchKey, 'DriverNum') }}</app-data-point>
        <app-data-point label="Truck Number">{{ getProperty(dispatch.lDispatchKey, 'TruckNum') }}</app-data-point>
        <app-data-point label="Status">{{ getProperty(dispatch.lDispatchKey, 'Status') }}</app-data-point>
      </div>

      <template slot="body">
        <app-grid-form class="_editor" context="inline">
          <div class="columns is-multiline">
            <div class="column is-3">
              <app-shortcode id="DIS_lDriverKey" v-model="dispatch.lDriverKey" :options="drivers" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditDriver(dispatch)">
                Driver Number
              </app-shortcode>
            </div>
            <div class="column is-3">
              <app-shortcode id="DIS_lTruckKey" v-model="dispatch.lTruckKey" :options="trucks" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditProperty()">
                Truck Number
              </app-shortcode>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lTotalMileage" :disabled="!canEditProperty()">
                Mileage
              </app-text>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="Status">
                {{ getProperty(dispatch.lDispatchKey, 'Status') }}
              </app-data-point>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dAssigned" @change="backfill(dispatch, $event)" id="DIS_dAssigned" :disabled="!canEditProperty()">
                Assigned Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'AssignedBy') }}
              </app-data-point>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dDispatched" @change="backfill(dispatch, $event)" id="DIS_dDispatched" :disabled="!canEditProperty()">
                Dispatched Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'DispatchedBy') }}
              </app-data-point>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dAcknowledged" @change="backfill(dispatch, $event)" id="DIS_dAcknowledged" :disabled="!canEditProperty()">
                Acknowledged Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'AcknowledgedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Acknowledged">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dArrived" @change="backfill(dispatch, $event)" id="DIS_dArrived" :disabled="!canEditProperty()">
                Arrived Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'ArrivedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Arrived">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dHooked" @change="backfill(dispatch, $event)" id="DIS_dHooked" :disabled="!canEditProperty()">
                Hooked Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'HookedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Hooked">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dDropped" @change="backfill(dispatch, $event)" id="DIS_dDropped" :disabled="!canEditProperty()">
                Dropped Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'DroppedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Dropped">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dCompleted" @change="backfillAndOpenActions(dispatch, $event)" id="DIS_dCompleted" :disabled="!canEditProperty() || isNewCall">
                Completed Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'CompletedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Completed">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <app-text v-model="dispatch.tcCommission" v-if="canEditCommissionAmounts" :disabled="!canEditProperty()">
                Commissionable Amount
              </app-text>
            </div>
            <div class="column is-6 is-bottom">
              <app-text v-model="dispatch.vc50LoadNum" :disabled="true">
                Load Number
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <span id="data-tags-section">
      <div class="_header">
        <div class="_subheader">System Data Tags</div>
        <app-button class="_add" @click="addTag" :disabled="!canEditSystemDataTags">
          <i class="far fa-plus"></i>
        </app-button>
      </div>

      <app-grid-form context="inline">
        <div class="_tag columns is-multiline" v-for="(tag, index) in systemDataTags" :key="tag.key">
          <div class="column is-5">
            <template v-if="hasDataTag(tag)">
              <app-select v-model="tag.key" :options="dataTags" :id="tag.key" keyAlias="ShortCode" valueAlias="Name" :disabled="!canEditSystemDataTags">
                Tag
              </app-select>
            </template>
            <template v-else>
              <app-text v-model="tag.key" :id="tag.key" :disabled="true">
                Tag
              </app-text>
            </template>
          </div>
          <div class="column is-6">
            <component :is="getValueInput(tag.key)" v-model="tag.value" :id="tag.key" :options="getValueOptions(tag.key)" :disabled="!canEditSystemDataTags || !hasDataTag(tag)">
              Value
            </component>
          </div>
          <div class="_remove column is-1" @click="removeTag(index)" :disabled="!canEditSystemDataTags || !hasDataTag(tag)">
            <i class="far fa-trash-alt"></i>
          </div>
        </div>
      </app-grid-form>
    </span>
  </span>
</template>

<script>
import Access from '@/access.js';
import { mapActions } from 'vuex';
import throttle from 'lodash/throttle';
import BaseSection from './BaseSection.vue';
import { createLeg } from './LegsSection/utils';

import {
  VALUE_ID,
  CALL_SECTION_TOW_DISPATCH,
  EVENT_ALTER_CALL_COMPLETE,
  BEFORE_CALL_READ
} from '@/config.js';

export default {
  name: 'dispatch-section',

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_TOW_DISPATCH,

      trucks: [],
      drivers: [],
      thumbnails: [],
      dataTags: [],
      systemDataTags: [],
      backfillableDates: [
        { order: 0, id: 'DIS_dAssigned', model: 'dAssigned' },
        { order: 1, id: 'DIS_dDispatched', model: 'dDispatched' },
        { order: 2, id: 'DIS_dAcknowledged', model: 'dAcknowledged' },
        { order: 3, id: 'DIS_dArrived', model: 'dArrived' },
        { order: 4, id: 'DIS_dHooked', model: 'dHooked' },
        { order: 5, id: 'DIS_dDropped', model: 'dDropped' },
        { order: 6, id: 'DIS_dCompleted', model: 'dCompleted' }
      ]
    };
  },

  computed: {
    canEditSystemDataTags () {
      return Access.has('calls.editSystemDataTags') && this.canEditProperty();
    },

    canEditCommissionAmounts () {
      return Access.has('dispatches.editCommissionAmounts');
    },

    dispatchesProxy () {
      let dispatches = this.$_.get(this.call, 'Dispatches', []);

      return this.$_.filter(dispatches, ['bRetow', false]);
    },

    isFinalDispatch () {
      let totalDispatches = this.dispatchesProxy.length;
      let completedDispatches = this.$_.filter(this.dispatchesProxy, ['lDispatchStatusTypeKey', VALUE_ID.dispatchStatus.completed]).length;

      return (totalDispatches - completedDispatches) === 1;
    }
  },

  watch: {
    systemDataTags: {
      deep: true,
      handler: throttle(function () {
        this.flattenSystemDataTags();
      }, 500)
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getTrucks',
      'TOPSCOMPANY__getDrivers',
      'TOPSCOMPANY__getSystemDataTags',

      'CALL__getDispatchDetails'
    ]),

    getValueInput (tagKey) {
      let targetTag = this.$_.find(this.$data.dataTags, ['ShortCode', tagKey]);

      if (!targetTag) return 'app-text';

      let options = this.$_.get(targetTag, 'Options', []);

      return options.length ? 'app-select-simple' : 'app-text';
    },

    getValueOptions (tagKey) {
      let targetTag = this.$_.find(this.$data.dataTags, ['ShortCode', tagKey]);

      return this.$_.get(targetTag, 'Options', []);
    },

    expandSystemDataTags () {
      if (this.$_.get(this.call, 'vc255DelimitedInfo', '').length < 3) return;

      let pairs = this.$_.split(this.call.vc255DelimitedInfo, ';');
      let tags = [];

      pairs.forEach(pair => {
        let [key, value] = [...this.$_.split(pair, '=')];

        tags.push({
          key: key,
          value: value
        });
      });

      this.$data.systemDataTags = tags;
    },

    flattenSystemDataTags () {
      let pairs = [];

      this.$data.systemDataTags.forEach(item => {
        pairs.push([item.key, item.value].join('='));
      });

      this.call.vc255DelimitedInfo = pairs.join(';');
    },

    addTag () {
      this.$data.systemDataTags.push({
        key: '',
        value: ''
      });
    },

    removeTag (index) {
      this.$data.systemDataTags.splice(index, 1);
    },

    getDispatchDetails () {
      this.CALL__getDispatchDetails({
        callKey: this.call.lCallKey,
        callback: response => {
          this.$data.thumbnails = response;
        }
      });
    },

    afterCallRead () {
      this.$nextTick(() => {
        this.getDispatchDetails();
        this.expandSystemDataTags();
      });
    },

    getProperty (dispatchKey, property) {
      let dispatch = this.$_.find(this.$data.thumbnails, ['Key', dispatchKey]);

      if (!dispatch) return;

      return this.$_.get(dispatch, property, '');
    },

    getDrivers () {
      this.TOPSCOMPANY__getDrivers({
        callback: response => {
          this.$data.drivers = response;
        }
      });
    },

    getTrucks () {
      this.TOPSCOMPANY__getTrucks({
        callback: response => {
          this.$data.trucks = response;
        }
      });
    },

    backfill (dispatch, input) {
      const watermark = this.backfillableDates.find(date => date.id === input.id);
      const datesBelowWatermark = this.backfillableDates.filter(date => date.order < watermark.order);

      datesBelowWatermark.forEach(date => {
        if (!dispatch[date.model]) {
          dispatch[date.model] = input.value;
        }
      });
    },

    shouldTriggerCompletionProcess (dispatch) {
      return this.isFinalDispatch && !!dispatch.dCompleted;
    },

    async backfillAndOpenActions (dispatch, input) {
      this.backfill(dispatch, input);

      if (!this.shouldTriggerCompletionProcess(dispatch)) return;

      document.querySelector('#DIS_dCompleted').blur();

      this.$hub.$emit(EVENT_ALTER_CALL_COMPLETE, {
        dispatch,
        mutations: [
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Assigned',
            value: dispatch.dAssigned
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Dispatched',
            value: dispatch.dDispatched
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Acknowledged',
            value: dispatch.dAcknowledged
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Arrived',
            value: dispatch.dArrived
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Hooked',
            value: dispatch.dHooked
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Dropped',
            value: dispatch.dDropped
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Completed',
            value: dispatch.dCompleted
          }
        ]
      });
    },

    canEditDriver (dispatch) {
      if (this.isNewCall) return true;
      if (!dispatch.bRetow && this.call.lCallStatusTypeKey === VALUE_ID.callStatus.dispatched) return true;
      if (dispatch.bRetow && this.call.lCallStatusTypeKey === VALUE_ID.callStatus.retowDispatch) return true;

      return false;
    },

    shouldExpandOnMount (index) {
      return this.dispatchesProxy.length === 1 && index === 0;
    },

    hasDataTag (tag) {
      if (!tag.key) return true;

      return this.$_.find(this.$data.dataTags, ['ShortCode', tag.key]);
    }
  },

  mounted () {
    this.getDrivers();
    this.getTrucks();

    this.$hub.$on(BEFORE_CALL_READ, () => {
      this.$set(this, 'systemDataTags', []);
    });

    this.TOPSCOMPANY__getSystemDataTags({
      success: response => {
        this.$data.dataTags = response;
      }
    });
  }
};
</script>
