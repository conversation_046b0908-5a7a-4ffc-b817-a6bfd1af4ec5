<template>
  <span id="retow-section" v-if="call.Retow">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-4">
          <app-date-time v-model="call.Retow.dCallTaken" id="retow.RET_dCallTaken"  :disabled="!canEditProperty()">
            Retow Call Taken
          </app-date-time>
        </div>
        <div class="column is-4">
          <app-username v-model="call.Retow.lUserKey_CallTaken">
            Taken By
          </app-username>
        </div>
        <div class="column is-4">
          <app-checkbox v-model="call.Retow.bSecondCommission" id="RET_bSecondCommission" :disabled="!canEditProperty()">
            Second Commission
          </app-checkbox>
        </div>
        <div class="column is-6 is-left">
          <label>Caller Name</label>
          <input v-model="call.Retow.vc30ContactName" type="text" class="input" id="RET_vc30ContactName" autocomplete="off" :disabled="!canEditProperty()" />
        </div>
        <div class="column is-6">
          <label>Caller Phone</label>
          <input v-model="call.Retow.vc20ContactPhoneNum" type="text" class="input" id="RET_vc20ContactPhoneNum" autocomplete="off" :disabled="!canEditProperty()" />
        </div>
        <div class="column is-4 is-left">
          <label>ETA or Appointment</label>
          <span class="select">
            <select v-model="etaPivot" :disabled="!canEditProperty()">
              <option value="appointment">Appointment</option>
              <option value="eta">ETA</option>
            </select>
          </span>
        </div>
        <div class="column is-8">
          <div v-if="etaPivot === 'eta'">
            <app-date-time v-model="call.Retow.dETA" id="RET_dETA" :disabled="!canEditProperty()">
              ETA
            </app-date-time>
          </div>
          <div v-if="etaPivot === 'appointment'">
            <app-date-time v-model="call.Retow.dAppointment" id="RET_dAppointment"  :disabled="!canEditProperty()">
              Appointment
            </app-date-time>
          </div>
        </div>
        <div class="column is-12 is-left">
          <label>Retow Location <span class="is-required"><i class="fas fa-circle-small"></i></span></label>
          <div class="field has-addons">
            <p class="control">
              <app-address-suggestor
                id="RET_vc100Location"
                v-model="call.Retow.vc100Location"
                :disabled="!canEditProperty()"
                @blur="enhanceLocation">
              </app-address-suggestor>
            </p>
            <p class="control width-auto">
              <a @click="toggleLocationSettings" tabindex="-1" class="button">
                <i :class="locationIconClasses"></i>
              </a>
            </p>
          </div>
        </div>
        <div class="column is-12 is-left">
          <label>Retow Destination <span class="is-required"><i class="fas fa-circle-small"></i></span></label>
          <div class="field has-addons">
            <p class="control" style="width: 33%">
              <app-shortcode :showLabel="false" :options="lots" v-model="destinationLot" placeholder="Lots..." keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" @change="formatDestinationLot" :disabled="!canEditProperty()"></app-shortcode>
            </p>
            <p class="control">
              <app-address-suggestor
                id="RET_vc100Destination"
                v-model="call.Retow.vc100Destination"
                placeholder="Address"
                :disabled="!canEditProperty()"
                @blur="enhanceDestination">
              </app-address-suggestor>
            </p>
            <p class="control width-auto">
              <a @click="toggleDestinationSettings" tabindex="-1" class="button">
                <i :class="destinationIconClasses"></i>
              </a>
            </p>
          </div>
        </div>
        <div class="column is-12 is-left">
          <app-textarea v-model="call.Retow.vc255Notes" :maxlength="255" id="RET_vc255Notes" :disabled="!canEditProperty()">
            Retow Notes
          </app-textarea>
        </div>
      </div> <!-- /columns -->

      <portal to="modals" v-if="locationSettingsVisible">
        <app-modal :show="locationSettingsVisible" title="Location Settings" @close="toggleLocationSettings" :pad="false">
          <section class="tow-settings">
            <div class="_map">
              <gmap-map v-if="locationCoordinatesSet" :center="locationCenter" :zoom="13" style="width: 100%; height: 100%">
                <gmap-marker :key="12345" :position="locationMarker.position" :icon="locationMarker.icon" :clickable="false" :draggable="false"></gmap-marker>
              </gmap-map>
              <app-placeholder v-else>Set a valid location to view the map.</app-placeholder>
            </div>

            <app-grid-form context="inline">
              <div class="_details columns is-multiline">
                <div class="column is-3">
                  <app-text v-model="call.Retow.gcLocationLatitude" id="RET_gcLocationLatitude" :disabled="!canEditProperty()">
                    Latitude
                  </app-text>
                </div>
                <div class="column is-3">
                  <app-text v-model="call.Retow.gcLocationLongitude" id="RET_gcLocationLongitude" :disabled="!canEditProperty()">
                    Longitude
                  </app-text>
                </div>
                <div class="column is-3">
                  <app-checkbox v-model="autoGeocodeLocation" :disabled="!canEditProperty()">
                    Geocode
                  </app-checkbox>
                </div>
                <div class="column is-3">
                  <app-checkbox v-model="autoAddCityStateToLocation" :disabled="!canEditProperty()">
                    Add City &amp; State
                  </app-checkbox>
                </div>
              </div>
            </app-grid-form>

            <div class="_controls">
              <app-button @click="toggleLocationSettings">
                Close
              </app-button>
              <app-button type="primary" @click="geocodeLocation" :disabled="!canGeocodeLocation">
                Geocode
              </app-button>
            </div>
          </section>
        </app-modal>
      </portal>

      <portal to="modals" v-if="destinationSettingsVisible">
        <app-modal :show="destinationSettingsVisible" title="Destination Settings" @close="toggleDestinationSettings" :pad="false">
          <section class="tow-settings">
            <div class="_map">
              <gmap-map v-if="destinationCoordinatesSet" :center="destinationCenter" :zoom="13" style="width: 100%; height: 100%">
                <gmap-marker :key="12346" :position="destinationMarker.position" :icon="destinationMarker.icon" :clickable="false" :draggable="false"></gmap-marker>
              </gmap-map>
              <app-placeholder v-else>Set a valid location to view the map.</app-placeholder>
            </div>

            <app-grid-form context="inline">
              <div class="columns is-multiline">
                <div class="column is-3">
                  <label>Latitude</label>
                  <input v-model="call.Retow.gcDestinationLatitude" id="RET_gcDestinationLatitude" class="input" autocomplete="off" :disabled="!canEditProperty()" />
                </div>
                <div class="column is-3">
                  <label>Longitude</label>
                  <input v-model="call.Retow.gcDestinationLongitude" id="RET_gcDestinationLongitude" class="input" autocomplete="off" :disabled="!canEditProperty()" />
                </div>
                <div class="column is-3">
                  <app-checkbox v-model="autoGeocodeDestination" :disabled="!canEditProperty()">
                    Geocode
                  </app-checkbox>
                </div>
                <div class="column is-3">
                  <app-checkbox v-model="autoAddCityStateToDestination" :disabled="!canEditProperty()">
                    Add City &amp; State
                  </app-checkbox>
                </div>
              </div>
            </app-grid-form>

            <div class="_controls">
              <app-button @click="toggleDestinationSettings">
                Close
              </app-button>
              <app-button type="primary" @click="geocodeDestination" :disabled="!canGeocodeDestination">
                Geocode
              </app-button>
            </div>
          </section>
        </app-modal>
      </portal>
    </app-grid-form>
  </span>
</template>

<script>
import is from 'is_js';
import BaseSection from './BaseSection.vue';
import { fullDateTime } from '@/filters.js';
import { mapActions, mapGetters } from 'vuex';
import { CALL_SECTION_RETOW, EVENT_ERROR } from '@/config.js';
import UsernameShow from '@/components/inputs/UsernameShow.vue';

export default {
  name: 'retow-section',

  extends: BaseSection,

  components: {
    UsernameShow
  },

  data () {
    return {
      lots: [],
      etaPivot: 'eta',
      destinationLot: '',
      autoGeocodeLocation: true,
      autoGeocodeDestination: true,
      towLocationNoteVisible: false,
      locationSettingsVisible: false,
      sectionName: CALL_SECTION_RETOW,
      autoAddCityStateToLocation: true,
      destinationSettingsVisible: false,
      autoAddCityStateToDestination: true
    };
  },

  computed: {
    ...mapGetters(['__state']),

    locationCoordinatesSet () {
      return !!this.call.Retow.gcLocationLatitude && !!this.call.Retow.gcLocationLongitude;
    },

    destinationCoordinatesSet () {
      return !!this.call.Retow.gcDestinationLatitude && !!this.call.Retow.gcDestinationLongitude;
    },

    canGeocodeLocation () {
      return this.call.Retow.vc100Location.length > 4;
    },

    canGeocodeDestination () {
      return this.call.Retow.vc100Destination.length > 4;
    },

    canSuggestTowLocations () {
      return is.all.truthy([
        this.call.Retow.vc100Location.length > 4,
        this.$data.subterminal.gcLatitude,
        this.$data.subterminal.gcLongitude
      ]);
    },

    locationCenter () {
      return {
        lat: Number(this.$_.get(this.call.Retow, 'gcLocationLatitude', 0)),
        lng: Number(this.$_.get(this.call.Retow, 'gcLocationLongitude', 0))
      };
    },

    destinationCenter () {
      return {
        lat: Number(this.$_.get(this.call.Retow, 'gcDestinationLatitude', 0)),
        lng: Number(this.$_.get(this.call.Retow, 'gcDestinationLongitude', 0))
      };
    },

    locationMarker () {
      return {
        position: {
          lat: Number(this.$_.get(this.call.Retow, 'gcLocationLatitude', 0)),
          lng: Number(this.$_.get(this.call.Retow, 'gcLocationLongitude', 0))
        },
        icon: { url: '/static/car.png' }
      };
    },

    destinationMarker () {
      return {
        position: {
          lat: Number(this.$_.get(this.call.Retow, 'gcDestinationLatitude', 0)),
          lng: Number(this.$_.get(this.call.Retow, 'gcDestinationLongitude', 0))
        },
        icon: { url: '/static/finish.png' }
      };
    },

    cityState () {
      if (is.all.truthy([
        this.__state.topsCompany.settings.vc50MappingCity,
        this.__state.topsCompany.settings.ch2MappingState
      ])) {
        return this.__state.topsCompany.settings.vc50MappingCity + ', ' + this.__state.topsCompany.settings.ch2MappingState;
      }

      return '';
    },

    shouldAppendCityStateToLocation () {
      if (is.any.truthy([
        is.include(this.call.Retow.vc100Location, ','),
        is.include(this.call.Retow.vc100Location, '{'),
        is.include(this.call.Retow.vc100Location, this.cityState)
      ])) {
        return false;
      };

      return is.all.truthy([
        this.$data.autoAddCityStateToLocation,
        this.cityState,
        this.call.Retow.vc100Location
      ]);
    },

    shouldAppendCityStateToDestination () {
      if (is.any.truthy([
        is.include(this.call.Retow.vc100Destination, ','),
        is.include(this.call.Retow.vc100Destination, '{'),
        is.include(this.call.Retow.vc100Destination, this.cityState)
      ])) {
        return false;
      };

      return is.all.truthy([
        this.$data.autoAddCityStateToDestination,
        this.cityState,
        this.call.Retow.vc100Destination
      ]);
    },

    shouldGeocodeLocation () {
      return is.all.truthy([
        this.$data.autoGeocodeLocation,
        is.include(this.call.Retow.vc100Location, ',')
      ]);
    },

    shouldGeocodeDestination () {
      return is.all.truthy([
        this.$data.autoGeocodeDestination,
        is.include(this.call.Retow.vc100Destination, ',')
      ]);
    },

    locationIconClasses () {
      return {
        'fal': !this.locationCoordinatesSet,
        'fas': this.locationCoordinatesSet,
        'fa-map-marker-alt': true,
        'is-complete': this.locationCoordinatesSet
      };
    },

    destinationIconClasses () {
      return {
        'fal': !this.destinationCoordinatesSet,
        'fas': this.destinationCoordinatesSet,
        'fa-map-marker-alt': true,
        'is-complete': this.destinationCoordinatesSet
      };
    }
  },

  methods: {
    ...mapActions([
      'MAP__getCoordinates',
      'TOPSCOMPANY__getLots',
      'TOPSCOMPANY__getLotCoordinates',
      'PREFERENCE__setTowSection'
    ]),

    toggleDestinationSettings () {
      this.$data.destinationSettingsVisible = !this.$data.destinationSettingsVisible;
    },

    toggleLocationSettings () {
      this.$data.locationSettingsVisible = !this.$data.locationSettingsVisible;
    },

    enhanceLocation () {
      if (this.shouldAppendCityStateToLocation) {
        this.call.Retow.vc100Location = this.call.Retow.vc100Location + ', ' + this.cityState;
      }

      if (this.shouldGeocodeLocation) {
        this.geocodeLocation();
      }
    },

    formatDestinationLot () {
      let lot = this.$_.find(this.$data.lots, ['Key', this.$data.destinationLot]);

      if (lot) {
        this.call.Retow.vc100Destination = `{${lot.ShortCode}}`;
        this.getStorageLotCoordinates(lot);
      }
    },

    getStorageLotCoordinates (lot) {
      this.TOPSCOMPANY__getLotCoordinates({
        lotKey: lot.Key,
        callback: response => {
          let latitude = this.$_.get(response, 'Latitude', '');
          let longitude = this.$_.get(response, 'Longitude', '');

          this.$set(this.call.Retow, 'gcDestinationLatitude', latitude);
          this.$set(this.call.Retow, 'gcDestinationLongitude', longitude);
        }
      });
    },

    enhanceDestination () {
      if (this.shouldAppendCityStateToDestination) {
        this.call.Retow.vc100Destination = this.call.Retow.vc100Destination + ', ' + this.cityState;
      }

      if (this.shouldGeocodeDestination) {
        this.geocodeDestination();
      }
    },

    getCoordinates ({ location, verbose = false }) {
      return new Promise(resolve => {
        this.MAP__getCoordinates({
          location: location,
          callback: response => {
            let approximate = this.$_.get(response, 'Approximation', false);
            let latitude = this.$_.get(response, 'Latitude', '');
            let longitude = this.$_.get(response, 'Longitude', '');

            if (approximate && verbose) {
              this.$hub.$emit(EVENT_ERROR, 'Unable to pinpoint this location.');
            }

            resolve({
              latitude: latitude,
              longitude: longitude,
              approximate: approximate
            });
          }
        });
      });
    },

    async geocodeLocation (verbose = false) {
      const coordinates = await this.getCoordinates({ location: this.call.Retow.vc100Location, verbose: verbose });

      if (!coordinates.approximate) {
        this.$set(this.call.Retow, 'gcLocationLatitude', coordinates.latitude);
        this.$set(this.call.Retow, 'gcLocationLongitude', coordinates.longitude);
      }
    },

    async geocodeDestination (verbose = false) {
      const coordinates = await this.getCoordinates({ location: this.call.Retow.vc100Destination, verbose: verbose });

      if (!coordinates.approximate) {
        this.$set(this.call.Retow, 'gcDestinationLatitude', coordinates.latitude);
        this.$set(this.call.Retow, 'gcDestinationLongitude', coordinates.longitude);
      }
    },

    evaluateETAPivot () {
      this.$data.etaPivot = this.$_.isEmpty(this.call.Retow.dAppointment) ? 'eta' : 'appointment';
    },

    setDefaults () {
      if (this.$_.isEmpty(this.call.Retow.dCallTaken)) {
        this.call.Retow.dCallTaken = fullDateTime(this.$data.now);
      }

      if (this.$_.isEmpty(this.call.Retow.vc100Location)) {
        let lot = this.$_.get(this.call, 'Inventory.vc15LotLocation', '');
        let destination = this.$_.get(this.call, 'vc100Destination', '');

        this.call.Retow.vc100Location = lot || destination;
      }
    },

    afterCallRead () {
      this.evaluateETAPivot();
      this.setDefaults();
    }
  },

  mounted () {
    this.$data.autoGeocodeLocation = this.__state.preference.autoGeocodeLocation;
    this.$data.autoGeocodeDestination = this.__state.preference.autoGeocodeDestination;
    this.$data.autoAddCityStateToLocation = this.__state.preference.autoAddCityStateToLocation;
    this.$data.autoAddCityStateToDestination = this.__state.preference.autoAddCityStateToDestination;

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.$data.lots = response;
      }
    });
  }
};
</script>
