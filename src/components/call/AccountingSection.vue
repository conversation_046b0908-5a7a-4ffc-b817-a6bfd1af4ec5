<template>
  <span id="accounting-section">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-6 is-left">
          <app-date-time v-model="call.dAcctReconcil" id="CAL_dAcctReconcil" :disabled="true">
            Reconciled Date
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="call.sAcctReconcilBy" id="CAL_sAcctReconcilBy" :disabled="true">
            Reconciled By
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="call.dRetowAcctReconcil" id="CAL_dRetowAcctReconcil" :disabled="true">
            Retow Reconciled Date
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="call.sRetowAcctReconcilBy" id="CAL_sRetowAcctReconcilBy" :disabled="true">
            Retow Reconciled By
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="call.dAccConfirm" id="CAL_dAccConfirm" :disabled="true">
            Confirmed Date
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="call.sAcctConfirmBy" id="CAL_sAcctConfirmBy" :disabled="true">
            Confirmed By
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-textarea v-model="call.vc255AccountingNotes" id="CAL_vc255AccountingNotes" :maxlength="255" :disabled="!canEditNotes">
            Notes
          </app-textarea>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="call.dCompletionDate" id="CAL_dCompletionDate" :disabled="true">
            Call Completed Date
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="call.dRevenueDate" id="CAL_dRevenueDate" :disabled="true">
            Call Revenue Date
          </app-text>
        </div>

        <div class="_header column is-12 is-left">
          <div class="_subheader">Collections</div>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="call.tcCollectionAmount" id="CAL_tcCollectionAmount" :disabled="!canEditAccounting">
            Amount to Collect
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.tcCollectionAmountCollected" id="CAL_tcCollectionAmountCollected" :disabled="!canEditAccounting">
            Amount Collected
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-date-time v-model="call.dCollectionDateSent" id="CAL_dCollectionDateSent" :disabled="!canEditAccounting">
            Date Sent
          </app-date-time>
        </div>
        <div class="column is-12 is-left is-bottom">
          <app-text v-model="call.vc255CollectionNotes" id="CAL_vc255CollectionNotes" :disabled="!canEditAccounting">
            Collection Notes
          </app-text>
        </div>
      </div>
    </app-grid-form>
  </span>
</template>

<script>
import Access from '@/utils/access.js';
import BaseSection from './BaseSection.vue';
import { CALL_SECTION_ACCOUNTING } from '@/config.js';

export default {
  name: 'accounting-section',

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_ACCOUNTING
    };
  },

  computed: {
    canEditNotes () {
      return Access.has('calls.accountingNotes');
    },

    canEditAccounting () {
      return !Access.has('calls.restrictAccounting');
    }
  }
};
</script>
