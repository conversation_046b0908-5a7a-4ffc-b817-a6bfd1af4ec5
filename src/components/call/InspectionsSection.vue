<template>
  <div id="inspections-section">
    <section class="inspections__form" v-if="sortedInspectionItems.length > 0">
      <app-grid-form context="inline">
        <div class="columns is-multiline">
          <div v-for="(item, index) in sortedInspectionItems" class="column is-12 is-left" :key="index">
            <div class="inspections__item-group">
              <component
                class="inspections__item-input"
                v-model="item.vc255Value"
                :id="'id-' + item.lItemKey"
                :is="controlType(item)"
                :options="property(item, 'PossibleValues')"
                :required="property(item, 'Required')"
                translateTrue="translateTrue(item)"
                translateFalse="translateFalse(item)"
                :disabled="!canEdit"
                keyAlias="Value"
                valueAlias="Value"
                activeAlias="Active">
                {{ property(item, 'Description') }}
              </component>
              <button @click.prevent="remove(index)" class="inspections__item-remove button" :disabled="!canEdit" tabindex="-1"><i class="far fa-trash-alt"></i></button>
            </div>
          </div>
        </div>
      </app-grid-form>
    </section>
    <app-placeholder v-else>No inspection items have been added.</app-placeholder>

    <footer class="item-pool">
      <app-dropdown :is-up="true">
        <button id="inspection.add" class="button is-small" aria-haspopup="true" aria-controls="dropdown-menu">
          <span>Add Item</span>
          <span class="icon is-small">
            <i class="fal fa-angle-down" aria-hidden="true"></i>
          </span>
        </button>

        <template slot="menu">
          <div v-for="(item, index) in sortedInspectionItemsProxy" @click="add(item)" class="dropdown-item" :key="index">{{ item.Description }}</div>
        </template>
      </app-dropdown>
    </footer>
  </div>
</template>

<script>
import Access from '@/access.js';
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

import {
  VALUE_ID,
  CALL_SECTION_INSPECTIONS,
  EVENT_TOGGLE_CALL_SECTION,
  BEFORE_CALL_READ
} from '@/config.js';

export default {
  name: 'inspection-section',

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_INSPECTIONS,

      itemPool: [],
      itemPoolVisible: false
    };
  },

  computed: {
    canEdit () {
      return Access.has('inspections.edit');
    },

    inspectionItemsProxy: {
      get () {
        return this.$_.get(this.call, 'InspectionItems', []);
      },
      set (value) {
        this.call.InspectionItems = value;
      }
    },

    sortedInspectionItemsProxy () {
      let filteredPool = this.$_.reject(this.$data.itemPool, item => {
        let target = this.$_.find(this.inspectionItemsProxy, ['lItemKey', item.Key]);

        return !this.$_.isEmpty(target);
      });

      return this.$_.orderBy(filteredPool, ['Order']);
    },

    sortedInspectionItems () {
      return this.$_.orderBy(this.inspectionItemsProxy, ['poolOrder']);
    }
  },

  methods: {
    ...mapActions(['TOPSCOMPANY__getCallInspectionItems']),

    itemShape (item) {
      return this.$_.find(this.$data.itemPool, ['Key', item.lItemKey]);
    },

    controlType (item) {
      switch (this.property(item, 'ValueTypeKey')) {
        case VALUE_ID.inspectionValueType.suggestions:
          return 'app-suggestion';
        case VALUE_ID.inspectionValueType.dateTime:
          return 'app-date-time';
        case VALUE_ID.inspectionValueType.number:
          return 'app-number';
        case VALUE_ID.inspectionValueType.select:
          return 'app-select';
        case VALUE_ID.inspectionValueType.trueFalse:
          return 'app-truefalse';
        case VALUE_ID.inspectionValueType.yesNo:
          return 'app-yesno';
        // @TODO
        // Not currently used
        case VALUE_ID.inspectionValueType.multiSelect:
        case VALUE_ID.inspectionValueType.text:
        default:
          return 'app-text';
      }
    },

    translateTrue (item) {
      switch (this.property(item, 'ValueTypeKey')) {
        case VALUE_ID.inspectionValueType.trueFalse:
          return 'True';
        case VALUE_ID.inspectionValueType.yesNo:
          return 'Yes';
        default:
          return true;
      }
    },

    translateFalse (item) {
      switch (this.property(item, 'ValueTypeKey')) {
        case VALUE_ID.inspectionValueType.trueFalse:
          return 'False';
        case VALUE_ID.inspectionValueType.yesNo:
          return 'No';
        default:
          return false;
      }
    },

    toggleItemPool (value = !this.$data.itemPoolVisible) {
      if (!this.canEdit) {
        this.$data.itemPoolVisible = false;
        return;
      }

      this.$nextTick(() => {
        this.$data.itemPoolVisible = value;
      });
    },

    property (item, property) {
      return this.$_.get(this.itemShape(item), property, '');
    },

    add (item) {
      if (!this.canEdit) return;

      this.createIfMissing('InspectionItems', []);

      let freshItem = {
        lCallKey: this.call.lCallKey,
        lItemKey: item.Key,
        vc255Value: '',
        lOrder: this.inspectionItemsProxy.length + 1,
        poolOrder: this.$_.findIndex(this.sortedInspectionItemsProxy, ['Key', item.Key])
      };

      this.inspectionItemsProxy.push(freshItem);

      this.$nextTick(() => {
        document.querySelector('#' + 'id-' + freshItem.lItemKey).focus();
        this.$forceUpdate();
      });
    },

    remove (index) {
      this.inspectionItemsProxy.splice(index, 1);
    }
  },

  mounted () {
    this.TOPSCOMPANY__getCallInspectionItems({
      callback: response => {
        this.$data.itemPool = response;

        if (this.$data.itemPool.length === 0) {
          this.$hub.$emit(EVENT_TOGGLE_CALL_SECTION, { section: 'inspection', enabled: false });
        }
      }
    });

    this.$hub.$on(BEFORE_CALL_READ, () => {
      this.$set(this.call, 'InspectionItems', []);
    });
  }
};
</script>

<style scoped>
#inspections-section {
  .inspections__form {
    min-block-size: 33dvh;
  }

  .inspections__item-group {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .item-pool {
    padding: 1rem;

    .dropdown-content {
      min-block-size: 10dvh;
      max-block-size: 30dvh;
      overflow-y: scroll;
      box-shadow: var(--box-shadow-100);
    }
  }
}
</style>
