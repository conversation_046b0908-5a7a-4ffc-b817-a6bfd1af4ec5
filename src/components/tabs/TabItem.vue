<template>
  <div class="_tab-item"
    :data-active="value === $parent.valueProxy"
    :data-disabled="disabled"
    @click="onClick"
    ref="tab">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'tab-item',

  props: {
    value: { default: null },
    disabled: { type: Boolean, default: false }
  },

  methods: {
    onClick () {
      if (!this.disabled) {
        this.$parent.valueProxy = this.value;
      }
    }
  }
};
</script>
