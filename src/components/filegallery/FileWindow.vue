<template>
  <div class="files-container">
    <template v-if="hasFiles">
      <FileList
        v-if="viewLayout === 'list'"
        :allFiles="allFiles"
        :enableDelete="enableDelete"
        :enableEdit="enableEdit"
        @refresh="$emit('refresh')" />
      <GalleryPanel
        v-else
        :allFiles="allFiles"
        :enableDelete="enableDelete"
        :enableEdit="enableEdit"
        @refresh="$emit('refresh')" />
    </template>
    <div class="empty-space" v-else>
      No files to show. <a href="#" @click.prevent="$emit('refresh')">Refresh.</a>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import BaseSection from '../call/BaseSection.vue';
import GalleryPanel from './GalleryPanel.vue';
import FileList from './FileList.vue';

export default {
  name: 'file-window',

  extends: BaseSection,

  components: {
    FileList,
    GalleryPanel
  },

  props: {
    allFiles: {
      type: Array
    },
    viewLayout: {
      type: String,
      default: 'grid'
    },
    enableDelete: {
      type: Boolean,
      default: false
    },
    enableEdit: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    ...mapGetters(['__state']),

    hasFiles () {
      return this.allFiles.length > 0;
    }
  }
};
</script>

<style scoped>
.empty-space {
  display: grid;
  place-content: center;

  height: 33cqh;
}
</style>
