<template>
  <div class="file-browser">
    <FileWindow
      :allFiles="allFiles"
      :viewLayout="viewLayout"
      :enableDelete="canDelete"
      @refresh="$emit('refresh')" />
  </div>
</template>

<script>
import FileWindow from './FileWindow.vue';

export default {
  components: { FileWindow },

  props: {
    canDelete: {
      type: Boolean,
      default: false
    },
    allFiles: {
      type: Array,
      default: () => []
    },
    viewLayout: {
      type: String,
      default: 'grid'
    }
  }
};
</script>

<style scoped>
.file-browser {
  height: 25rem;
  overflow: auto;
}
</style>
