<template>
  <div class="gallery-panel">
    <div class="image-wrapper" v-for="(file, index) in allFiles" :key="index" @click="openImageModal(index)">
      <img class="image" :src="file.ThumbnailURL" />
      <section class="tags">
        <i class="tag-icon fas fa-tag"
          v-for="(tag, index) in filterHiddenTags(file.jTags)"
          :style="{ '--index': index }"
          :title="tag.tagType"
          :key="tag.tagUUID">
        </i>
      </section>
    </div>

    <GalleryModal
      :show="showImageModal"
      :initialIndex="initialIndex"
      :allImages="allFiles"
      @close-modal="closeImageModal"
      :enableDelete="enableDelete" />
  </div>
</template>

<script>
import BaseSection from '../call/BaseSection.vue';
import GalleryModal from './modal/GalleryModal.vue';

export default {
  name: 'gallery-panel',

  extends: BaseSection,

  inject: ['filterHiddenTags'],

  components: {
    GalleryModal
  },

  props: {
    allFiles: {
      type: Array
    },
    enableDelete: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      showImageModal: false,
      initialIndex: 0
    };
  },

  methods: {
    openImageModal (index) {
      this.initialIndex = index;
      this.showImageModal = true;
    },

    closeImageModal () {
      this.showImageModal = false;
      this.$emit('refresh');
    }
  }
};
</script>

<style scoped>
.gallery-panel {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(96px, 1fr));
  gap: 0.2rem;
}

.image-wrapper {
  position: relative;

  border-radius: 0.2rem;
  overflow: hidden;

  &:hover {
    .tag-icon {
      transform: translateX(0);
      opacity: 1;

      &::after {
        opacity: 1;
        transform: rotate(-180deg) translateY(0);
      }
    }
  }
}

.image {
  object-fit: cover;
}

.tags {
  position: absolute;
  bottom: 0.6rem;
  left: 0.2rem;
  right: 0.2rem;

  display: flex;
  gap: 0.2rem;
}

.tag-icon {
  --index: 0;

  transform: translateX(calc(var(--index) * -0.5rem));

  color: hsl(var(--pure-purple-h), var(--pure-purple-s), calc(var(--pure-purple-l) + 45%));
  opacity: calc(1 - var(--index) * 0.3);
  transition: transform 0.3s var(--ease-out-quint), opacity 0.2s var(--ease-out-quint);

  &::after {
    position: absolute;
    top: -0.25rem;

    content: attr(title);
    font-family: var(--font-face-text);
    font-size: var(--font-size-small1);
    writing-mode: vertical-lr;
    transform: rotate(-180deg) translateY(-0.5rem);
    transform-origin: 0 0;
    opacity: 0;
    transition: opacity 0.2s var(--ease-out-quint), transform 0.8s var(--ease-out-quint);
    transition-delay: 0.1s;
  }
}
</style>
