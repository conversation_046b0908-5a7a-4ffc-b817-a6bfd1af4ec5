<template>
    <div class="file-item">
      <div class="file-description"  >
        <img :src="file.ThumbnailURL" alt="Thumbnail" class="file-preview"
             @mouseenter="showPreview = true"
             @mouseleave="showPreview = false"
             style="cursor: pointer;" />
        <label>{{ originalFileName }}</label>
        <div v-if="showPreview" class="thumbnail-preview">
          <img :src="file.ThumbnailURL" alt="Full Thumbnail" width="150" height="150"/>
        </div>
      </div>

     <span class="actions-area">
        <span class="blue">{{ readableFileSize }}</span>
        <CopyLinkButton :link="file.FileURL"/>
        <i :class="getFileIcon(file.sFileType)" class="file-icon" @click="onFileIconClick(file.FileURL)"></i>
      </span>
    </div>
</template>


<script>
/*
  {
        "lKey": "10757",
        "lCallKey": "jpg",
        "sFileName": "123",
        "lFileSizeKB": "46",
        "dUploadTime": "2023-11-09 17:04:49",
        "dTakenTime": null,
        "bTakenTimeUTC": "0",
        "sFileType": "pdf",
        "lLocationAccuracy": null,
        "sNotes": "",
        "lUserKey": "25828",
        "sAquiringDeviceOS": null,
        "sAquiringDeviceModel": null,
        "bInternalOnly": "0",
        "dPurgeDate": null,
        "jTags": "[{\"tagType\": \"originalFileName\", \"tagUUID\": \"654d113147d01\", \"audienceName\": \"txi\", \"originalFileName\": \"Screen Shot 2023-11-08 at 07.36.38 AM.png\"}]",
        "FileURL": "https://s3.amazonaws.com/txc-tops-image-store-test/123.pdf",
        "ThumbnailURL": "https://s3.amazonaws.com/txc-tops-image-store-test/123-thumb.jpg"
    },
 */
import CopyLinkButton from './CopyLinkButton.vue';
export default {
  components: {CopyLinkButton},
  props: {
    index: {
      type: Number
    },
    file: {
      type: Object
    }
  },
  data () {
    return {
      ICON_MAP: [
        {fileType: 'heic', icon: 'fas fa-file-image'},
        {fileType: 'jpg', icon: 'fas fa-file-image'},
        {fileType: 'png', icon: 'fas fa-file-image'},
        {fileType: 'tif', icon: 'fas fa-file-image'},
        {fileType: 'pdf', icon: 'fas fa-file-pdf'}],
      showPreview: false, // State to control the preview visibility
      enableDelete: {
        type: Boolean,
        default: false
      }
    };
  },
  computed: {
    readableFileSize () {
      let size = this.file.lFileSizeKB;
      const i = size === 0 ? 0 : Math.floor(Math.log(size) / Math.log(1024));
      const fileSize = (size / Math.pow(1024, i)).toFixed(2) * 1;
      const unit = ['KB', 'MB', 'GB', 'TB'][i];
      return `${fileSize} ${unit}`;
    },
    originalFileName () {
      // Parse the JSON string from the jTags property
      const tags = JSON.parse(this.file.jTags);
      // Find the tag object that contains the originalFileName property
      const originalFileNameTag = tags.find(tag => tag.tagType === 'originalFileName');
      // Return the original file name, or the current file name if not found
      return originalFileNameTag ? originalFileNameTag.originalFileName : this.file.sFileName;
    },
    getFileIcon () {
      return (fileType) => {
        // Convert fileType to lower case for case-insensitive comparison
        const lowerCaseFileType = fileType.toLowerCase();
        // Find the icon that matches the fileType
        const matchingIcon = this.ICON_MAP.find(iconMap => iconMap.fileType === lowerCaseFileType);
        // Return the icon class if found, otherwise a default icon class
        return matchingIcon ? matchingIcon.icon : 'fas fa-file';
      };
    }
  },
  methods: {
    onFileIconClick (link) {
      window.open(link, '_blank');
    }
  }
};
</script>

<style scoped>
.file-item {
  position: relative;/* This provides a positioning context for absolute children */
  width: 100%; /* Full width */
  border-bottom: 1px solid #f2f2f2; /* Light grey border */
  padding: 0; /* Spacing inside the div */
  display: flex; /* Use flexbox for layout */
  align-items: center; /* Align items vertically */
  justify-content: space-between; /* Space out the label and the actions area */
}
.file-description {
  display: flex; /* Use flexbox for layout */
  align-items: center; /* Align items vertically */
}

.file-preview {
  width: 36px; /* Width of the preview image */
  height: 36px; /* Height of the preview image */
  object-fit: cover; /* Make the image fit inside the element */
  border-radius: 4px; /* Rounded corners for the preview image */
  margin-right: 8px; /* Space between the preview image and the label */
  transition: transform 0.2s ease; /* Transition for a hover effect */
}

.file-item:hover {
  background-color: #e1e1e1; /* Darken the background on hover */
}

.file-item label {
  font-weight: bold; /* Make the label bold */
  color: #333; /* Dark grey color for text for readability */
  margin-left: 8px; /* Space after the label */
}
.thumbnail-preview {
  position: absolute;
  width: 150px; /* Width of the preview image */
  height: 150px; /* Height of the preview image */
  border: 1px solid #ccc; /* Optional: border around the preview */
  box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* Optional: shadow for the popup */
  background-color: white; /* Background color of the preview popup */
  z-index: 10000; /* Ensure it's above other elements */
  left: 50px;
  top: 0; /* Position below the thumbnail */
  margin-top: 8px; /* Space between the thumbnail and the preview */
}

.thumbnail-preview img {
  width: 100%; /* Full width of the popup */
  height: 100%; /* Full height of the popup */
  display: block; /* Remove inline-block spacing */
}

.actions-area{
  display: flex; /* Use flexbox for layout */
  align-items: center; /* Align items vertically */
  gap: 12px; /* Space between elements in the actions area */
  padding-right: 8px; /* Space after the actions area */
}

.actions-area span {
  background-color: #eef4ff; /* Light blue background for type and size badges */
  color: #616A75; /* Blue color text for contrast */
  border-radius: 4px; /* Rounded corners for the badges */
  padding: 4px 8px; /* Spacing inside the badges */
  font-size: 0.85rem; /* Smaller font size for the badges */
  white-space: nowrap; /* Prevents the badges from breaking into multiple lines */
}

i.file-icon {
  color: #616A75; /* Red color icon for PDFs, change as needed for different file types */
  font-size: 1.5rem; /* Larger font size for the icon */
  transition: transform 0.2s ease; /* Transition for a hover effect */
}

i.file-icon:hover {
  transform: scale(1.1); /* Slightly scale up the icon on hover */
  cursor: pointer; /* Change cursor to pointer when hovering over the icon */
}
</style>
