<template>
  <div class="details-bar">
    <h3 class="is-small is-upper">Meta</h3>

    <div class="grid-item">
      <div class="detail-item">
        <span class="detail-label">Internal Only</span>
        <span class="detail-value"><input type="checkbox" v-model="file.bInternalOnly" @change="toggleInternalOnly" :disabled="!canEdit" /></span>
      </div>
      <div class="detail-item">
        <span class="detail-label">Uploaded By</span>
        <span class="detail-value">{{ file.lUserKey }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">Uploaded At</span>
        <span class="detail-value" :title="formattedUploadTime">{{ formattedUploadTime }}</span>
      </div>
    </div>

    <div class="grid-item">
      <div class="detail-item">
        <span class="detail-label">Original Filename</span>
        <span class="detail-value" :title="originalFileName">{{ originalFileName }}</span>
      </div>
    </div>

    <div class="grid-item">
      <div class="detail-item" v-if="readableFileSize">
        <span class="detail-label">File Size</span>
        <span class="detail-value">{{ readableFileSize }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">File Type</span>
        <span class="detail-value">{{ file.sFileType }}</span>
      </div>
    </div>

    <div class="grid-item" v-if="file.sNotes">
        {{ file.sNotes }}
    </div>
  </div>
</template>


<script>
import Vue from 'vue';
import format from 'date-fns/format';
import Access from '@/utils/access';

// Store the logarithm base as a constant
const LOG_1024 = Math.log(1024);
export default {
  props: {
    file: Object
  },

  computed: {
    canEdit () {
      return Access.has('images.edit');
    },

    readableFileSize () {
      const size = this.file.lFileSizeKB;

      if (!size) {
        return null;
      }

      const i = size === 0 ? 0 : Math.floor(Math.log(size) / LOG_1024);
      const fileSize = (size / Math.pow(1024, i)).toFixed(2) * 1;
      const unit = ['KB', 'MB', 'GB', 'TB'][i];
      return `${fileSize} ${unit}`;
    },

    formattedUploadTime () {
      return format(new Date(this.file.dUploadTime), 'MM/d/YY · h:mma');
    },

    originalFileName () {
      try {
        // Check if jTags exists and is not null or empty
        if (!this.file.jTags || this.file.jTags === 'null' || this.file.jTags === '') {
          return this.file.sFileName;
        }

        // Parse the JSON string from the jTags property
        const tags = JSON.parse(this.file.jTags);

        // Check if tags is an array before using find
        if (!Array.isArray(tags)) {
          return this.file.sFileName;
        }

        // Find the tag object that contains the originalFileName property
        const originalFileNameTag = tags.find(tag => tag.tagType === 'originalFileName');

        // Return the original file name, or the current file name if not found
        return originalFileNameTag ? originalFileNameTag.originalFileName : this.file.sFileName;
      } catch (error) {
        console.error('Error parsing jTags:', error);
        return this.file.sFileName;
      }
    }
  },

  methods: {
    async toggleInternalOnly () {
      const payload = {
        Authentication: {
          InstanceKey: this.$store.state.instance.Key,
          AuthenticationKey: this.$store.state.instance.Authentication
        },
        Data: {
          OrgUnitKey: this.$store.state.orgUnitKey,
          ImagePrefix: this.file.sFileName,
          internalOnly: this.file.bInternalOnly ? 1 : 0
        }
      };

      const response = await Vue.axios.post(import.meta.env.VITE_TXI_API_IMAGE_VISIBILITY, payload);
    }
  }
};
</script>

<style scoped>
.details-bar {
  display       : flex;
  flex-direction: column;
  gap           : 1rem;

  /* height: 100cqh; */
  /* overflow-y: auto; */
}

.grid-item {
  padding: 0.5rem;
  border: 0.1rem solid var(--input-border);
  border-radius: 0.25rem;
}


/* DETAILS BAR - DETAIL ITEM KEY/VALUE */

.detail-item {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Aligns items to the right */
}


.detail-label {
  font-weight : normal;
  margin-right: auto; /* Pushes everything after it to the right */
}


.detail-value {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Aligns content to the right */

  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #4E92CC;
  font-weight: bold;
}
</style>
