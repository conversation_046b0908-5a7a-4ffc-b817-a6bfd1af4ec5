<template>
  <section class="thumbnail-box">
    <div class="thumbnail-liner">
      <div class="thumbnail-wrapper"
        v-for="(file, index) in allImages"
        :key="index"
        @click="navigateImage(index - currentIndex)">
        <img class="thumbnail" :src="file.ThumbnailURL" :alt="`${file.sFileName}.${file.sFileType}`" />
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    allImages: Array,
    currentIndex: Number
  },

  methods: {
    navigateImage (index) {
      this.$emit('navigate-image', index);
    }
  }
};
</script>

<style scoped>
.thumbnail-box {
  container: thumbnail-box / inline-size;
}

.thumbnail-liner {
  display              : grid;
  grid-template-columns: repeat(2, 1fr);
  align-content        : start;
  gap                  : 0.2rem;

  padding   : 0.5rem;
  width     : 100cqw;
  height    : 90cqh;
  overflow-y: auto;
}

.thumbnail-wrapper {
  container: thumbnail-wrapper / inline-size;

  aspect-ratio : 1;
  border-radius: 0.25rem;
  overflow     : clip;
}

.thumbnail {
  max-width : 100%;
  max-height: 100%;
  object-fit: cover;
}

/* Simple responsive layout */
@media (max-width : 768px) {
  .thumbnail-box {
    /* flex-basis : 100%; */
  }
}
</style>
