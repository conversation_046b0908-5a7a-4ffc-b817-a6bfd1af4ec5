<template>
  <div v-if="show" class="image-modal" @click.self="closeImageModal">

    <div class="gallery-container" @click.stop>
      <ThumbnailBox :allImages="allImages" :currentIndex="currentIndex" @navigate-image="navigateImage" />

      <section class="photo-pane">
        <div class="main-image-container">
          <img id="main-image"
            class="full-image"
            :src="currentImage.PreviewURL || currentImage.FileURL"
            :style="{ transform: `rotate(${rotationAngle}deg)` }"
            alt="Full Image">
        </div>
      </section>

      <section class="meta-pane">
        <div class="toolbar">
          <div class="toolbar-group primary">
            <app-button type="info" @click="navigateImage(-1)">
              <i class="fas fa-arrow-left"></i>
            </app-button>
            <app-button type="info" @click="navigateImage(1)">
              <i class="fas fa-arrow-right"></i>
            </app-button>
            <div class="flex-space"></div>
            <span class="image-counter is-bold">{{ currentIndex + 1 }} of {{ totalImages }}</span>
            <div class="flex-space"></div>
            <app-button type="dark" @click="closeImageModal">
              <i class="far fa-xmark"></i>
            </app-button>
          </div>

          <div class="toolbar-group secondary">
            <app-button @click="openFullImageLink(currentImage.FileURL)" title="Open full image.">
              <i class="far fa-expand"></i>
            </app-button>
            <app-button @click="rotateImage" title="Rotate image clockwise.">
              <i class="far fa-rotate-right"></i>
            </app-button>
            <div class="flex-space"></div>
            <CopyLinkButton :link="currentImage.FileURL" />
            <div class="flex-space"></div>
            <app-button :disabled="!canDelete" @click="onConfirmDelete(currentImage.lKey)" title="Delete image.">
              <i v-if="!isDeleting" class="far fa-trash"></i>
              <i v-else class="fas fa-refresh fa-spin"></i>
            </app-button>
          </div>
        </div>

        <TagsControl :file="currentImage" />
        <DetailsBar :file="currentImage" />
      </section>
    </div>

  </div>
</template>

<script>
import DetailsBar from './DetailsBar.vue';
import ThumbnailBox from './ThumbnailBox.vue';
import CopyLinkButton from '../CopyLinkButton.vue';
import TagsControl from '../TagsControl.vue';

export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    enableDelete: {
      type: Boolean,
      default: false
    },
    allImages: Array
  },

  components: {
    CopyLinkButton,
    DetailsBar,
    ThumbnailBox,
    TagsControl
  },

  data () {
    return {
      rotationAngle: 0,
      currentIndex: this.initialIndex,
      isDeleting: false
    };
  },

  watch: {
    initialIndex: {
      deep: true,
      handler: function (newVal) {
        this.currentIndex = newVal;
      }
    }
  },

  mounted () {
    console.log('Gallery Modal Enable Delete: ', this.enableDelete);
  },

  computed: {
    canDelete () {
      return this.enableDelete && !this.isDeleting;
    },

    currentImage () {
      return this.allImages[this.currentIndex];
    },

    totalImages () {
      return this.allImages.length;
    },

    originalFileName () {
      // Parse the JSON string from the jTags property
      const tags = JSON.parse(this.currentImage.jTags);
      // Find the tag object that contains the originalFileName property
      const originalFileNameTag = tags.find(tag => tag.tagType === 'originalFileName');
      // Return the original file name, or the current file name if not found
      return originalFileNameTag ? originalFileNameTag.originalFileName : this.file.sFileName;
    }
  },

  methods: {
    onConfirmDelete (fileKey) {
      this.$confirm('Are you sure you want to delete this image? This cannot be undone.', 'Confirm Delete', {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.onDeleteItem(fileKey);
      }).catch(() => {
        // Do nothing
      });
    },

    onDeleteItem (fileKey) {
      console.log('Deleting image with key: ', fileKey);
      this.isDeleting = true;
      setTimeout(() => {
        this.isDeleting = false;
        this.$notify({ title: 'Image Deleted', message: 'Your image was successfully deleted.', type: 'success' });
      }, 1000);
    },

    rotateImage () {
      this.rotationAngle += 90;
    },

    openFullImageLink (link) {
      window.open(link, '_blank');
    },

    closeImageModal () {
      this.$emit('close-modal');
    },

    navigateImage (direction) {
      const nextIndex = this.currentIndex + direction;
      if (nextIndex >= 0 && nextIndex < this.allImages.length) {
        this.currentIndex = nextIndex; // Update currentIndex when navigating
      }
    }
  }
};
</script>

<style scoped>
.image-modal {
  z-index         : 1000;
  position        : fixed;
  top             : 0;
  left            : 0;
  width           : 100%;
  height          : 100%;
  background      : rgba(0, 0, 0, 0.5);
  display         : flex;
  align-items     : center;
  justify-content : center;
}

.gallery-container {
  --gallery-column-width: 10rem;

  display              : grid;
  grid-template-columns: var(--gallery-column-width) 1fr 20rem;
  align-items          : start;
  gap                  : 0.1rem;

  --from-background : oklch(from var(--placeholder-bg) l c h / 0.8);
  --to-background : oklch(from var(--placeholder-bg) calc(l - 0.02) c h / 0.8);

  contain        : content;
  padding        : 0.25rem;
  width          : calc(100vw - 4rem);
  max-height     : calc(100vh - 4rem);
  background     : linear-gradient(to right, var(--from-background) calc(var(--gallery-column-width) - 2rem), var(--to-background) var(--gallery-column-width));
  border-radius  : 0.75rem;
  backdrop-filter: blur(1rem);
}

.photo-pane {
  align-self: stretch;
  position: relative;

  display        : flex;
  flex-direction : column;
  flex-basis     : 90%;

  background-color         : white;
  border-top-left-radius   : 0.5rem;
  border-bottom-left-radius: 0.5rem;
  box-sizing               : border-box;
}

.meta-pane {
  align-self: stretch;
  position: relative;

  display       : flex;
  flex-direction: column;
  gap           : 2rem;

  padding                   : 0.5rem;
  background-color          : white;
  border-top-right-radius   : 0.5rem;
  border-bottom-right-radius: 0.5rem;
  overflow-y: auto;
}

.main-image-container {
  position: relative;

  display           : flex;
  justify-content   : center;
  align-items       : center;

  max-height      : 80vh;
  min-height      : 80vh;
  background-image: url("data:image/svg+xml,%3Csvg width='6' height='6' viewBox='0 0 6 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23e5eaf2' fill-opacity='0.4' fill-rule='evenodd'%3E%3Cpath d='M5 0h1L0 6V5zM6 5v1H5z'/%3E%3C/g%3E%3C/svg%3E");
  padding         : 0.5rem;
}

.full-image {
  transition   : transform 0.6s var(--ease-out-expo);
  max-width    : 100%;
  max-height   : 80vh;
  object-fit   : contain;
  border-radius: 0.5rem;
}



/* TOOL BAR */
.toolbar {
  contain: content;
  border: 0.1rem solid var(--input-border);
  border-radius: 0.25rem;
  box-shadow: var(--box-shadow-50);
}

.toolbar-group {
  display       : flex;
  align-items   : center;
  gap           : 0.5rem;

  padding          : 0.5rem;

  &.secondary {
    background-color: var(--placeholder-bg);
  }
}


/* Simple responsive layout */
@media (max-width : 768px) {
  .gallery-container {
    flex-direction : column;
  }

  .photo-pane {
    flex-basis : 100%;
  }
}
</style>
