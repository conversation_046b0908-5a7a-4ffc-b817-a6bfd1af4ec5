<template>
  <div class="progress-container">
    <div class="progress-item" v-for="(progressObj, index) in uploadProgress" :key="index" v-if="!progressObj.isRemoved">
      <label>Upload {{ index + 1 }}:</label>

      <!-- Show progress bar and percentage only if there's no message -->
      <span class="grow">
        <progress v-if="!progressObj.message" :value="progressObj.progress" max="100" class="progress"></progress>
        <span v-if="progressObj.message" :class="{ 'success': !progressObj.error, 'error': progressObj.error }">
          {{ progressObj.message }}
        </span>
      </span>

      <!-- Show success or error message once available -->
      <span class="actions-area">
        <span v-if="!progressObj.message">{{ progressObj.progress }}%</span>
        <i class="fas fa-light fa-circle-xmark" v-if="progressObj.error" @click="removeError(index)"  style="cursor: pointer;"></i>
      </span>

    </div>
  </div>
</template>


<script>
export default {
  props: {
    uploadProgress: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    uploadProgress: {
      deep: true,
      handler: function (newVal) {
        newVal.forEach((progressObj, index) => {
          if (progressObj.isComplete && !progressObj.error && !progressObj.isRemoved) {
            setTimeout(() => {
              this.$set(this.uploadProgress[index], 'isRemoved', true);
            }, 3000);
          }
        });
      }
    }
  },
  methods: {
    removeError (index) {
      this.$set(this.uploadProgress[index], 'isRemoved', true); // Remove the progress item
    }
  }
};
</script>

<style scoped>
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
}

.progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 5px 10px 5px 10px;
  margin-bottom: 2px;
  background-color: #F2F2F2;
}

.progress-item label {
  flex-shrink: 0;
  margin-right: 10px;
}

.grow {
  flex-grow: 1;
}
.actions-area{
  margin-left: 10px;
  padding-left: 5px;
}
.success {
  color: green;
}

.error {
  color: red;
}

/* For the icon */
.fas.fa-light.fa-circle-xmark {
  flex-shrink: 0;  /* Prevent the icon from shrinking */
}


/* Style for the native progress element */
.progress {
  /* Remove default appearance */
  -webkit-appearance: none;
  appearance: none;
  /* Margins */
  margin: 5px 5px;

  /* Dimensions */
  width: 100%;
  height: 20px; /* Adjust the height to your liking */

  /* Colors */
  background-color: #e6e6e6;
  border: 1px solid #DADADA;

  /* For browsers that support it, remove the inner border and padding in Firefox and Opera. */
  &::-webkit-progress-bar {
    background-color: transparent;
  }

  /* Style the progress value (the bar) */
  &::-webkit-progress-value {
    background-color: #3D6999;
    background-image: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    background-size: 25px 25px;
    animation: moveStripes 2s linear infinite;
  }

  &::-moz-progress-bar {
    background-color: #3D6999;
    background-image: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    background-size: 25px 25px;
    animation: moveStripes 2s linear infinite;
  }

  &::-ms-fill {
    border: none; /* Remove border in IE 10- */
    background-color: #3D6999;
    /* Add stripes for IE 10+ */
    background-image: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    animation: moveStripes 2s linear infinite;
  }
}

/* Create the animation for the stripes */
@keyframes moveStripes {
  to {
    background-position: 50px 0;
  }
}


</style>
