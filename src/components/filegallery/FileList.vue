<template>
  <div class="file-list">
    <div v-for="(file, index) in allFiles" :key="index">
      <FileRow
        :index="index"
        :file="file"
        :enableDelete="enableDelete"
        @click="openImageModal(index)" />
    </div>

    <GalleryModal
      :show="showImageModal"
      :initialIndex="initialIndex"
      :allImages="allFiles"
      :enableDelete="enableDelete"
      @close-modal="closeImageModal" />
  </div>
</template>


<script>
import GalleryModal from './modal/GalleryModal.vue';
import FileRow from './FileRow.vue';

export default {
  name: 'file-list',

  components: {
    FileRow,
    GalleryModal
  },

  props: {
    allFiles: {
      type: Array
    },
    enableDelete: {
      type: Boolean,
      default: false
    },
    enableEdit: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      showImageModal: false,
      initialIndex: 0
    };
  },

  methods: {
    openImageModal (index) {
      this.initialIndex = index;
      this.showImageModal = true;
    },

    closeImageModal () {
      this.showImageModal = false;
      this.$emit('refresh');
    }
  }
};
</script>
