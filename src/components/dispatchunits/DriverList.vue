<template>
  <transition-group name="smooth-list" tag="ul" :css="false">
    <li class="_item" data-role="filter" key="filter-control">
      <i class="_icon far fa-address-card"></i>
      <select class="_status" v-model="$store.state.assignCall.driverStatusFilter">
        <option value="">All Drivers</option>
        <option v-for="status in statuses" :value="status.Key" :key="status.Key">{{ status.Value }}</option>
      </select>
      <div class="_busy-control" @click="$store.state.assignCall.showBusyDrivers = !$store.state.assignCall.showBusyDrivers">
        <i class="far fa-filter-slash" v-if="$store.state.assignCall.showBusyDrivers" title="Hide busy drivers."></i>
        <i class="far fa-filter" v-else title="Show busy drivers."></i>
      </div>
    </li>

    <DriverItem v-for="driver in data"
      :item="driver"
      :statuses="statuses"
      :is-selected="selectedKey === driver.Code"
      :key="driver.Key"
      @click="$emit('click', driver)" />
  </transition-group>
</template>

<script>
import DriverItem from './DriverItem.vue';

export default {
  name: 'driver-item',

  components: { DriverItem },

  props: {
    data: {
      type: Array,
      required: true,
      default: []
    },
    selectedKey: {
      type: String | Number,
      default: ''
    }
  },

  data () {
    return {
      statuses: []
    };
  },

  mounted () {
    this.$store.dispatch('TOPSCOMPANY__getDriverStatuses', {
      callback: response => {
        this.$data.statuses = response;
      }
    });
  }
};
</script>
