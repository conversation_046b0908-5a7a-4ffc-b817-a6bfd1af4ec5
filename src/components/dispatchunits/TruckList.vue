<template>
  <transition-group name="smooth-list" tag="ul" :css="false">
    <li class="_item" data-role="filter" key="filter-control">
      <i class="_icon far fa-steering-wheel"></i>
      <select class="_status" v-model="$store.state.assignCall.truckStatusFilter">
        <option value="">All Trucks</option>
        <option v-for="status in statuses" :value="status.Key" :key="status.Key">{{ status.Value }}</option>
      </select>
      <div class="_busy-control" @click="$store.state.assignCall.showBusyTrucks = !$store.state.assignCall.showBusyTrucks">
        <i class="far fa-filter-slash" v-if="$store.state.assignCall.showBusyTrucks" title="Hide busy trucks."></i>
        <i class="far fa-filter" v-else title="Show busy trucks."></i>
      </div>
    </li>

    <TruckItem v-for="truck in data"
      :item="truck"
      :statuses="statuses"
      :is-selected="selectedKey === truck.Number"
      :key="truck.Key"
      @click="$emit('click', truck)" />
  </transition-group>
</template>

<script>
import TruckItem from './TruckItem.vue';

export default {
  name: 'truck-item',

  components: { TruckItem },

  props: {
    data: {
      type: Array,
      required: true,
      default: []
    },
    selectedKey: {
      type: String | Number,
      default: ''
    }
  },

  data () {
    return {
      selectedTruckKey: '',
      statuses: []
    };
  },

  mounted () {
    this.$store.dispatch('TOPSCOMPANY__getTruckStatuses', {
      callback: response => {
        this.$data.statuses = response;
      }
    });
  }
};
</script>
