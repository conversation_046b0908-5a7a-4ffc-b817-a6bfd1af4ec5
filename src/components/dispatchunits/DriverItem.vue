<template>
  <li class="_item" :data-selected="isSelected" @click="$emit('click', item)">
    <div class="_assignments" v-if="item.Count > 0" :title="`Currently on ${item.Count} assignments.`">
      <template v-if="item.Count < 10">{{ item.Count }}</template>
      <template v-else><i class="fa-solid fa-exclamation"></i></template>
    </div>
    <div class="_label">{{ item.Code }}</div>
    <div class="_sublabel">{{ item.Truck }}</div>

    <select class="_status" v-model="item.Status" @change="setStatus">
      <option v-for="status in statuses" :value="status.Value" :key="status.Key">
        {{ status.Value }}
      </option>
    </select>
  </li>
</template>

<script>
export default {
  name: 'driver_item',

  props: {
    item: {
      type: Object,
      required: true
    },
    statuses: {
      type: Array,
      required: true
    },
    isSelected: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },

  methods: {
    setStatus () {
      this.$store.dispatch('TOPSCOMPANY__updateDriverStatus', {
        key: this.item.Key,
        statusKey: this.statuses.find(status => status.Value === this.item.Status).Key
      });
    }
  }
};
</script>
