<template>
  <div id="dispatch-units"
    :data-primary-list-drivers="$store.state.assignCall.primaryList === 'drivers'"
    :data-primary-list-trucks="$store.state.assignCall.primaryList === 'trucks'">

    <section class="swap-gutter">
      <div class="backdrop"></div>
      <div class="click-magnet"></div>
      <button class="swap-button" @click="swapListPriority">
        <i class="fas fa-arrow-right-arrow-left"></i>
      </button>
    </section>

    <section class="drivers">
      <DriverList
        :data="$store.getters['assignCall.drivers']"
        :selected-key="selectedDriverCode"
        @click="onDriverClick" />
    </section>

    <section class="trucks">
      <TruckList
        :data="$store.getters['assignCall.trucks']"
        :selected-key="selectedTruckNumber"
        @click="onTruckClick" />
    </section>

  </div>
</template>

<script>
import TruckList from './TruckList.vue';
import DriverList from './DriverList.vue';

export default {
  name: 'dispatch-units',

  components: { TruckList, DriverList },

  props: {
    selectedTruck: {
      type: Object,
      default: undefined
    },
    selectedDriver: {
      type: Object,
      default: undefined
    }
  },

  data () {
    return {
      _selectedTruck: null,
      _selectedDriver: null
    };
  },

  computed: {
    $selectedTruck: {
      get () {
        return this.selectedTruck ? this.selectedTruck : this.$data._selectedTruck;
      },
      set (value) {
        if (this.selectedTruck !== undefined) {
          this.$emit('update:selectedTruck', value);
        } else {
          this.$data._selectedTruck = value;
          this.$emit('on-truck-selected', value);
        }
      }
    },

    $selectedDriver: {
      get () {
        return this.selectedDriver ? this.selectedDriver : this.$data._selectedDriver;
      },
      set (value) {
        if (this.selectedDriver !== undefined) {
          this.$emit('update:selectedDriver', value);
        } else {
          this.$data._selectedDriver = value;
          this.$emit('on-driver-selected', value);
        }
      }
    },

    selectedTruckNumber () {
      return this.$_.get(this.$selectedTruck, 'Number', '');
    },

    selectedDriverCode () {
      return this.$_.get(this.$selectedDriver, 'Code', '');
    }
  },

  watch: {
    '$store.state.assignCall.driverStatusFilter' () {
      this.getDispatchUnits();
    },

    '$store.state.assignCall.truckStatusFilter' () {
      this.getDispatchUnits();
    }
  },

  methods: {
    getDispatchUnits () {
      this.$store.dispatch('TOPSCALL__getDriverTruckToAssign', {
        notBusy: false,
        driverStatuses: [this.$store.state.assignCall.driverStatusFilter],
        truckStatuses: [this.$store.state.assignCall.truckStatusFilter],
        callback: response => {
          this.$store.state.assignCall.dispatchUnitsResponse = response;

          this.$emit('on-dispatch-units-loaded', response);
          this.$emit('on-drivers-loaded', this.$store.getters['assignCall.drivers']);
          this.$emit('on-trucks-loaded', this.$store.getters['assignCall.trucks']);
        }
      });
    },

    onDriverClick (driver) {
      this.$selectedDriver = driver;

      if (this.$store.state.assignCall.primaryList === 'drivers') {
        const truck = this.$store.getters['assignCall.trucks'].find(truck => truck.Number === driver.Truck);
        if (truck) {
          this.$selectedTruck = truck;
        }
      }
    },

    onTruckClick (truck) {
      this.$selectedTruck = truck;

      if (this.$store.state.assignCall.primaryList === 'trucks') {
        const driver = this.$store.getters['assignCall.drivers'].find(driver => driver.Truck === truck.Number);
        if (driver) {
          this.$selectedDriver = driver;
        }
      }
    },

    swapListPriority () {
      this.$store.state.assignCall.primaryList = this.$store.state.assignCall.primaryList === 'drivers' ? 'trucks' : 'drivers';
    }
  },

  mounted () {
    this.getDispatchUnits();
  }
};
</script>

<style lang="css">
#dispatch-units {
  display: grid;
  grid-template-columns: 1fr 0.3rem 1fr;

  &[data-primary-list-drivers="true"] {
    grid-template-areas: "drivers swap-gutter trucks";
  }

  &[data-primary-list-trucks="true"] {
    grid-template-areas: "trucks swap-gutter drivers";
  }

  width: 100%;
  overflow: hidden;
  background-color: var(--body-border);
}

.swap-gutter {
  grid-area: swap-gutter;

  position: relative;

  display: grid;
  place-items: center;

  transition: opacity 0.4s;
  z-index: 9;

  &:hover {
    .backdrop {
      opacity: 1;
    }

    .click-magnet {
      left: -3rem;
      right: -3rem;
    }

    .swap-button {
      opacity: 1;
      filter: blur(0);
      transform: scale(1);
    }
  }

  .backdrop,
  .click-magnet {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
  }

  .backdrop {
    background: linear-gradient(0deg,
      hsla(var(--pure-blue-hsl), 0) 5%,
      hsla(var(--pure-blue-hsl), 1) 50%,
      hsla(var(--pure-blue-hsl), 0) 95%
    );
    opacity: 0;
    transition: opacity 0.6s;
  }

  .swap-button {
    --size: 2rem;

    position: absolute;

    width: var(--size);
    height: var(--size);
    color: white;
    background-color: var(--pure-blue);
    border: 0;
    border-radius: var(--size);
    opacity: 0;
    filter: blur(1rem);
    transform: scale(0.3);
    transition: all 0.2s;
  }
}

.drivers,
.trucks {
  background-color: white;
  overflow-y: auto;
  container-type: inline-size;
}

.drivers {
  grid-area: drivers;
}

.trucks {
  grid-area: trucks;
}

._item {
  position: relative;

  display: grid;
  grid-template-columns: 1.25rem 2fr 1fr;
  grid-template-areas: "icon label sublabel";
  align-items: center;
  gap: 0 0.25rem;

  @container (min-width: 200px) {
    grid-template-columns: 1.25rem 2fr 1fr 1fr;
    grid-template-areas: "icon label sublabel status";
  }

  padding: 0.5rem;

  &[data-selected] {
    background-color: var(--selected-background);
  }

  &[data-role="filter"] {
    grid-template-columns: 1.25rem 1fr 1.5rem;
    grid-template-areas: "icon status busycontrol";
    background-color: hsl(var(--blue-hsl), 0.03);
    color: hsl(var(--blue-hsl));

    & * {
      border: 0;
    }
  }

  ._icon,
  ._assignments {
    grid-area: icon;
  }

  ._busy-control {
    grid-area: busycontrol;

    place-self: center;
  }

  ._assignments {
    --size: 1.25rem;

    display: grid;
    place-items: center;

    width: var(--size);
    height: var(--size);
    font-size: var(--font-size-small1);
    font-weight: bold;
    color: hsl(var(--pure-orange-hsl));
    background-color: hsl(var(--pure-orange-hsl), 0.2);
    border-radius: 1rem;
  }

  ._label {
    grid-area: label;

    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
  }

  ._sublabel {
    grid-area: sublabel;

    white-space: nowrap;
    overflow: hidden;
  }

  ._status {
    grid-area: status;

    border: 0;
    background-color: transparent !important;
    appearance: none;

    &:hover {
      background-color: hsl(var(--blue-hsl), 0.5);
    }
  }

  &:not([data-role="filter"]) {
    ._sublabel {
      opacity: 0.6;
    }

    ._status {
      display: none;
      padding: 0.25em 0.5em;
      font-size: var(--font-size-small1);
      background-color: hsl(var(--blue-hsl), 0.1) !important;
      border-radius: 1rem;
      opacity: 0.6;

      @container (min-width: 200px) {
        display: block;
      }
    }
  }
}
</style>
