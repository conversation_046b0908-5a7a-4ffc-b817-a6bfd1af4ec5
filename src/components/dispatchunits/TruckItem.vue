<template>
  <li class="_item" :data-selected="isSelected" @click="$emit('click', item)">
    <div class="_assignments" v-if="item.Count > 0" :title="`Currently on ${item.Count} assignments.`">
      <template v-if="item.Count < 10">{{ item.Count }}</template>
      <template v-else><i class="fa-solid fa-exclamation"></i></template>
    </div>
    <div class="_label">{{ item.Number }}</div>
    <div class="_sublabel">{{ item.Type }}</div>

    <select class="_status" v-model="item.Status" @change="setStatus">
      <option v-for="status in statuses" :value="status.Value" :key="status.Key">
        {{ status.Value }}
      </option>
    </select>
  </li>
</template>

<script>
export default {
  name: 'truck-item',

  props: {
    item: {
      type: Object,
      required: true
    },
    statuses: {
      type: Array,
      required: true
    },
    isSelected: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  },

  methods: {
    setStatus () {
      this.$store.dispatch('TOPSCOMPANY__updateTruckStatus', {
        key: this.item.Key,
        statusKey: this.statuses.find(status => status.Value === this.item.Status).Key
      });
    }
  }
};
</script>
