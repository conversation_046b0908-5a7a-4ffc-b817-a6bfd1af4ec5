<template>
  <app-grid-form class="retow-search" context="inline">
    <div class="columns is-multiline">
      <div class="column is-3">
        <app-date-time v-model="call.Retow.dCallTaken" :is-range="true">
          Call Taken Date
        </app-date-time>
      </div>
      <div class="column is-3">
        <app-text v-model="call.Retow.sCallTakenBy">
          Call Taken By
        </app-text>
      </div>
      <div class="column is-3">
        <app-date-time v-model="call.Retow.dETA" :is-range="true">
          ETA
        </app-date-time>
      </div>
      <div class="column is-3">
        <app-date-time v-model="call.Retow.dAppointment" :is-range="true">
          Appointment
        </app-date-time>
      </div>
      <div class="column is-4 is-left">
        <app-text v-model="call.Retow.vc30ContactName">
          Contact Name
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.Retow.vc20ContactPhoneNum">
          Contact Phone
        </app-text>
      </div>
      <div class="column is-4">
        <app-select-boolean v-model="call.Retow.bSecondCommission">
          Second Commission
        </app-select-boolean>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="call.Retow.vc100Location">
          Location
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="call.Retow.vc100Destination">
          Destination
        </app-text>
      </div>
      <div class="column is-12 is-left is-bottom">
        <app-text v-model="call.Retow.vc255Notes">
          Notes
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import BaseSection from './BaseSection.vue';

export default {
  name: 'retow-search',

  extends: BaseSection,

  mounted () {
    this.$set(this.call, 'Retow', {
      dCallTaken: '',
      sCallTakenBy: '',
      dETA: '',
      dAppointment: '',
      vc30ContactName: '',
      vc20ContactPhoneNum: '',
      bSecondCommission: '',
      vc100Location: '',
      vc100Destination: '',
      vc255Notes: ''
    });
  }
};
</script>
