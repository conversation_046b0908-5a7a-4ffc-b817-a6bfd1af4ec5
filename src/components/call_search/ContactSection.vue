<template>
  <app-grid-form class="_editor" context="inline">
    <div class="columns is-multiline" v-for="(contact, index) in call.Contacts" :key="index">
      <div class="column is-6">
        <app-text v-model="contact.vc30Name1">
          Name 1
        </app-text>
      </div>
      <div class="column is-3">
        <app-text v-model="contact.vc30Name2">
          Name 2
        </app-text>
      </div>
      <div class="column is-3">
        <app-select-search v-model="contact.lAddressTypeKey" :options="types" keyAlias="Key" valueAlias="Value">
          Type
        </app-select-search>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="contact.vc30Address1">
          Address 1
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="contact.vc30Address2">
          Address 2
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="contact.vc30City">
          City
        </app-text>
      </div>
      <div class="column is-3">
        <app-select-state v-model="contact.ch2StateKey">
          State
        </app-select-state>
      </div>
      <div class="column is-3">
        <app-text v-model="contact.vc10Zip">
          Zip
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="contact.vc20Phone1">
          Phone 1
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="contact.vc20Phone2">
          Phone 2
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="contact.vc20Fax">
          Fax
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="contact.vc50Email">
          Email
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="contact.vc50UserDefined1">
          User Defined 1
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="contact.vc50UserDefined2">
          User Defined 2
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'contact-search',

  extends: BaseSection,

  data () {
    return {
      types: [],
      users: []
    };
  },

  mounted () {
    this.$set(this.call, 'Contacts', [{
      vc30Name1: '',
      vc30Name2: '',
      lAddressTypeKey: '',
      vc30Address1: '',
      vc30Address2: '',
      vc30City: '',
      ch2StateKey: '',
      vc10Zip: '',
      vc20Phone1: '',
      vc20Phone2: '',
      vc20Fax: '',
      vc50Email: '',
      vc50UserDefined1: '',
      vc50UserDefined2: ''
    }]);
  },

  methods: {
    ...mapActions([
      'CONTACT__getTypes'
    ]),

    getTypes () {
      this.CONTACT__getTypes({
        success: response => {
          this.$data.types = response;
        }
      });
    },

    getUsers () {
      let userKeys = [];

      this.contactsProxy.forEach(contact => {
        if (contact.lUserKeyCreatedBy.length > 0) {
          userKeys.push(contact.lUserKeyCreatedBy);
        }
      });

      if (userKeys.length === 0) return;

      this.USER__getID({
        keys: userKeys,
        success: response => {
          this.$data.users = response;
        }
      });
    }
  }
};
</script>
