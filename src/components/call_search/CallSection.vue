<template>
  <span id="call-section">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-4 is-left is-bottom">
          <app-text v-model="call.lReferenceNum" id="CAL_lReferenceNum">
            Call Number
          </app-text>
        </div>
        <div class="column is-4 is-bottom">
          <app-select v-model="call.lCallStatusTypeKey" :options="statuses" id="CAL_sCallStatus" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
            Status
          </app-select>
        </div>
        <div class="column is-4 is-bottom">
          <app-text v-model="call.tcPrice" id="CAL_tcPrice">
            Price
          </app-text>
        </div>
      </div>
    </app-grid-form>
  </span>
</template>

<script>
import BaseSection from './BaseSection.vue';
import { mapActions, mapGetters } from 'vuex';
import { CALL_SECTION_CALL } from '@/config.js';

export default {
  name: CALL_SECTION_CALL,

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_CALL,

      statuses: []
    };
  },

  computed: {
    callKey () {
      return this.$route.params.key;
    }
  },

  methods: {
    ...mapActions([
      'CALL__getStatuses'
    ]),

    getStatuses () {
      this.CALL__getStatuses({
        callback: response => {
          this.$data.statuses = response;
        }
      });
    }
  },

  mounted () {
    this.getStatuses();
  }
};
</script>
