<template>
  <app-grid-form class="notes-search" context="inline">
    <div class="columns is-multiline" v-for="(note, index) in call.Notes" :key="index">
      <div class="column is-12">
        <app-text v-model="note.vc255Note">
          Note
        </app-text>
      </div>
      <div class="column is-6 is-left is-bottom">
        <app-date-time v-model="note.dCreatedOn" :is-range="true" :now-timestamp="false">
          Created Date
        </app-date-time>
      </div>
      <div class="column is-6 is-bottom">
        <app-text v-model="note.sCreatedBy">
          Created By
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import BaseSection from './BaseSection.vue';

export default {
  name: 'notes-search',

  extends: BaseSection,

  mounted () {
    this.$set(this.call, 'Notes', [{
      vc255Note: '',
      sCreatedBy: '',
      dCreatedOn: ''
    }]);
  }
};
</script>
