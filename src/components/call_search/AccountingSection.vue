<template>
  <app-grid-form class="accounting-search" context="inline">
    <div class="columns is-multiline">
      <div class="column is-6">
        <app-text v-model="call.vc20InvoiceNum">
          Tow Ticket Number
        </app-text>
      </div>
      <div class="column is-6"></div>
      <div class="column is-6 is-left">
        <app-date-time v-model="call.dAcctReconcil" :is-range="true">
          Reconciled Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="call.sAcctReconcilBy">
          Reconciled By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="call.dRetowAcctReconcil" :is-range="true">
          Retow Reconciled Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="call.sRetowAcctReconcilBy">
          Retow Reconciled By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="call.dAccConfirm" :is-range="true">
          Confirmed Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="call.sAcctConfirmBy">
          Confirmed By
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <app-textarea v-model="call.vc255AccountingNotes" maxlength="255">
          Accounting Notes
        </app-textarea>
      </div>
      <div class="column is-6 is-left is-bottom">
        <app-date-time v-model="call.dCompletionDate" :is-range="true">
          Call Completed Date
        </app-date-time>
      </div>
      <div class="column is-6 is-bottom">
        <app-date-time v-model="call.dRevenueDate" :is-range="true">
          Call Revenue Date
        </app-date-time>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import BaseSection from './BaseSection.vue';

export default {
  name: 'accounting-search',

  extends: BaseSection,

  mounted () {
    this.$set(this.call, 'vc20InvoiceNum', '');
    this.$set(this.call, 'dAcctReconcil', '');
    this.$set(this.call, 'sAcctReconcilBy', '');
    this.$set(this.call, 'dRetowAcctReconcil', '');
    this.$set(this.call, 'sRetowAcctReconcilBy', '');
    this.$set(this.call, 'dAccConfirm', '');
    this.$set(this.call, 'sAcctConfirmBy', '');
    this.$set(this.call, 'vc255AccountingNotes', '');
    this.$set(this.call, 'dCompletionDate', '');
    this.$set(this.call, 'dRevenueDate', '');
  }
};
</script>
