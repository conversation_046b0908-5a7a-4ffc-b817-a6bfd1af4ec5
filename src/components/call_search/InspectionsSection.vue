<template>
  <app-grid-form class="inspection-search" context="inline">
    <div class="columns is-multiline" v-for="(item, index) in call.InspectionItems" :key="index">
      <div class="column is-6 is-left is-bottom">
        <app-select v-model="item.lItemKey" :options="itemPool" keyAlias="Key" valueAlias="Description">
          Item
        </app-select>
      </div>
      <div class="column is-6 is-bottom">
        <app-text v-model="item.vc255Value">
          Value
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'inspection-search',

  extends: BaseSection,

  data () {
    return {
      itemPool: []
    };
  },

  mounted () {
    this.$set(this.call, 'InspectionItems', [{
      vc255Value: '',
      lItemKey: '' // Ex: 1020
    }]);

    this.getInspectionItems();
  },

  methods: {
    ...mapActions(['TOPSCOMPANY__getCallInspectionItems']),

    getInspectionItems () {
      this.TOPSCOMPANY__getCallInspectionItems({
        callback: response => {
          this.$data.itemPool = response;
        }
      });
    }
  }
};
</script>
