<template>
  <app-grid-form class="dispatch-search" context="inline">
    <div class="columns is-multiline" v-for="(dispatch, index) in call.Dispatches" :key="index">
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dAssigned" :is-range="true">
          Assigned Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sAssignedBy">
          Assigned By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dDispatched" :is-range="true">
          Dispatched Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sDispatchedBy">
          Dispatched By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dAcknowledged" :is-range="true">
          Acknowledged Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sAcknowledgedBy">
          Acknowledged By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dArrived" :is-range="true">
          Arrived Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sArrivedBy">
          Arrived By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dHooked" :is-range="true">
          Hooked Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sHookedBy">
          Hooked By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dDropped" :is-range="true">
          Dropped Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sDroppedBy">
          Dropped By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="dispatch.dCompleted" :is-range="true">
          Completed Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sCompletedBy">
          Completed By
        </app-text>
      </div>
      <div class="column is-4 is-left">
        <app-select-boolean>
          Retow
        </app-select-boolean>
      </div>
      <div class="column is-4">
        <app-text v-model="dispatch.lTotalMileage">
          Total Mileage
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="dispatch.tcCommission">
          Commissionable Amount
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="dispatch.sDriver">
          Driver Number
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="dispatch.sTruck">
          Truck Number
        </app-text>
      </div>
      <div class="column is-4 is-left is-bottom">
        <app-select-search :options="statuses" valueAlias="Value">
          Status
        </app-select-search>
      </div>
      <div class="column is-4 is-bottom">
        <app-date-time v-model="dispatch.dLastStatusChange" :is-range="true">
          Status Changed Date
        </app-date-time>
      </div>
      <div class="column is-4 is-bottom">
        <app-text v-model="dispatch.vc50LoadNum">
          Load Number
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'dispatch-search',

  extends: BaseSection,

  data () {
    return {
      statuses: []
    };
  },

  mounted () {
    this.$set(this.call, 'Dispatches', [{
      dAssigned: '',
      sAssignedBy: '',
      dDispatched: '',
      sDispatchedBy: '',
      dAcknowledged: '',
      sAcknowledgedBy: '',
      dArrived: '',
      sArrivedBy: '',
      dHooked: '',
      sHookedBy: '',
      dDropped: '',
      sDroppedBy: '',
      dCompleted: '',
      sCompletedBy: '',
      lTotalMileage: '',
      tcCommission: '',
      sDriver: '',
      sTruck: '',
      dLastStatusChange: '',
      vc50LoadNum: ''
    }]);

    this.getStatuses();
  },

  methods: {
    ...mapActions([
      'DISPATCH__getStatuses'
    ]),

    getStatuses () {
      this.DISPATCH__getStatuses({
        callback: response => {
          this.$data.statuses = response;
        }
      });
    }
  }
};
</script>
