<template>
  <div id="lien-search">
    <app-grid-form context="inline">
      <h4 class="_section-title">Process</h4>
      <div class="columns is-multiline">
        <div class="column is-6">
          <app-select-search v-model="call.Lien.lLienProcessKey" :options="processes" keyAlias="Value" valueAlias="Value">
            Process
          </app-select-search>
        </div>
        <div class="column is-6">
          <app-select-search v-model="call.Lien.lLienStatusTypeKey" :options="statuses" keyAlias="Value" valueAlias="Value">
            Status
          </app-select-search>
        </div>
        <div class="column is-4 is-left">
          <app-date-time v-model="call.Lien.dDateOpened" :is-range="true">
            Opened
          </app-date-time>
        </div>
        <div class="column is-4">
          <app-date-time v-model="call.Lien.dDateClosed" :is-range="true">
            Closed
          </app-date-time>
        </div>
        <div class="column is-4">
          <app-select-boolean v-model="call.Lien.bDontSendToCollections">
            No Collections
          </app-select-boolean>
        </div>
        <div class="column is-4 is-left">
          <app-text v-model="call.Lien.vc50UserDefined1">
            {{ TOPSCOMPANY__settings.vc15Label_LienProcess_UserDef1 }}
          </app-text>
        </div>
        <div class="column is-4">
          <app-text v-model="call.Lien.vc50UserDefined2">
            {{ TOPSCOMPANY__settings.vc15Label_LienProcess_UserDef2 }}
          </app-text>
        </div>
        <div class="column is-4">
          <app-text v-model="call.Lien.tcLienPrice">
            Price
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-text v-model="call.Lien.vc255Notes" maxlength="255">
            Notes
          </app-text>
        </div>
      </div>
    </app-grid-form>

    <app-grid-form context="inline">
      <h4 class="_section-title">Step</h4>
      <div class="columns is-multiline">
        <div class="column is-4">
          <app-text v-model="call.Lien.iActivationQty">
            Quantity
          </app-text>
        </div>
        <div class="column is-4">
          <app-select-search v-model="call.Lien.lActivationUnitsTypeKey" :options="activationUnits" keyAlias="Value" valueAlias="Value">
            Units
          </app-select-search>
        </div>
        <div class="column is-4">
          <app-select-search v-model="call.Lien.lActivationBasisTypeKey" :options="activationBasises" keyAlias="Value" valueAlias="Value">
            Basis
          </app-select-search>
        </div>
      </div>
    </app-grid-form>

    <app-grid-form context="inline">
      <h4 class="_section-title">Owner</h4>
      <div class="columns is-multiline">
        <div class="column is-6">
          <app-select-search v-model="call.Lien.lOwnerTypeKey" :options="ownerTypes" keyAlias="Value" valueAlias="Value">
            Type
          </app-select-search>
        </div>
        <div class="column is-6">
          <app-text v-model="call.Lien.vc50Name" :maxlength="50">
            Name
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-text v-model="call.Lien.vc30Address1" :maxlength="30">
            Address 1
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-text v-model="call.Lien.vc30Address2" :maxlength="30">
            Address 2
          </app-text>
        </div>
        <div class="column is-4 is-left">
          <app-text v-model="call.Lien.vc30City" :maxlength="30">
            City
          </app-text>
        </div>
        <div class="column is-4">
          <app-select-state v-model="call.Lien.ch2StateKey">
            State
          </app-select-state>
        </div>
        <div class="column is-4">
          <app-text v-model="call.Lien.vc10ZipCode" :maxlength="10">
            Zip
          </app-text>
        </div>
      </div>
    </app-grid-form>

    <app-grid-form context="inline">
      <h4 class="_section-title">Letter</h4>
      <div class="columns is-multiline">
        <div class="column is-12">
          <app-text v-model="call.Lien.sOwner">
            Owner
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-select-search v-model="call.Lien.lLienTaskKey" :options="allTasks" keyAlias="Name" valueAlias="Name">
            Task Name
          </app-select-search>
        </div>
        <div class="column is-6">
          <app-date-time v-model="call.Lien.dDateSent" :is-range="true">
            Date Sent
          </app-date-time>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="call.Lien.vc20CertificationNum">
            Certification Number
          </app-text>
        </div>
        <div class="column is-6">
          <app-date-time v-model="call.Lien.dReturnReceiptDate" :is-range="true">
            Return Receipt Date
          </app-date-time>
        </div>
        <div class="column is-12 is-left">
          <app-text v-model="call.Lien.vc50Notes">
            Notes
          </app-text>
        </div>
      </div>
    </app-grid-form>
  </div>
</template>

<script>
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'lien-search',

  extends: BaseSection,

  data () {
    return {
      allTasks: [],
      statuses: [],
      processes: [],
      ownerTypes: [],
      activationUnits: [],
      activationBasises: []
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings'])
  },

  mounted () {
    this.$set(this.call, 'Lien', {
      lLienProcessKey: '',
      lLienStatusTypeKey: '',
      dDateOpened: '',
      dDateClosed: '',
      bDontSendToCollections: '',
      vc50UserDefined1: '',
      vc50UserDefined2: '',
      tcLienPrice: '',
      vc255Notes: '',
      iActivationQty: '',
      lActivationUnitsTypeKey: '',
      lActivationBasisTypeKey: '',
      lOwnerTypeKey: '',
      vc50Name: '',
      vc30Address1: '',
      vc30Address2: '',
      vc30City: '',
      ch2StateKey: '',
      vc10ZipCode: '',
      sOwner: '',
      lLienTaskKey: '',
      dDateSent: '',
      vc20CertificationNum: '',
      dReturnReceiptDate: '',
      vc50Notes: ''
    });

    this.getStatuses();
    this.getProcesses();
    this.getOwnerTypes();
    this.getActivationUnits();
    this.getActivationBasises();
  },

  methods: {
    ...mapActions([
      'LIEN__getStatuses',
      'LIEN__getProcesses',
      'LIENPROCESS__getAllTasks',
      'LIENSTEPTASK__getOwnerTypes',
      'LIENSTEP__getActivationUnits',
      'LIENSTEP__getActivationBasises'
    ]),

    getProcesses () {
      this.LIEN__getProcesses({
        callback: response => {
          this.$data.processes = response;
        }
      });
    },

    getStatuses () {
      this.LIEN__getStatuses({
        callback: response => {
          this.$data.statuses = response;
        }
      });
    },

    getActivationUnits () {
      this.LIENSTEP__getActivationUnits({
        callback: response => {
          this.$data.activationUnits = response;
        }
      });
    },

    getActivationBasises () {
      this.LIENSTEP__getActivationBasises({
        callback: response => {
          this.$data.activationBasises = response;
        }
      });
    },

    getOwnerTypes () {
      this.LIENSTEPTASK__getOwnerTypes({
        callback: response => {
          this.$data.ownerTypes = response;
        }
      });
    },

    getAllTasks () {
      this.LIENPROCESS__getAllTasks({
        callback: response => {
          this.$data.allTasks = response;
        }
      });
    }
  }
};
</script>
