<template>
  <app-grid-form class="holds-search" context="inline">
    <div class="columns is-multiline" v-for="(hold, index) in call.Holds" :key="index">
      <div class="column is-4">
        <app-checkbox v-model="hold.bHoldOn">
          Hold On
        </app-checkbox>
      </div>
      <div class="column is-4">
        <app-date-time v-model="hold.dHoldUntil" :is-range="true">
          Hold Until
        </app-date-time>
      </div>
      <div class="column is-4">
        <app-shortcode v-model="hold.lHoldReasonTypeKey" :options="reasons" context="search" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
          Reason
        </app-shortcode>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="hold.vc255Notes" maxlength="255">
          Notes
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="hold.dAuthorizedOn" :is-range="true">
          Authorized Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="hold.vc50AuthorizedBy" maxlength="255">
          Authorized By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="hold.dReleased" :is-range="true">
          Released Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="hold.sReleasedBy">
          Released By
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="hold.dCreated" :is-range="true">
          Created Date
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-text v-model="hold.sCreatedBy">
          Created By
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="hold.vc255OtherInfo" maxlength="255">
          Other Hold Info
        </app-text>
      </div>
      <div class="column is-12 is-left is-bottom">
        <app-text v-model="hold.vc50UserDefined1" maxlength="255">
          {{ TOPSCOMPANY__settings.vc15Label_Hold_UserDef1 }}
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'holds-search',

  extends: BaseSection,

  data () {
    return {
      reasons: [],
      statuses: [
        { key: 1, value: 'True' },
        { key: 0, value: 'False' }
      ]
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings'])
  },

  methods: {
    ...mapActions(['HOLD__getReasons'])
  },

  mounted () {
    this.$set(this.call, 'Holds', [{
      bHoldOn: '',
      dHoldUntil: '',
      lHoldReasonTypeKey: '',
      vc255Notes: '',
      dAuthorizedOn: '',
      vc50AuthorizedBy: '',
      dReleased: '',
      sReleasedBy: '',
      dCreated: '',
      sCreatedBy: '',
      vc255OtherInfo: '',
      vc50UserDefined1: ''
    }]);

    this.HOLD__getReasons({
      callback: response => {
        this.$data.reasons = response;
      }
    });
  }
};
</script>
