<template>
<span :id="sectionName">
  <app-grid-form class="_editor" context="inline">
    <div class="columns is-multiline">
      <div class="column is-4">
        <app-text v-model="call.fMileageUnloaded">
          Unloaded
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.fMileageLoaded">
          Loaded
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.fMileageReturn">
          Return
        </app-text>
      </div>
    </div>
  </app-grid-form>
</span>
</template>

<script>
import BaseSection from './BaseSection.vue';
import { CALL_SECTION_MILEAGE } from '@/config.js';

export default {
  name: CALL_SECTION_MILEAGE,

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_MILEAGE
    };
  },

  mounted () {
    this.createIfMissing('fMileageUnloaded', '');
    this.createIfMissing('fMileageLoaded', '');
    this.createIfMissing('fMileageReturn', '');
  }
};
</script>
