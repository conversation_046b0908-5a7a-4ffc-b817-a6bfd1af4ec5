<template>
  <app-grid-form class="inventory-search" context="inline">
    <div class="columns is-multiline">
      <div class="column is-6">
        <app-select v-model="call.Inventory.lStorageLotKey" :options="lots" context="search" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
          Lot
        </app-select>
      </div>
      <div class="column is-6">
        <app-select v-model="call.lFinalDispositionTypeKey" :options="dispositions" context="search" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
          Release
        </app-select>
      </div>
      <div class="column is-12 is-left">
        <app-date-time v-model="call.Inventory.dDateIn" :is-range="true" :now-timestamp="false">
          Date In
        </app-date-time>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="call.Inventory.vc15LotLocation" maxlength="15">
          Location in Lot
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <app-date-time v-model="call.Inventory.dPlannedAuction" :is-range="true" :now-timestamp="false">
          Planned Auction
        </app-date-time>
      </div>
      <div class="column is-12 is-left">
        <app-date-time v-model="call.Inventory.dStopBillingDate" :is-range="true" :now-timestamp="false">
          Stop Billing Date
        </app-date-time>
      </div>
      <div class="column is-12 is-left">
        <app-date-time v-model="call.Inventory.dDateOut" :is-range="true" :now-timestamp="false">
          Date Out
        </app-date-time>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="call.Inventory.sDateOutBy">
          Date Out By
        </app-text>
      </div>
      <div class="column is-4 is-left">
        <app-text v-model="call.Inventory.vc100UserDefined1" maxlength="100">
          {{ TOPSCOMPANY__settings.vc15Label_Inventory_UserDef1 }}
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.Inventory.lBarCode">
          Bar Code
        </app-text>
      </div>
      <div class="column is-4">
        <app-date-time v-model="call.Inventory.dLastVerified" :is-range="true" :now-timestamp="false">
          Last Verified
        </app-date-time>
      </div>
      <div class="column is-12 is-left is-bottom">
        <app-textarea v-model="call.Inventory.vc255Notes" maxlength="255">
          Notes
        </app-textarea>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'inventory-search',

  extends: BaseSection,

  data () {
    return {
      lots: [],
      dispositions: []
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings'])
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getLots',
      'CALL__getFinalDispositions'
    ])
  },

  mounted () {
    this.$set(this.call, 'Inventory', {
      lStorageLotKey: '',
      dDateIn: '',
      vc15LotLocation: '',
      dPlannedAuction: '',
      dStopBillingDate: '',
      dDateOut: '',
      sDateOutBy: '',
      vc100UserDefined1: '',
      lBarCode: '',
      vc255Notes: '',
      dLastVerified: ''
    });

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.$data.lots = response;
      }
    });

    this.CALL__getFinalDispositions({
      callback: response => {
        this.$data.dispositions = response;
      }
    });
  }
};
</script>
