<template>
  <app-grid-form class="miscellaneous-search" context="inline">
    <div class="columns is-multiline">
      <div class="column is-6 is-left">
        <app-text v-model="call.tPriority" id="CAL_tPriority" maxlength="1">
          Priority
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="call.ch5Zone" id="CAL_ch5Zone" maxlength="5">
          Zone
        </app-text>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="call.vc20PONum" id="CAL_vc20PONum" maxlength="20">
          P.O. Number
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="call.vc20RONum" id="CAL_vc20RONum" maxlength="20">
          R.O. Number
        </app-text>
      </div>
      <div class="column is-4 is-left">
        <app-text v-model="call.vc100UserDefined1" id="CAL_vc100UserDefined1" maxlength="100">
          {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef1 }}
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.vc20MembershipNum" id="CAL_vc20MembershipNum" maxlength="20">
          Membership Number
        </app-text>
      </div>
      <div class="column is-4">
        <app-date-time v-model="call.dExpirationDate" id="CAL_dExpirationDate" :is-range="true" :now-timestamp="false">
          Membership Expiration
        </app-date-time>
      </div>
      <div class="column is-6 is-left">
        <app-text v-model="call.vc50UserDefined2" id="CAL_vc50UserDefined2" maxlength="50">
          {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef2 }}
        </app-text>
      </div>
      <div class="column is-6">
        <app-text v-model="call.vc50UserDefined3" id="CAL_vc50UserDefined3" maxlength="50">
          {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef3 }}
        </app-text>
      </div>
      <div class="column is-4 is-left is-bottom">
        <app-select-boolean v-model="call.bMileageRequired" id="CAL_bMileageRequired">
          Mileage Required
        </app-select-boolean>
      </div>
      <div class="column is-4 is-bottom">
        <app-text v-model="call.vc20PoliceNum" id="CAL_vc20PoliceNum" maxlength="20">
          Police Number
        </app-text>
      </div>
      <div class="column is-4 is-bottom">
        <app-text v-model="call.vc10PoliceBeat" id="CAL_vc10PoliceBeat" maxlength="10">
          Police Beat
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapGetters } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'miscellaneous-search',

  extends: BaseSection,

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings'])
  }
};
</script>
