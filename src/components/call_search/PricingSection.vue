<template>
  <app-grid-form class="pricing-search" context="inline">
    <div class="columns is-multiline" v-for="(price, index) in call.TowOrderLines" :key="index">
      <div class="column is-3">
        <app-text v-model="price.tcTotalPrice">
          Price
        </app-text>
      </div>
      <div class="column is-3">
        <app-text v-model="price.pQty">
          Quantity
        </app-text>
      </div>
      <div class="column is-3">
        <app-select-search v-model="price.lServicePricingTypeKey" :options="pricingTypes" valueAlias="Value">
          Pricing Type
        </app-select-search>
      </div>
      <div class="column is-3">
        <app-select-search v-model="price.lServiceKey" :options="services" valueAlias="Value">
          Service
        </app-select-search>
      </div>
      <div class="column is-12 is-left is-bottom">
        <app-text v-model="price.vc100Description">
          Description
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'pricing-search',

  extends: BaseSection,

  data () {
    return {
      services: [],
      pricingTypes: []
    };
  },

  mounted () {
    this.$set(this.call, 'TowOrderLines', [{
      tcTotalPrice: '',
      pQty: '',
      lServicePricingTypeKey: '',
      lServiceKey: '',
      vc100Description: ''
    }]);

    this.getServices();
    this.getPricingTypes();
  },

  methods: {
    ...mapActions([
      'ORDERLINE__getServices',
      'ORDERLINE__getPricingTypes'
    ]),

    getServices () {
      this.ORDERLINE__getServices({
        callback: response => {
          this.$data.services = response;
        }
      });
    },

    getPricingTypes () {
      this.ORDERLINE__getPricingTypes({
        callback: response => {
          this.$data.pricingTypes = response;
        }
      });
    }
  }
};
</script>
