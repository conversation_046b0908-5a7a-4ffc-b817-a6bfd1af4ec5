<template>
  <app-grid-form class="subterminal-search" context="inline">
    <div class="columns">
      <div class="column is-bottom">
        <app-select-search v-model="call.lSubterminalKey" :options="subterminals" keyAlias="Key" valueAlias="Value">
          Company
        </app-select-search>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'subterminal-search',

  extends: BaseSection,

  data () {
    return {
      subterminals: []
    };
  },

  mounted () {
    this.getSubterminals();
  },

  methods: {
    ...mapActions(['CALL__getSubterminals']),

    getSubterminals () {
      this.CALL__getSubterminals({
        callback: response => {
          this.$data.subterminals = response;
        }
      });
    }
  }
};
</script>
