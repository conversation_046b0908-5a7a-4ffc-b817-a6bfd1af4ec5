<template>
  <app-grid-form class="payment-search" context="inline">
    <section v-for="(payment, index) in call.TowPayments" :key="index">
      <div class="columns is-multiline">
        <div class="column is-4 is-left">
          <app-select-search v-model="payment.sPaymentType" :options="paymentTypes" valueAlias="Value">
            Type
          </app-select-search>
        </div>
        <div class="column is-4">
          <app-text v-model="payment.tcAmount">
            Amount
          </app-text>
        </div>
        <div class="column is-4">
          <app-date-time v-model="payment.dReceived" :is-range="true" :now-timestamp="false">
            Received At
          </app-date-time>
        </div>
        <div class="column is-4 is-left">
          <InputCardType v-model="payment.lCreditCardTypeKey" context="search" />
        </div>
        <div class="column is-8">
          <app-text v-model="payment.vc20PaymentInfo">
            Check / Card Number
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-text v-model="payment.sCustomer">
            Customer
          </app-text>
        </div>
        <div class="column is-8 is-left">
          <app-text v-model="payment.vc30CardholderName">
            Cardholder Name
          </app-text>
        </div>
        <div class="column is-4">
          <app-date-time v-model="payment.ch4ExpiryDate" :is-range="true" :now-timestamp="false">
            Expiry
          </app-date-time>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="payment.vc20AuthorizationInfo">
            Authorization Number
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="payment.vc20TransactionTag">
            Transaction Number
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-select-search v-model="payment.sReceiptType" :options="receiptTypes" valueAlias="Value">
            Receipt Type
          </app-select-search>
        </div>
        <div class="column is-6">
          <app-text v-model="payment.sReceivedBy">
            Received By
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="payment.dReconciled" :is-range="true" :now-timestamp="false">
            Reconciled At
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="payment.sReconciledBy">
            Reconciled By
          </app-text>
        </div>
        <div class="column is-12 is-left is-bottom">
          <app-text v-model="payment.vc255Notes">
            Notes
          </app-text>
        </div>
      </div>
    </section>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import { VALUE_ID } from '@/config.js';
import BaseSection from './BaseSection.vue';

export default {
  name: 'payment-search',

  extends: BaseSection,

  data () {
    return {
      receiptTypes: [],
      paymentTypes: [],
      cardStatusTypes: [],
      processingTypes: []
    };
  },

  mounted () {
    this.$set(this.call, 'TowPayments', [{
      tcAmount: '',
      vc20AuthorizationInfo: '',
      vc30CardholderName: '',
      lCreditCardTypeKey: '',
      dReceived: '',
      sReceivedBy: '',
      dReconciled: '',
      sReconciledBy: '',
      sCustomer: '',
      vc20PaymentInfo: '',
      sPaymentType: '',
      sReceiptType: '',
      ch4ExpiryDate: '',
      vc20SequenceNum: '',
      vc20TransactionTag: '',
      vc255Notes: ''
    }]);

    this.getReceiptTypes();
    this.getPaymentTypes();
    this.getCardStatusTypes();
    this.getProcessingTypes();
  },

  methods: {
    ...mapActions([
      'PAYMENT__getPaymentTypes',
      'PAYMENT__getReceiptTypes',
      'PAYMENT__getCardStatusTypes',
      'PAYMENT__getProcessingTypes'
    ]),

    getProcessingTypes () {
      this.PAYMENT__getProcessingTypes({
        callback: response => {
          this.$data.processingTypes = response;
        }
      });
    },

    getReceiptTypes () {
      this.PAYMENT__getReceiptTypes({
        callback: response => {
          this.$data.receiptTypes = response;
        }
      });
    },

    getPaymentTypes () {
      this.PAYMENT__getPaymentTypes({
        callback: response => {
          this.$data.paymentTypes = this.$_.reject(response, ['Key', VALUE_ID.paymentType.arReceivable]);
        }
      });
    },

    getCardStatusTypes () {
      this.PAYMENT__getCardStatusTypes({
        callback: response => {
          this.$data.cardStatusTypes = response;
        }
      });
    }
  }
};
</script>
