import StandardLayout from '@/layouts/Standard.vue';
import Welcome from '@/components/Welcome.vue';
import SignIn from '@/components/SignIn.vue';

const rootRoutes = {
  path: '/',
  components: { default: StandardLayout },
  children: [
    { path: '', name: 'Default', component: Welcome },
    { path: 'sign-in', label: 'Sign In', name: 'SignIn', component: SignIn },
    { path: 'welcome', label: 'Welcome', name: 'Welcome', component: Welcome },
    { path: 'profile', label: 'Profile', name: 'Profile', component: () => import('@/components/Profile.vue') },
    { path: 'grid/:key/columns', label: 'Columns', name: 'GridColumns', component: () => import('@/components/features/Columns.vue') },
    { path: 'grid/:key/filters', label: 'Grid Search', name: 'GridSearch', component: () => import('@/components/tower/Search.vue') },
    { path: 'notify', label: 'Notify', name: 'Notify', component: () => import('@/components/tower/Notify.vue') },
    { path: 'playground', label: 'Playground', name: 'Playground', component: () => import('@/components/Playground.vue') },
    { path: 'composables-demo', label: 'Composables Demo', name: 'ComposablesDemo', component: () => import('@/components/examples/ComposablesDemo.vue') },
  ]
};

export default rootRoutes;
