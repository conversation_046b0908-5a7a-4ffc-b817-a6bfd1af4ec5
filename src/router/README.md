# Router Structure

This directory contains the Vue Router configuration for the application.

## Structure

- `index.js`: Main router file that imports and combines all route groups
- `routes/`: Directory containing all route group files
  - `root.js`: Routes for the root path ('/')
  - `manager.js`: Routes for the manager section ('/manager')
  - `administration.js`: Routes for the administration section ('/administration')
  - `tower.js`: Routes for the tower section ('/tower')
  - `notFound.js`: 404 route for handling non-existent routes

## Adding New Routes

To add a new route:

1. Identify which route group the new route belongs to
2. Add the route to the appropriate file in the `routes/` directory
3. Follow the existing pattern for route configuration

## Creating a New Route Group

To create a new top-level route group:

1. Create a new file in the `routes/` directory (e.g., `newGroup.js`)
2. Define the route group with its path and children
3. Export the route group
4. Import the new route group in `index.js` and add it to the routes array
